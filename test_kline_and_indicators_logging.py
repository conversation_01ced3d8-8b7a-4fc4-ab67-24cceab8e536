#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 K线数据获取和技术指标计算日志测试脚本
验证系统是否正确输出K线数据获取和技术指标计算的日志信息
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_kline_and_indicators_logging():
    """测试K线数据获取和技术指标计算的日志输出"""
    print("=" * 60)
    print("🔧 K线数据获取和技术指标计算日志测试")
    print("=" * 60)
    
    try:
        print("📦 正在导入WMZC模块...")
        import WMZC
        print("✅ WMZC模块导入成功！")
        
        # 检查关键组件是否存在
        print("\n🔍 检查关键组件...")
        
        # 检查统一交易所管理器
        if hasattr(WMZC, 'unified_exchange') and WMZC.unified_exchange:
            print("✅ 统一交易所管理器存在")
            
            # 测试K线数据获取
            print("\n📊 测试K线数据获取...")
            symbol = 'BTC-USDT-SWAP'
            timeframe = '1m'
            limit = 50
            
            print(f"[测试] 📊 开始获取K线数据: {symbol} {timeframe} (limit={limit})")
            
            try:
                kline_data = WMZC.unified_exchange.get_kline_data(symbol, timeframe, limit)
                
                if kline_data is not None and not kline_data.empty:
                    print(f"[测试] ✅ K线数据获取成功: {len(kline_data)}条数据")
                    
                    # 测试技术指标计算
                    print("\n🔧 测试技术指标计算...")
                    
                    # 测试MACD计算
                    print("[测试] 📈 正在计算MACD指标...")
                    try:
                        macd_result = WMZC.calculate_macd(kline_data.copy())
                        if macd_result is not None and not macd_result.empty:
                            print(f"[测试] ✅ MACD计算成功: {len(macd_result)}条数据")
                            if 'macd' in macd_result.columns:
                                latest_macd = macd_result['macd'].iloc[-1]
                                print(f"[测试] 📊 最新MACD值: {latest_macd:.6f}")
                        else:
                            print("[测试] ❌ MACD计算失败")
                    except Exception as e:
                        print(f"[测试] ❌ MACD计算异常: {e}")
                    
                    # 测试RSI计算
                    print("[测试] 📉 正在计算RSI指标...")
                    try:
                        rsi_result = WMZC.calculate_rsi(kline_data.copy())
                        if rsi_result is not None and not rsi_result.empty:
                            print(f"[测试] ✅ RSI计算成功: {len(rsi_result)}条数据")
                            if 'rsi' in rsi_result.columns:
                                latest_rsi = rsi_result['rsi'].iloc[-1]
                                print(f"[测试] 📊 最新RSI值: {latest_rsi:.2f}")
                        else:
                            print("[测试] ❌ RSI计算失败")
                    except Exception as e:
                        print(f"[测试] ❌ RSI计算异常: {e}")
                    
                    # 测试KDJ计算
                    print("[测试] 📊 正在计算KDJ指标...")
                    try:
                        kdj_result = WMZC.calculate_kdj(kline_data.copy())
                        if kdj_result is not None and not kdj_result.empty:
                            print(f"[测试] ✅ KDJ计算成功: {len(kdj_result)}条数据")
                            if all(col in kdj_result.columns for col in ['k', 'd', 'j']):
                                latest_k = kdj_result['k'].iloc[-1]
                                latest_d = kdj_result['d'].iloc[-1]
                                latest_j = kdj_result['j'].iloc[-1]
                                print(f"[测试] 📊 最新KDJ值: K={latest_k:.2f}, D={latest_d:.2f}, J={latest_j:.2f}")
                        else:
                            print("[测试] ❌ KDJ计算失败")
                    except Exception as e:
                        print(f"[测试] ❌ KDJ计算异常: {e}")
                    
                else:
                    print("[测试] ❌ K线数据获取失败或为空")
                    
            except Exception as e:
                print(f"[测试] ❌ K线数据获取异常: {e}")
        else:
            print("❌ 统一交易所管理器不存在")
        
        # 检查日志系统
        print("\n📝 检查日志系统...")
        
        if hasattr(WMZC, 'log'):
            print("✅ 日志函数存在")
            # 测试日志输出
            WMZC.log("🧪 这是一条测试日志消息", "INFO")
            print("[测试] 📝 测试日志已发送")
        else:
            print("❌ 日志函数不存在")
        
        # 检查配置
        print("\n⚙️ 检查配置...")
        if hasattr(WMZC, 'config'):
            log_level = WMZC.config.get('LOG_LEVEL', 'INFO')
            print(f"✅ 当前日志级别: {log_level}")
            
            enable_macd = WMZC.config.get('ENABLE_MACD', True)
            enable_rsi = WMZC.config.get('ENABLE_RSI', True)
            enable_kdj = WMZC.config.get('ENABLE_KDJ', True)
            
            print(f"✅ MACD启用状态: {enable_macd}")
            print(f"✅ RSI启用状态: {enable_rsi}")
            print(f"✅ KDJ启用状态: {enable_kdj}")
        else:
            print("❌ 配置对象不存在")
        
        print("\n" + "=" * 60)
        print("🎉 K线数据获取和技术指标计算日志测试完成！")
        print("=" * 60)
        
        print("\n💡 如果您在WMZC系统的日志控制台中没有看到以下信息：")
        print("   - K线数据获取的开始和完成日志")
        print("   - 技术指标计算的进度和结果日志")
        print("   请检查：")
        print("   1. 日志级别设置是否为INFO或更低")
        print("   2. 交易循环是否已启动")
        print("   3. API配置是否正确")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入WMZC模块失败: {e}")
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 启动K线数据获取和技术指标计算日志测试...")
    
    success = test_kline_and_indicators_logging()
    
    if success:
        print("\n✅ 测试完成！")
        print("💡 现在您可以启动WMZC交易系统，观察日志控制台中的输出。")
    else:
        print("\n❌ 测试失败！")
        print("💡 请检查WMZC系统的配置和依赖。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
