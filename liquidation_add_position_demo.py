#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚨 预估强平价加仓功能演示
展示新增的预估强平价加仓功能的完整工作流程
"""

import sys
import os
import time
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_liquidation_add_position_feature():
    """演示预估强平价加仓功能"""
    print("=" * 80)
    print("🚨 WMZC预估强平价加仓功能演示")
    print("=" * 80)
    print(f"⏰ 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        import WMZC
        
        print("📦 正在加载WMZC系统...")
        
        # 创建EqualPositionStrategy实例进行演示
        strategy = WMZC.EqualPositionStrategy()
        
        print("✅ WMZC系统加载完成")
        print()
        
        # === 功能特性展示 ===
        print("🎯 功能特性展示:")
        print("=" * 50)
        
        # 1. 配置展示
        print("1️⃣ 预估强平价加仓配置:")
        print(f"   ├── 启用状态: {strategy.config['liquidation_add_position_enabled']}")
        print(f"   ├── 触发距离: {strategy.config['liquidation_trigger_distance']} 个点")
        print(f"   └── 加仓量: {strategy.config['liquidation_add_amount']} (half=半量, equal=等量)")
        print()
        
        # 2. 启用功能
        print("2️⃣ 启用预估强平价加仓功能:")
        strategy.config['liquidation_add_position_enabled'] = True
        strategy.config['liquidation_trigger_distance'] = 10  # 设置为10个点
        strategy.config['liquidation_add_amount'] = 'half'    # 设置为半量加仓
        print("   ✅ 功能已启用")
        print(f"   ✅ 触发距离设置为: {strategy.config['liquidation_trigger_distance']} 个点")
        print(f"   ✅ 加仓量设置为: {strategy.config['liquidation_add_amount']}")
        print()
        
        # 3. 模拟持仓信息
        print("3️⃣ 模拟持仓信息:")
        symbol = "BTC-USDT-SWAP"
        
        # 先创建一个模拟持仓
        strategy.positions['test_position'] = {
            'id': 'test_position',
            'symbol': symbol,
            'entry_price': 50000.0,
            'amount': 0.001,
            'investment': 50.0,
            'status': 'active',
            'type': 'normal',
            'timestamp': time.time()
        }
        
        position_info = strategy.get_position_info(symbol)
        if position_info:
            print(f"   ├── 交易对: {symbol}")
            print(f"   ├── 持仓数量: {position_info['size']:.6f} BTC")
            print(f"   ├── 持仓价值: {position_info['value']:.2f} USDT")
            print(f"   ├── 保证金: {position_info['margin']:.2f} USDT")
            print(f"   └── 预估强平价: {position_info['liquidation_price']:.2f} USDT")
        print()
        
        # 4. 风险评估
        print("4️⃣ 风险评估:")
        risk_info = strategy.check_liquidation_risk(symbol)
        print(f"   ├── 风险等级: {risk_info['risk_level']}")
        print(f"   ├── 风险描述: {risk_info['message']}")
        print(f"   ├── 距离强平价: {risk_info['distance']:.2f} USDT")
        print(f"   └── 距离百分比: {risk_info['distance_pct']:.1f}%")
        print()
        
        # 5. 模拟触发加仓
        print("5️⃣ 模拟触发加仓:")
        print("   📊 假设价格接近强平价，触发加仓条件...")
        
        # 执行紧急加仓
        success = strategy.emergency_add_position_by_margin(symbol, 0.5, "演示触发")
        
        if success:
            print("   ✅ 加仓执行成功！")
            print("   📈 已按半量加仓策略执行")
            
            # 显示加仓后的持仓信息
            updated_position_info = strategy.get_position_info(symbol)
            if updated_position_info:
                print(f"   📊 加仓后持仓数量: {updated_position_info['size']:.6f} BTC")
                print(f"   📊 加仓后持仓价值: {updated_position_info['value']:.2f} USDT")
        else:
            print("   ❌ 加仓执行失败")
        print()
        
        # 6. 历史记录
        print("6️⃣ 加仓历史记录:")
        if strategy.records:
            for i, record in enumerate(strategy.records[-3:], 1):  # 显示最近3条记录
                print(f"   {i}. {record['time']} - {record['strategy']}")
                print(f"      价格: {record['add_price']} USDT, 状态: {record['status']}")
        else:
            print("   📝 暂无历史记录")
        print()
        
        # === 使用指南 ===
        print("📋 使用指南:")
        print("=" * 50)
        print("1. 在等量加仓标签页中找到'🚨 预估强平价加仓'配置区域")
        print("2. 勾选'启用预估强平价加仓'复选框")
        print("3. 设置合适的'触发距离'（建议8-15个点）")
        print("4. 选择加仓量：半量加仓(50%)或等量加仓(100%)")
        print("5. 系统将自动监控价格，当接近强平价时自动执行加仓")
        print()
        
        # === 风险提示 ===
        print("⚠️ 重要风险提示:")
        print("=" * 50)
        print("🔴 此功能在价格接近强平价时自动加仓，可能进一步增加风险")
        print("🔴 建议设置合理的触发距离，避免过于激进的加仓")
        print("🔴 确保账户有足够资金支持加仓操作")
        print("🔴 强烈建议在模拟环境中充分测试后再使用")
        print("🔴 请根据市场情况和个人风险承受能力谨慎使用")
        print()
        
        # === 技术实现总结 ===
        print("🔧 技术实现总结:")
        print("=" * 50)
        print("✅ GUI配置界面 - 用户友好的配置选项")
        print("✅ 实时价格监控 - 每5秒检查一次价格变化")
        print("✅ 智能触发机制 - 基于距离强平价的点数触发")
        print("✅ 风险控制集成 - 与现有GlobalPositionController兼容")
        print("✅ 配置持久化 - 设置自动保存到配置文件")
        print("✅ 完整日志记录 - 所有操作都有详细日志")
        print("✅ 历史记录管理 - 加仓历史可查询和管理")
        print()
        
        print("🎉 预估强平价加仓功能演示完成！")
        print("💡 您现在可以在WMZC系统的等量加仓标签页中使用此功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_feature_comparison():
    """显示功能对比"""
    print("\n" + "=" * 80)
    print("📊 功能对比：传统加仓 vs 预估强平价加仓")
    print("=" * 80)
    
    comparison_data = [
        ("触发条件", "手动判断或技术指标", "自动监控强平价距离"),
        ("触发时机", "依赖人工判断", "价格接近强平价时自动触发"),
        ("风险控制", "依赖用户经验", "基于量化指标自动执行"),
        ("响应速度", "人工操作，可能延迟", "系统自动，响应迅速"),
        ("适用场景", "趋势交易", "风险管理和仓位救援"),
        ("操作复杂度", "需要持续监控", "一次配置，自动执行"),
        ("风险等级", "中等", "较高（需谨慎使用）")
    ]
    
    print(f"{'特性':<12} {'传统加仓':<20} {'预估强平价加仓':<25}")
    print("-" * 80)
    
    for feature, traditional, liquidation in comparison_data:
        print(f"{feature:<12} {traditional:<20} {liquidation:<25}")
    
    print("\n💡 建议：两种加仓方式可以结合使用，在不同市场条件下发挥各自优势")

if __name__ == "__main__":
    # 运行演示
    success = demo_liquidation_add_position_feature()
    
    if success:
        show_feature_comparison()
        print("\n🎯 下一步：启动WMZC系统，在等量加仓标签页中体验新功能！")
    else:
        print("\n❌ 演示失败，请检查系统配置")
    
    print("\n" + "=" * 80)
