#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 技术指标计算错误修复补丁
修复DataFrame布尔值判断错误
"""

import pandas as pd
import numpy as np

def safe_dataframe_check(df, condition_func):
    """安全的DataFrame条件检查"""
    try:
        if isinstance(df, pd.DataFrame):
            if df.empty:
                return False
            # 使用.iloc[-1]获取最后一个值进行判断
            return condition_func(df.iloc[-1])
        elif isinstance(df, pd.Series):
            if df.empty:
                return False
            return condition_func(df.iloc[-1])
        else:
            return condition_func(df)
    except Exception as e:
        print(f"⚠️ DataFrame条件检查失败: {e}")
        return False

def safe_macd_signal_check(macd_df):
    """安全的MACD信号检查"""
    try:
        if isinstance(macd_df, pd.DataFrame) and not macd_df.empty:
            # 获取最新的MACD值
            latest_macd = macd_df.iloc[-1]
            if isinstance(latest_macd, pd.Series):
                macd_value = latest_macd.get('MACD', 0)
            else:
                macd_value = latest_macd
            
            # 简单的信号判断
            if macd_value > 0:
                return "BUY"
            elif macd_value < 0:
                return "SELL"
            else:
                return "HOLD"
        return "HOLD"
    except Exception as e:
        print(f"⚠️ MACD信号检查失败: {e}")
        return "HOLD"

def safe_kdj_signal_check(kdj_df):
    """安全的KDJ信号检查"""
    try:
        if isinstance(kdj_df, pd.DataFrame) and not kdj_df.empty:
            latest_kdj = kdj_df.iloc[-1]
            k_value = latest_kdj.get('K', 50) if isinstance(latest_kdj, pd.Series) else latest_kdj
            
            if k_value < 20:
                return "BUY"
            elif k_value > 80:
                return "SELL"
            else:
                return "HOLD"
        return "HOLD"
    except Exception as e:
        print(f"⚠️ KDJ信号检查失败: {e}")
        return "HOLD"

def safe_rsi_signal_check(rsi_df):
    """安全的RSI信号检查"""
    try:
        if isinstance(rsi_df, pd.DataFrame) and not rsi_df.empty:
            latest_rsi = rsi_df.iloc[-1]
            rsi_value = latest_rsi.get('RSI', 50) if isinstance(latest_rsi, pd.Series) else latest_rsi
            
            if rsi_value < 30:
                return "BUY"
            elif rsi_value > 70:
                return "SELL"
            else:
                return "HOLD"
        return "HOLD"
    except Exception as e:
        print(f"⚠️ RSI信号检查失败: {e}")
        return "HOLD"

# 导出修复函数
__all__ = [
    'safe_dataframe_check',
    'safe_macd_signal_check', 
    'safe_kdj_signal_check',
    'safe_rsi_signal_check'
]
