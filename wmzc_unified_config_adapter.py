#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC统一配置适配器
为WMZC系统提供统一配置文件支持
"""

import json
import os
from datetime import datetime

class WMZCUnifiedConfigAdapter:
    """WMZC统一配置适配器"""
    
    def __init__(self):
        self.unified_config_file = 'wmzc_unified_config.json'
        self.config = None
        self.load_unified_config()
    
    def load_unified_config(self):
        """加载统一配置"""
        try:
            if os.path.exists(self.unified_config_file):
                with open(self.unified_config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                print(f"✅ 统一配置已加载: {len(self.config)} 个分组")
            else:
                print(f"❌ 统一配置文件不存在: {self.unified_config_file}")
                self.config = {}
        except Exception as e:
            print(f"❌ 加载统一配置失败: {e}")
            self.config = {}
    
    def get_legacy_config(self):
        """获取兼容旧版本的配置格式"""
        if not self.config:
            return {}
        
        # 转换为旧版本格式，保持兼容性
        legacy_config = {
            # API配置
            'API_KEY': self.config.get('api', {}).get('okx', {}).get('api_key', ''),
            'API_SECRET': self.config.get('api', {}).get('okx', {}).get('api_secret', ''),
            'PASSPHRASE': self.config.get('api', {}).get('okx', {}).get('passphrase', ''),
            'OKX_API_KEY': self.config.get('api', {}).get('okx', {}).get('api_key', ''),
            'OKX_SECRET_KEY': self.config.get('api', {}).get('okx', {}).get('api_secret', ''),
            'OKX_PASSPHRASE': self.config.get('api', {}).get('okx', {}).get('passphrase', ''),
            
            # 交易配置
            'EXCHANGE': self.config.get('trading', {}).get('exchange', 'OKX'),
            'SYMBOL': self.config.get('trading', {}).get('symbol', 'BTC-USDT-SWAP'),
            'TIMEFRAME': self.config.get('trading', {}).get('timeframe', '1m'),
            'ORDER_USDT_AMOUNT': self.config.get('trading', {}).get('order_amount', 10.0),
            'LEVERAGE': self.config.get('trading', {}).get('leverage', 3),
            'ENABLE_TRADING': self.config.get('trading', {}).get('enable_trading', False),
            
            # 风险管理
            'RISK_PERCENT': self.config.get('risk', {}).get('risk_percent', 1.0),
            'RISK_PER_TRADE': self.config.get('risk', {}).get('risk_per_trade', 1.0),
            'STOP_LOSS': self.config.get('risk', {}).get('stop_loss', 2.0),
            'TAKE_PROFIT': self.config.get('risk', {}).get('take_profit', 4.0),
            'TAKE_PROFIT_PCT': self.config.get('risk', {}).get('take_profit_pct', 2.0),
            
            # 策略配置
            'ENABLE_KDJ': self.config.get('strategies', {}).get('enable_kdj', True),
            'ENABLE_MACD': self.config.get('strategies', {}).get('enable_macd', True),
            'ENABLE_RSI': self.config.get('strategies', {}).get('enable_rsi', True),
            'ENABLE_PINBAR': self.config.get('strategies', {}).get('enable_pinbar', True),
            
            # 技术指标参数
            'KDJ_PERIOD': self.config.get('strategies', {}).get('kdj_period', 9),
            'MACD_FAST': self.config.get('strategies', {}).get('macd_fast', 12),
            'MACD_SLOW': self.config.get('strategies', {}).get('macd_slow', 26),
            'MACD_SIGNAL': self.config.get('strategies', {}).get('macd_signal', 9),
            
            # 日志配置
            'LOG_LEVEL': self.config.get('logging', {}).get('log_level', 'INFO'),
            'ENABLE_LOGGING': self.config.get('logging', {}).get('enable_logging', True),
            'LOG_TO_CONSOLE': self.config.get('logging', {}).get('log_to_console', True),
            
            # 系统配置
            'TEST_MODE': self.config.get('system', {}).get('test_mode', True),
            'SANDBOX': self.config.get('api', {}).get('okx', {}).get('sandbox', True),
            
            # 界面配置
            'window_geometry': self.config.get('ui', {}).get('window_geometry', '1200x800+100+100'),
            'current_tab': self.config.get('ui', {}).get('current_tab', 0),
            
            # 元数据
            '_UNIFIED_CONFIG': True,
            '_CONFIG_VERSION': self.config.get('_metadata', {}).get('config_version', '1.0'),
            '_LAST_UPDATED': datetime.now().isoformat()
        }
        
        return legacy_config
    
    def save_unified_config(self):
        """保存统一配置"""
        try:
            if self.config:
                self.config['_metadata']['last_updated'] = datetime.now().isoformat()
                
                with open(self.unified_config_file, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                
                print("✅ 统一配置已保存")
                return True
        except Exception as e:
            print(f"❌ 保存统一配置失败: {e}")
            return False
    
    def update_config(self, section, key, value):
        """更新配置"""
        if not self.config:
            return False
        
        try:
            if section not in self.config:
                self.config[section] = {}
            
            self.config[section][key] = value
            return self.save_unified_config()
        except Exception as e:
            print(f"❌ 更新配置失败: {e}")
            return False

# 创建全局统一配置适配器
wmzc_unified_config = WMZCUnifiedConfigAdapter()

def get_unified_config():
    """获取统一配置（兼容格式）"""
    return wmzc_unified_config.get_legacy_config()

def save_unified_config():
    """保存统一配置"""
    return wmzc_unified_config.save_unified_config()

def update_unified_config(section, key, value):
    """更新统一配置"""
    return wmzc_unified_config.update_config(section, key, value)

if __name__ == "__main__":
    # 测试统一配置
    config = get_unified_config()
    print(f"统一配置加载测试: {len(config)} 个配置项")
