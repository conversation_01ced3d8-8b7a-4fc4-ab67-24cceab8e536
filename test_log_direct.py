#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 直接测试WMZC日志控制台
验证日志控制台是否正常工作
"""

import time
from datetime import datetime

def test_wmzc_log_direct():
    """直接测试WMZC日志"""
    print("🧪 直接测试WMZC日志控制台")
    print("=" * 50)
    
    try:
        # 导入WMZC模块
        print("1. 导入WMZC模块...")
        import WMZC
        
        # 检查应用实例
        print("2. 检查应用实例...")
        if hasattr(WMZC, 'app') and WMZC.app:
            app = WMZC.app
            print("✅ 找到WMZC应用实例")
        else:
            print("❌ 未找到WMZC应用实例")
            return False
        
        # 检查日志方法
        print("3. 检查日志方法...")
        if hasattr(app, 'add_log_message'):
            print("✅ 找到add_log_message方法")
        else:
            print("❌ 未找到add_log_message方法")
            return False
        
        # 发送测试日志
        print("4. 发送测试日志...")
        
        test_messages = [
            "🔧 异步任务销毁错误已修复",
            "✅ 日志消费者线程正常运行", 
            "📊 系统状态检查完成",
            "🚀 日志控制台功能验证",
            "💡 协程警告和任务销毁问题已解决"
        ]
        
        for i, message in enumerate(test_messages):
            timestamp = datetime.now().strftime('%H:%M:%S')
            formatted_msg = f"[{timestamp}] INFO - {message}"
            
            try:
                app.add_log_message(formatted_msg, "INFO")
                print(f"✅ 发送成功: {message}")
                time.sleep(1)
            except Exception as e:
                print(f"❌ 发送失败: {message} - {e}")
                return False
        
        # 测试不同级别的日志
        print("5. 测试不同级别日志...")
        
        levels_test = [
            ("⚠️ 测试WARNING级别日志", "WARNING"),
            ("❌ 测试ERROR级别日志", "ERROR"),
            ("✅ 所有级别测试完成", "INFO")
        ]
        
        for message, level in levels_test:
            timestamp = datetime.now().strftime('%H:%M:%S')
            formatted_msg = f"[{timestamp}] {level} - {message}"
            
            try:
                app.add_log_message(formatted_msg, level)
                print(f"✅ {level}级别发送成功")
                time.sleep(1)
            except Exception as e:
                print(f"❌ {level}级别发送失败: {e}")
        
        print("\n🎉 日志控制台测试完成！")
        print("💡 请检查WMZC系统的'📜 日志控制台'标签页")
        print("💡 您应该看到刚才发送的所有测试消息")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入WMZC模块失败: {e}")
        print("💡 请确保WMZC系统正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        return False

def continuous_test():
    """持续测试"""
    print("\n🔄 启动持续测试...")
    
    try:
        import WMZC
        app = WMZC.app
        
        if not app or not hasattr(app, 'add_log_message'):
            print("❌ 无法启动持续测试")
            return
        
        print("✅ 持续测试已启动")
        print("💡 每5秒发送一条测试日志")
        print("💡 按Ctrl+C停止")
        
        counter = 1
        while True:
            timestamp = datetime.now().strftime('%H:%M:%S')
            message = f"[{timestamp}] INFO - 🔄 持续测试 #{counter} - 系统运行正常"
            
            try:
                app.add_log_message(message, "INFO")
                print(f"✅ 发送测试消息 #{counter}")
                counter += 1
                time.sleep(5)
            except Exception as e:
                print(f"❌ 发送失败: {e}")
                break
                
    except KeyboardInterrupt:
        print("\n🛑 持续测试已停止")
    except Exception as e:
        print(f"❌ 持续测试异常: {e}")

def main():
    """主函数"""
    try:
        # 基础测试
        success = test_wmzc_log_direct()
        
        if success:
            print("\n" + "=" * 50)
            print("🎯 测试结果: 成功")
            print("💡 日志控制台功能正常")
            
            # 询问是否进行持续测试
            choice = input("\n是否启动持续测试？(y/N): ").strip().lower()
            if choice == 'y':
                continuous_test()
        else:
            print("\n" + "=" * 50)
            print("❌ 测试结果: 失败")
            print("💡 请检查WMZC系统是否正常运行")
        
    except KeyboardInterrupt:
        print("\n🛑 测试被中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")

if __name__ == "__main__":
    main()
