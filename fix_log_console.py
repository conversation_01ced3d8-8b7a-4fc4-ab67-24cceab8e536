#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC日志控制台修复工具
修复日志控制台显示问题，确保日志正常输出
"""

import os
import json
import shutil
import traceback
from datetime import datetime

class LogConsoleFixer:
    """日志控制台修复器"""
    
    def __init__(self):
        self.current_dir = os.getcwd()
        self.wmzc_file = 'WMZC.py'
        self.config_files = ['trading_config.json', 'wmzc_config.json']
        self.fixes_applied = []
        
    def run_comprehensive_fix(self):
        """运行全面修复"""
        print("🔧 WMZC日志控制台修复")
        print("=" * 60)
        
        try:
            # 1. 备份文件
            self.create_backup()
            
            # 2. 修复日志配置
            self.fix_log_configuration()
            
            # 3. 修复日志初始化时机
            self.fix_log_initialization_timing()
            
            # 4. 增强日志输出机制
            self.enhance_log_output_mechanism()
            
            # 5. 创建日志测试工具
            self.create_log_test_tool()
            
            # 6. 验证修复效果
            self.verify_log_console_fix()
            
            return len(self.fixes_applied) > 0
            
        except Exception as e:
            print(f"❌ 修复过程中发生异常: {e}")
            traceback.print_exc()
            return False
    
    def create_backup(self):
        """创建备份"""
        print("\n💾 创建备份...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = f'log_console_backup_{timestamp}'
        
        try:
            os.makedirs(backup_dir, exist_ok=True)
            
            # 备份WMZC.py
            if os.path.exists(self.wmzc_file):
                shutil.copy2(self.wmzc_file, os.path.join(backup_dir, f'{self.wmzc_file}.backup'))
                print(f"  ✅ 已备份: {self.wmzc_file}")
            
            # 备份配置文件
            for config_file in self.config_files:
                if os.path.exists(config_file):
                    shutil.copy2(config_file, os.path.join(backup_dir, f'{config_file}.backup'))
                    print(f"  ✅ 已备份: {config_file}")
            
            self.fixes_applied.append(f"创建备份: {backup_dir}")
            
        except Exception as e:
            print(f"  ❌ 创建备份失败: {e}")
    
    def fix_log_configuration(self):
        """修复日志配置"""
        print("\n⚙️ 修复日志配置...")
        
        # 修复配置文件中的日志设置
        for config_file in self.config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 确保日志配置正确
                    log_config_updated = False
                    
                    # 设置合适的日志级别
                    if config.get('LOG_LEVEL') in ['ERROR', 'CRITICAL']:
                        config['LOG_LEVEL'] = 'INFO'
                        log_config_updated = True
                        print(f"  ✅ {config_file}: 日志级别已调整为INFO")
                    elif 'LOG_LEVEL' not in config:
                        config['LOG_LEVEL'] = 'INFO'
                        log_config_updated = True
                        print(f"  ✅ {config_file}: 添加日志级别配置")
                    
                    # 确保日志功能启用
                    if not config.get('ENABLE_LOGGING', True):
                        config['ENABLE_LOGGING'] = True
                        log_config_updated = True
                        print(f"  ✅ {config_file}: 启用日志功能")
                    
                    # 确保控制台日志启用
                    if not config.get('LOG_TO_CONSOLE', True):
                        config['LOG_TO_CONSOLE'] = True
                        log_config_updated = True
                        print(f"  ✅ {config_file}: 启用控制台日志")
                    
                    # 添加日志修复标记
                    config['_LOG_CONSOLE_FIXED'] = datetime.now().isoformat()
                    log_config_updated = True
                    
                    if log_config_updated:
                        with open(config_file, 'w', encoding='utf-8') as f:
                            json.dump(config, f, indent=2, ensure_ascii=False)
                        print(f"  ✅ {config_file}: 日志配置已更新")
                        self.fixes_applied.append(f"修复{config_file}日志配置")
                    else:
                        print(f"  ✅ {config_file}: 日志配置正常")
                        
                except Exception as e:
                    print(f"  ❌ 修复 {config_file} 失败: {e}")
    
    def fix_log_initialization_timing(self):
        """修复日志初始化时机"""
        print("\n🔄 修复日志初始化时机...")
        
        # 创建日志初始化增强脚本
        init_enhancement = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC日志初始化增强脚本
确保日志控制台在正确的时机初始化并连接
"""

import logging
import sys
from datetime import datetime

class WMZCLogInitializer:
    """WMZC日志初始化器"""
    
    def __init__(self):
        self.log_handlers = []
        self.gui_callback = None
        
    def setup_enhanced_logging(self):
        """设置增强的日志系统"""
        try:
            # 创建根日志记录器
            root_logger = logging.getLogger()
            root_logger.setLevel(logging.DEBUG)
            
            # 清除现有处理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            
            # 创建格式器
            formatter = logging.Formatter(
                '[%(asctime)s] %(levelname)s - %(message)s',
                datefmt='%H:%M:%S'
            )
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            root_logger.addHandler(console_handler)
            self.log_handlers.append(console_handler)
            
            print("✅ 增强日志系统初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 增强日志系统初始化失败: {e}")
            return False
    
    def set_gui_callback(self, callback):
        """设置GUI回调"""
        self.gui_callback = callback
        print("✅ GUI日志回调已设置")
    
    def test_log_output(self):
        """测试日志输出"""
        try:
            # 测试不同级别的日志
            logging.debug("DEBUG级别测试消息")
            logging.info("INFO级别测试消息")
            logging.warning("WARNING级别测试消息")
            logging.error("ERROR级别测试消息")
            
            # 如果有GUI回调，也测试GUI输出
            if self.gui_callback:
                self.gui_callback("GUI日志测试消息", "INFO")
                self.gui_callback("GUI警告测试消息", "WARNING")
                self.gui_callback("GUI错误测试消息", "ERROR")
            
            print("✅ 日志输出测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 日志输出测试失败: {e}")
            return False

# 创建全局日志初始化器
wmzc_log_initializer = WMZCLogInitializer()

def initialize_wmzc_logging():
    """初始化WMZC日志系统"""
    return wmzc_log_initializer.setup_enhanced_logging()

def set_wmzc_gui_callback(callback):
    """设置WMZC GUI回调"""
    wmzc_log_initializer.set_gui_callback(callback)

def test_wmzc_logging():
    """测试WMZC日志系统"""
    return wmzc_log_initializer.test_log_output()

if __name__ == "__main__":
    initialize_wmzc_logging()
    test_wmzc_logging()
'''
        
        try:
            with open('wmzc_log_initializer.py', 'w', encoding='utf-8') as f:
                f.write(init_enhancement)
            print("  ✅ 日志初始化增强脚本已创建: wmzc_log_initializer.py")
            self.fixes_applied.append("创建日志初始化增强脚本")
        except Exception as e:
            print(f"  ❌ 创建日志初始化脚本失败: {e}")
    
    def enhance_log_output_mechanism(self):
        """增强日志输出机制"""
        print("\n🚀 增强日志输出机制...")
        
        # 创建日志输出增强器
        output_enhancer = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 WMZC日志输出增强器
增强日志输出机制，确保日志正确显示在控制台
"""

import threading
import queue
import time
from datetime import datetime

class LogOutputEnhancer:
    """日志输出增强器"""
    
    def __init__(self):
        self.log_queue = queue.Queue()
        self.gui_callback = None
        self.output_thread = None
        self.running = False
        
    def set_gui_callback(self, callback):
        """设置GUI回调"""
        self.gui_callback = callback
        
    def start_log_processor(self):
        """启动日志处理器"""
        if self.running:
            return
        
        self.running = True
        self.output_thread = threading.Thread(target=self._process_logs, daemon=True)
        self.output_thread.start()
        print("✅ 日志处理器已启动")
    
    def stop_log_processor(self):
        """停止日志处理器"""
        self.running = False
        if self.output_thread:
            self.output_thread.join(timeout=1)
        print("✅ 日志处理器已停止")
    
    def add_log(self, message, level="INFO"):
        """添加日志到队列"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_entry = {
                'timestamp': timestamp,
                'message': message,
                'level': level,
                'formatted': f"[{timestamp}] {level} - {message}"
            }
            self.log_queue.put(log_entry, timeout=1)
        except queue.Full:
            print("⚠️ 日志队列已满，丢弃日志消息")
    
    def _process_logs(self):
        """处理日志队列"""
        while self.running:
            try:
                # 从队列获取日志
                log_entry = self.log_queue.get(timeout=0.1)
                
                # 输出到控制台
                print(log_entry['formatted'])
                
                # 输出到GUI（如果可用）
                if self.gui_callback:
                    try:
                        self.gui_callback(log_entry['message'], log_entry['level'])
                    except Exception as e:
                        print(f"GUI日志输出失败: {e}")
                
                self.log_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"日志处理异常: {e}")
                time.sleep(0.1)
    
    def flush_logs(self):
        """刷新所有待处理的日志"""
        try:
            while not self.log_queue.empty():
                log_entry = self.log_queue.get_nowait()
                print(log_entry['formatted'])
                if self.gui_callback:
                    try:
                        self.gui_callback(log_entry['message'], log_entry['level'])
                    except:
                        pass
                self.log_queue.task_done()
        except queue.Empty:
            pass

# 创建全局日志输出增强器
log_output_enhancer = LogOutputEnhancer()

def start_enhanced_logging():
    """启动增强日志系统"""
    log_output_enhancer.start_log_processor()

def stop_enhanced_logging():
    """停止增强日志系统"""
    log_output_enhancer.stop_log_processor()

def enhanced_log(message, level="INFO"):
    """增强日志函数"""
    log_output_enhancer.add_log(message, level)

def set_enhanced_gui_callback(callback):
    """设置增强GUI回调"""
    log_output_enhancer.set_gui_callback(callback)

if __name__ == "__main__":
    # 测试增强日志系统
    start_enhanced_logging()
    
    enhanced_log("测试INFO消息", "INFO")
    enhanced_log("测试WARNING消息", "WARNING")
    enhanced_log("测试ERROR消息", "ERROR")
    
    time.sleep(1)
    stop_enhanced_logging()
'''
        
        try:
            with open('log_output_enhancer.py', 'w', encoding='utf-8') as f:
                f.write(output_enhancer)
            print("  ✅ 日志输出增强器已创建: log_output_enhancer.py")
            self.fixes_applied.append("创建日志输出增强器")
        except Exception as e:
            print(f"  ❌ 创建日志输出增强器失败: {e}")
    
    def create_log_test_tool(self):
        """创建日志测试工具"""
        print("\n🧪 创建日志测试工具...")
        
        test_tool = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 WMZC日志控制台测试工具
测试日志控制台的各项功能
"""

import tkinter as tk
from tkinter import ttk
import logging
import threading
import time
from datetime import datetime

class LogConsoleTestTool:
    """日志控制台测试工具"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("WMZC日志控制台测试工具")
        self.root.geometry("800x600")
        
        self.setup_ui()
        self.setup_logging()
        
    def setup_ui(self):
        """设置用户界面"""
        # 控制面板
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Button(control_frame, text="测试INFO日志", 
                  command=lambda: self.test_log("INFO")).pack(side="left", padx=5)
        ttk.Button(control_frame, text="测试WARNING日志", 
                  command=lambda: self.test_log("WARNING")).pack(side="left", padx=5)
        ttk.Button(control_frame, text="测试ERROR日志", 
                  command=lambda: self.test_log("ERROR")).pack(side="left", padx=5)
        ttk.Button(control_frame, text="批量测试", 
                  command=self.batch_test).pack(side="left", padx=5)
        ttk.Button(control_frame, text="清空日志", 
                  command=self.clear_log).pack(side="left", padx=5)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.root, text="日志输出")
        log_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=20, wrap=tk.WORD,
                               font=("Consolas", 9), bg="black", fg="white")
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # 配置日志颜色
        self.log_text.tag_config("INFO", foreground="lightgreen")
        self.log_text.tag_config("WARNING", foreground="yellow")
        self.log_text.tag_config("ERROR", foreground="red")
        self.log_text.tag_config("DEBUG", foreground="lightblue")
        
        self.log_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief="sunken")
        status_bar.pack(fill="x", side="bottom")
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建自定义日志处理器
        class GUILogHandler(logging.Handler):
            def __init__(self, gui_callback):
                super().__init__()
                self.gui_callback = gui_callback
            
            def emit(self, record):
                try:
                    msg = self.format(record)
                    self.gui_callback(msg, record.levelname)
                except:
                    pass
        
        # 配置日志
        self.logger = logging.getLogger('wmzc_test')
        self.logger.setLevel(logging.DEBUG)
        
        # 添加GUI处理器
        gui_handler = GUILogHandler(self.add_log_message)
        formatter = logging.Formatter('[%(asctime)s] %(levelname)s - %(message)s',
                                    datefmt='%H:%M:%S')
        gui_handler.setFormatter(formatter)
        self.logger.addHandler(gui_handler)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def add_log_message(self, message, level="INFO"):
        """添加日志消息到GUI"""
        try:
            self.log_text.config(state=tk.NORMAL)
            
            # 插入消息
            self.log_text.insert(tk.END, message + "\\n")
            
            # 设置颜色
            line_start = self.log_text.index("end-2c linestart")
            line_end = self.log_text.index("end-2c lineend")
            self.log_text.tag_add(level, line_start, line_end)
            
            # 自动滚动
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)
            
            # 更新状态
            self.status_var.set(f"最后日志: {level} - {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            print(f"GUI日志更新失败: {e}")
    
    def test_log(self, level):
        """测试指定级别的日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        message = f"测试{level}级别日志消息 - {timestamp}"
        
        if level == "INFO":
            self.logger.info(message)
        elif level == "WARNING":
            self.logger.warning(message)
        elif level == "ERROR":
            self.logger.error(message)
        elif level == "DEBUG":
            self.logger.debug(message)
    
    def batch_test(self):
        """批量测试日志"""
        def run_batch():
            for i in range(5):
                self.test_log("INFO")
                time.sleep(0.2)
                self.test_log("WARNING")
                time.sleep(0.2)
                self.test_log("ERROR")
                time.sleep(0.2)
        
        threading.Thread(target=run_batch, daemon=True).start()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete("1.0", tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.add_log_message("🗑️ 日志已清空", "INFO")
    
    def run(self):
        """运行测试工具"""
        self.add_log_message("🧪 日志控制台测试工具已启动", "INFO")
        self.add_log_message("💡 点击按钮测试不同级别的日志输出", "INFO")
        self.root.mainloop()

def main():
    """主函数"""
    print("🧪 启动WMZC日志控制台测试工具...")
    
    try:
        test_tool = LogConsoleTestTool()
        test_tool.run()
    except Exception as e:
        print(f"❌ 测试工具启动失败: {e}")

if __name__ == "__main__":
    main()
'''
        
        try:
            with open('test_log_console.py', 'w', encoding='utf-8') as f:
                f.write(test_tool)
            print("  ✅ 日志测试工具已创建: test_log_console.py")
            self.fixes_applied.append("创建日志测试工具")
        except Exception as e:
            print(f"  ❌ 创建日志测试工具失败: {e}")
    
    def verify_log_console_fix(self):
        """验证日志控制台修复"""
        print("\n✅ 验证日志控制台修复...")
        
        verification_results = {
            'config_fixed': 0,
            'scripts_created': 0,
            'wmzc_file_exists': False
        }
        
        # 验证配置文件修复
        for config_file in self.config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    if (config.get('LOG_LEVEL') == 'INFO' and 
                        config.get('ENABLE_LOGGING', True) and 
                        config.get('LOG_TO_CONSOLE', True) and
                        '_LOG_CONSOLE_FIXED' in config):
                        verification_results['config_fixed'] += 1
                        print(f"  ✅ {config_file}: 配置修复验证通过")
                    else:
                        print(f"  ⚠️ {config_file}: 配置修复可能不完整")
                except Exception as e:
                    print(f"  ❌ {config_file}: 验证失败 {e}")
        
        # 验证脚本创建
        scripts = ['wmzc_log_initializer.py', 'log_output_enhancer.py', 'test_log_console.py']
        for script in scripts:
            if os.path.exists(script):
                verification_results['scripts_created'] += 1
                print(f"  ✅ {script}: 已创建")
            else:
                print(f"  ❌ {script}: 未创建")
        
        # 验证WMZC文件
        verification_results['wmzc_file_exists'] = os.path.exists(self.wmzc_file)
        if verification_results['wmzc_file_exists']:
            print(f"  ✅ {self.wmzc_file}: 存在")
        else:
            print(f"  ❌ {self.wmzc_file}: 不存在")
        
        # 显示验证结果
        print(f"\n📊 验证结果:")
        print(f"  配置文件修复: {verification_results['config_fixed']}/{len(self.config_files)}")
        print(f"  脚本创建: {verification_results['scripts_created']}/3")
        print(f"  WMZC文件: {'✅' if verification_results['wmzc_file_exists'] else '❌'}")
        
        return verification_results

def main():
    """主函数"""
    print("🔧 WMZC日志控制台修复工具")
    print("=" * 60)
    
    fixer = LogConsoleFixer()
    
    try:
        success = fixer.run_comprehensive_fix()
        
        print("\n" + "=" * 60)
        if success:
            print(f"🎉 日志控制台修复完成！共应用了 {len(fixer.fixes_applied)} 个修复")
            print("\n💡 修复内容:")
            for i, fix in enumerate(fixer.fixes_applied, 1):
                print(f"  {i}. ✅ {fix}")
            
            print("\n🚀 下一步操作:")
            print("  1. 重新启动WMZC系统")
            print("  2. 检查日志控制台标签页是否正常显示")
            print("  3. 运行 python test_log_console.py 测试日志功能")
            print("  4. 验证系统日志是否正常输出到控制台")
            
            print("\n🧪 测试建议:")
            print("  • 使用日志测试工具验证功能")
            print("  • 检查不同级别日志的颜色显示")
            print("  • 测试日志清空和保存功能")
            print("  • 验证日志自动滚动是否正常")
            
        else:
            print("❌ 日志控制台修复失败")
            print("💡 请检查错误信息并手动修复")
        
        return success
        
    except Exception as e:
        print(f"❌ 修复过程中发生异常: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
