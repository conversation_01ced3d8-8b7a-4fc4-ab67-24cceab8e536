#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC修复后全面分析器
执行修复后的系统分析和Bug检测，确保零回归
"""

import ast
import re
import os
import json
import shutil
from datetime import datetime
from typing import List, Dict, Set, Tuple
from collections import defaultdict, Counter

class PostFixBugDetector:
    """修复后Bug检测器"""
    
    def __init__(self, file_path: str = "WMZC.py"):
        self.file_path = file_path
        self.backup_path = f"WMZC_post_analysis_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        self.bugs_found = []
        self.code_lines = []
        self.total_lines = 0
        
        # 检测统计
        self.stats = {
            'syntax_errors': 0,
            'logic_errors': 0,
            'naming_issues': 0,
            'duplicate_code': 0,
            'performance_issues': 0,
            'security_issues': 0,
            'regression_risks': 0
        }
        
    def create_backup(self):
        """创建备份"""
        try:
            shutil.copy2(self.file_path, self.backup_path)
            print(f"✅ 备份已创建: {self.backup_path}")
            return True
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    
    def load_and_parse_code(self):
        """加载和解析代码"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                self.code_lines = content.split('\n')
                self.total_lines = len(self.code_lines)
            
            print(f"✅ 成功加载 {self.total_lines} 行代码")
            
            # 尝试解析AST
            try:
                self.ast_tree = ast.parse(content)
                print("✅ Python语法验证通过")
                return True
            except SyntaxError as e:
                self.add_bug("SYNTAX_001", "🔴致命", "语法错误", 
                           f"Python语法错误: {e}", f"第{e.lineno}行", 
                           self.code_lines[e.lineno-1] if e.lineno <= len(self.code_lines) else "",
                           "修复语法错误", "系统无法启动")
                self.stats['syntax_errors'] += 1
                return False
                
        except Exception as e:
            print(f"❌ 加载代码失败: {e}")
            return False
    
    def add_bug(self, bug_id: str, severity: str, category: str, description: str,
                location: str, code_snippet: str, fix_suggestion: str, impact: str):
        """添加Bug记录"""
        self.bugs_found.append({
            'id': bug_id,
            'severity': severity,
            'category': category,
            'description': description,
            'location': location,
            'code_snippet': code_snippet,
            'fix_suggestion': fix_suggestion,
            'impact': impact
        })
    
    def detect_post_fix_regressions(self):
        """检测修复后的回归问题"""
        print("🔍 检测修复后回归问题...")
        
        regression_patterns = [
            # 检测被注释掉的重要函数
            (r'#.*def\s+(\w+)', "重要函数被注释", "可能导致功能缺失"),
            # 检测孤立的文档字符串
            (r'^\s*""".*"""$', "孤立文档字符串", "可能是函数定义被错误删除"),
            # 检测不完整的类定义
            (r'class\s+\w+.*:\s*$', "空类定义", "类实现可能被意外删除"),
            # 检测未闭合的括号或引号
            (r'["\'].*[^"\']$', "未闭合引号", "可能导致语法错误"),
        ]
        
        for i, line in enumerate(self.code_lines, 1):
            for pattern, issue_type, impact in regression_patterns:
                if re.search(pattern, line):
                    self.add_bug(f"REGRESSION_{len(self.bugs_found)+1:03d}", "🟠高危", "回归风险",
                               f"{issue_type}: {line.strip()[:50]}...",
                               f"第{i}行", line.strip(),
                               f"检查并修复{issue_type}", impact)
                    self.stats['regression_risks'] += 1
    
    def detect_naming_inconsistencies(self):
        """检测命名不一致问题"""
        print("🔍 检测命名不一致...")
        
        # 收集所有函数和变量名
        function_names = set()
        variable_names = set()
        class_names = set()
        
        for node in ast.walk(self.ast_tree):
            if isinstance(node, ast.FunctionDef):
                function_names.add(node.name)
            elif isinstance(node, ast.ClassDef):
                class_names.add(node.name)
            elif isinstance(node, ast.Name):
                variable_names.add(node.id)
        
        # 检测命名风格不一致
        snake_case_pattern = re.compile(r'^[a-z_][a-z0-9_]*$')
        camel_case_pattern = re.compile(r'^[a-z][a-zA-Z0-9]*$')
        pascal_case_pattern = re.compile(r'^[A-Z][a-zA-Z0-9]*$')
        
        # 检查函数命名
        snake_case_funcs = [f for f in function_names if snake_case_pattern.match(f)]
        camel_case_funcs = [f for f in function_names if camel_case_pattern.match(f)]
        
        if len(snake_case_funcs) > 0 and len(camel_case_funcs) > 0:
            self.add_bug("NAMING_001", "🟡中危", "命名不一致",
                       f"函数命名风格混合: {len(snake_case_funcs)}个snake_case, {len(camel_case_funcs)}个camelCase",
                       "全局", f"示例: {snake_case_funcs[0]} vs {camel_case_funcs[0]}",
                       "统一使用snake_case命名风格", "影响代码可读性")
            self.stats['naming_issues'] += 1
    
    def detect_duplicate_code_intelligent(self):
        """智能检测重复代码"""
        print("🔍 智能检测重复代码...")
        
        # 函数级别的重复检测
        function_bodies = {}
        
        for node in ast.walk(self.ast_tree):
            if isinstance(node, ast.FunctionDef):
                # 获取函数体的AST字符串表示
                try:
                    body_str = ast.dump(node)
                    func_signature = f"{node.name}({len(node.args.args)}args)"
                    
                    if body_str in function_bodies:
                        # 检查是否是真正的重复（排除合理的重复）
                        existing_func = function_bodies[body_str]
                        
                        # 排除特殊方法和构造函数
                        if (node.name not in ['__init__', '__getattr__', '__setattr__', '__str__', '__repr__'] and
                            not node.name.startswith('_') and
                            not node.name.endswith('_callback') and
                            existing_func != node.name):
                            
                            self.add_bug(f"DUPLICATE_{len(self.bugs_found)+1:03d}", "🟡中危", "重复代码",
                                       f"函数 '{node.name}' 与 '{existing_func}' 实现完全相同",
                                       f"第{node.lineno}行", f"def {node.name}(...)",
                                       f"合并重复函数或确认功能差异", "代码冗余")
                            self.stats['duplicate_code'] += 1
                    else:
                        function_bodies[body_str] = node.name
                        
                except Exception:
                    continue
    
    def detect_performance_issues(self):
        """检测性能问题"""
        print("🔍 检测性能问题...")
        
        performance_patterns = [
            # 循环中的重复计算
            (r'for.*in.*:\s*.*len\(', "循环中重复计算len()", "缓存len()结果"),
            # 字符串拼接在循环中
            (r'for.*:\s*.*\+=.*["\']', "循环中字符串拼接", "使用join()方法"),
            # 全局变量频繁访问
            (r'global\s+\w+.*\n.*global\s+\w+', "频繁全局变量声明", "减少全局变量使用"),
            # 深层嵌套循环
            (r'for.*:\s*.*for.*:\s*.*for.*:', "三层嵌套循环", "考虑算法优化"),
        ]
        
        for i, line in enumerate(self.code_lines, 1):
            for pattern, issue_type, suggestion in performance_patterns:
                if re.search(pattern, line, re.MULTILINE):
                    self.add_bug(f"PERF_{len(self.bugs_found)+1:03d}", "🟢低危", "性能问题",
                               f"{issue_type}: {line.strip()[:50]}...",
                               f"第{i}行", line.strip(),
                               suggestion, "轻微性能影响")
                    self.stats['performance_issues'] += 1
    
    def detect_security_issues(self):
        """检测安全问题"""
        print("🔍 检测安全问题...")
        
        security_patterns = [
            # 硬编码密钥（更精确的检测）
            (r'["\'][a-zA-Z0-9]{20,}["\'].*(?:key|secret|token|password)', "可能的硬编码密钥", "使用环境变量"),
            # SQL注入风险
            (r'execute\s*\(\s*["\'].*%.*["\']', "SQL注入风险", "使用参数化查询"),
            # 不安全的pickle使用
            (r'pickle\.loads?\s*\(', "不安全的pickle使用", "验证数据来源"),
            # 不安全的eval使用（排除model.eval()）
            (r'(?<!model\.)eval\s*\(', "不安全的eval使用", "使用ast.literal_eval"),
        ]
        
        for i, line in enumerate(self.code_lines, 1):
            for pattern, issue_type, suggestion in security_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # 排除注释行
                    if not line.strip().startswith('#'):
                        self.add_bug(f"SECURITY_{len(self.bugs_found)+1:03d}", "🔴致命", "安全风险",
                                   f"{issue_type}: {line.strip()[:50]}...",
                                   f"第{i}行", line.strip(),
                                   suggestion, "安全风险")
                        self.stats['security_issues'] += 1
    
    def detect_logic_errors(self):
        """检测逻辑错误"""
        print("🔍 检测逻辑错误...")
        
        logic_patterns = [
            # 空的except块
            (r'except.*:\s*pass\s*$', "空异常处理", "添加适当的错误处理"),
            # 可能的除零错误
            (r'/\s*[a-zA-Z_]\w*(?!\s*[!=<>])', "可能的除零错误", "添加除零检查"),
            # 无限循环风险
            (r'while\s+True:(?!.*break)', "可能的无限循环", "确保有退出条件"),
            # 未使用的变量
            (r'^\s*(\w+)\s*=.*\n(?!.*\1)', "可能未使用的变量", "删除或使用变量"),
        ]
        
        for i, line in enumerate(self.code_lines, 1):
            for pattern, issue_type, suggestion in logic_patterns:
                if re.search(pattern, line, re.MULTILINE):
                    self.add_bug(f"LOGIC_{len(self.bugs_found)+1:03d}", "🟡中危", "逻辑错误",
                               f"{issue_type}: {line.strip()[:50]}...",
                               f"第{i}行", line.strip(),
                               suggestion, "可能导致运行时错误")
                    self.stats['logic_errors'] += 1
    
    def run_comprehensive_analysis(self):
        """运行全面分析"""
        print("🚀 开始WMZC修复后全面分析...")
        
        # 1. 创建备份
        if not self.create_backup():
            return False
        
        # 2. 加载和解析代码
        if not self.load_and_parse_code():
            return False
        
        # 3. 运行各种检测
        self.detect_post_fix_regressions()
        
        if hasattr(self, 'ast_tree'):  # 只有语法正确时才运行AST相关检测
            self.detect_naming_inconsistencies()
            self.detect_duplicate_code_intelligent()
        
        self.detect_performance_issues()
        self.detect_security_issues()
        self.detect_logic_errors()
        
        # 4. 生成报告
        self.generate_comprehensive_report()
        
        return True
    
    def generate_comprehensive_report(self):
        """生成全面报告"""
        print("\n" + "=" * 80)
        print("📊 WMZC修复后全面分析报告")
        print("=" * 80)
        
        total_bugs = len(self.bugs_found)
        
        if total_bugs == 0:
            print("🎉 恭喜！未发现任何问题，系统状态优秀！")
            return
        
        # 按严重程度分类
        severity_counts = Counter(bug['severity'] for bug in self.bugs_found)
        
        print(f"📈 代码规模: {self.total_lines:,} 行")
        print(f"🔍 发现问题: {total_bugs} 个")
        print("\n📊 问题分布:")
        for severity, count in severity_counts.items():
            print(f"  {severity}: {count} 个")
        
        print("\n📋 问题分类统计:")
        for category, count in self.stats.items():
            if count > 0:
                print(f"  {category.replace('_', ' ').title()}: {count} 个")
        
        # 显示前10个最重要的问题
        print("\n🔍 重要问题详情 (前10个):")
        print("-" * 80)
        
        # 按严重程度排序
        severity_order = {"🔴致命": 0, "🟠高危": 1, "🟡中危": 2, "🟢低危": 3}
        sorted_bugs = sorted(self.bugs_found, key=lambda x: severity_order.get(x['severity'], 4))
        
        for i, bug in enumerate(sorted_bugs[:10], 1):
            print(f"\n{i}. {bug['severity']} {bug['id']}: {bug['description']}")
            print(f"   📍 位置: {bug['location']}")
            print(f"   📝 代码: {bug['code_snippet'][:80]}...")
            print(f"   🔧 建议: {bug['fix_suggestion']}")
            print(f"   💥 影响: {bug['impact']}")
        
        if total_bugs > 10:
            print(f"\n... 还有 {total_bugs - 10} 个问题未显示")
        
        # 生成修复建议
        self.generate_fix_recommendations()
    
    def generate_fix_recommendations(self):
        """生成修复建议"""
        print("\n" + "=" * 80)
        print("💡 修复建议")
        print("=" * 80)
        
        critical_bugs = [bug for bug in self.bugs_found if bug['severity'] == "🔴致命"]
        high_bugs = [bug for bug in self.bugs_found if bug['severity'] == "🟠高危"]
        
        if critical_bugs:
            print(f"🚨 立即修复 ({len(critical_bugs)} 个致命问题):")
            for bug in critical_bugs[:5]:
                print(f"   • {bug['description']} - {bug['location']}")
        
        if high_bugs:
            print(f"\n⚠️ 优先修复 ({len(high_bugs)} 个高危问题):")
            for bug in high_bugs[:5]:
                print(f"   • {bug['description']} - {bug['location']}")
        
        # 系统健康度评估
        total_issues = len(self.bugs_found)
        critical_count = len(critical_bugs)
        high_count = len(high_bugs)
        
        if critical_count == 0 and high_count == 0:
            health_score = "优秀"
            health_color = "🟢"
        elif critical_count == 0 and high_count <= 3:
            health_score = "良好"
            health_color = "🟡"
        elif critical_count <= 2:
            health_score = "一般"
            health_color = "🟠"
        else:
            health_score = "需要改进"
            health_color = "🔴"
        
        print(f"\n{health_color} 系统健康度: {health_score}")
        print(f"📊 问题密度: {total_issues / self.total_lines * 1000:.2f} 问题/千行")
        
        # 保存详细报告
        self.save_detailed_report()
    
    def save_detailed_report(self):
        """保存详细报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"wmzc_post_fix_analysis_report_{timestamp}.json"
        
        report_data = {
            'analysis_time': datetime.now().isoformat(),
            'file_analyzed': self.file_path,
            'total_lines': self.total_lines,
            'total_bugs': len(self.bugs_found),
            'statistics': self.stats,
            'bugs': self.bugs_found
        }
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 详细报告已保存: {report_file}")
            
        except Exception as e:
            print(f"\n⚠️ 保存报告失败: {e}")

def main():
    """主函数"""
    print("=" * 80)
    print("🔍 WMZC修复后全面分析工具")
    print("执行修复后的系统分析和Bug检测，确保零回归")
    print("=" * 80)
    
    detector = PostFixBugDetector()
    
    try:
        success = detector.run_comprehensive_analysis()
        
        if success:
            print("\n✅ 修复后分析完成！")
            return True
        else:
            print("\n❌ 分析过程中出现错误")
            return False
            
    except Exception as e:
        print(f"\n💥 分析异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
