{"total_analyzed": 70, "real_bugs_count": 17, "false_positives_count": 17, "uncertain_cases_count": 36, "real_bugs": [{"line": 8823, "content": "loop = asyncio.get_event_loop()", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 8961, "content": "loop = asyncio.get_event_loop()", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 13799, "content": "asyncio.create_task(self._position_risk_monitor_async())", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 13800, "content": "asyncio.create_task(self._market_risk_monitor_async())", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 13801, "content": "asyncio.create_task(self._liquidity_risk_monitor_async())", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 13802, "content": "asyncio.create_task(self._operational_risk_monitor_async())", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 13803, "content": "asyncio.create_task(self._compliance_monitor_async())", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 13810, "content": "self._position_risk_monitor_async(),", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 13811, "content": "self._market_risk_monitor_async(),", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 13812, "content": "self._liquidity_risk_monitor_async(),", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 13813, "content": "self._operational_risk_monitor_async(),", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 13814, "content": "self._compliance_monitor_async()", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 21956, "content": "ohlcv = self.client.fetch_ohlcv(symbol, timeframe, limit=limit)", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 22029, "content": "ohlcv = self.client.fetch_ohlcv(symbol, timeframe, limit=limit)", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 22829, "content": "asyncio.get_running_loop()", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 26772, "content": "asyncio.get_running_loop()", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}, {"line": 26926, "content": "return okx_client.fetch_ticker(formatted_symbol)", "analysis": {"category": "real_bug", "reason": "明确的异步方法调用缺少await", "confidence": "high"}}], "false_positives": [{"line": 8188, "content": "result = global_exception_handler.handle_exception(", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}, {"line": 22783, "content": "error_msg = self.enhanced_api.handle_api_error(e) if self.enhanced_api else str(e)", "analysis": {"category": "false_positive", "reason": "明确的同步方法调用", "confidence": "high"}}, {"line": 22861, "content": "return loop.run_until_complete(", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}, {"line": 22982, "content": "error_msg = self.enhanced_api.handle_api_error(e) if self.enhanced_api else str(e)", "analysis": {"category": "false_positive", "reason": "明确的同步方法调用", "confidence": "high"}}, {"line": 23003, "content": "error_msg = self.enhanced_api.handle_api_error(e) if self.enhanced_api else str(e)", "analysis": {"category": "false_positive", "reason": "明确的同步方法调用", "confidence": "high"}}, {"line": 23059, "content": "error_msg = self.enhanced_api.handle_api_error(e) if self.enhanced_api else str(e)", "analysis": {"category": "false_positive", "reason": "明确的同步方法调用", "confidence": "high"}}, {"line": 23078, "content": "error_msg = self.enhanced_api.handle_api_error(e) if self.enhanced_api else str(e)", "analysis": {"category": "false_positive", "reason": "明确的同步方法调用", "confidence": "high"}}, {"line": 59639, "content": "strategy_result = advanced_macd_strategy.execute_strategy(current_config['SYMBOL'], df)", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}, {"line": 59677, "content": "enhanced_result = enhanced_golden_cross_strategy.execute_strategy(current_config['SYMBOL'], df)", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}, {"line": 300, "content": "class TechnicalIndicatorCalculator:", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}, {"line": 340, "content": "", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}, {"line": 503, "content": "", "analysis": {"category": "false_positive", "reason": "不在异步函数中", "confidence": "high"}}, {"line": 609, "content": "", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}, {"line": 610, "content": "# RSI信号分析", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}, {"line": 702, "content": "\"signal\": final_signal,", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}, {"line": 703, "content": "\"confidence\": confidence,", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}, {"line": 704, "content": "\"timestamp\": time.time(),", "analysis": {"category": "false_positive", "reason": "缺乏异步上下文", "confidence": "medium"}}], "uncertain_cases": [{"line": 2152, "content": "stats = _high_performance_logger.get_stats()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 8286, "content": "global_exception_handler.handle_exception(e, context, auto_recover=True)", "analysis": {"category": "uncertain", "reason": "在异步上下文中，需要进一步确认", "confidence": "medium"}}, {"line": 10483, "content": "stats = memory_manager.get_memory_stats()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 10639, "content": "cached_data = data_sync_manager.get_data(data_type, identifier, max_age)", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 10661, "content": "stats = data_sync_manager.get_sync_stats()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 11678, "content": "kline_data = unified_exchange.get_kline_data(symbol, timeframe, limit)", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 13293, "content": "task_name = task.get_name() if hasattr(task, 'get_name') else 'unknown'", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 15680, "content": "perf_summary = perf_monitor.get_performance_summary() if perf_monitor else {}", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 15765, "content": "macd_stats = macd_cache.get_stats()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 15773, "content": "kdj_stats = kdj_cache.get_stats()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 15787, "content": "api_url = exchange_config.get_api_url('OKX', 'time')", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 16449, "content": "current_exchange = utility_manager.get_current_exchange() if 'utility_manager' in globals() else 'OKX'", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 18891, "content": "return unified_exchange.get_kline_data(symbol, timeframe, limit)", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 23472, "content": "current_exchange = unified_exchange.get_current_exchange_name()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 24695, "content": "exchange_name = self.get_current_exchange_name()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 24740, "content": "exchange_name = self.get_current_exchange_name()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 25999, "content": "exchange_name = self.get_current_exchange_name()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 26924, "content": "okx_client = exchange_manager.get_okx_client()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 59233, "content": "cache_stats = global_state.get_cache_statistics()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 59318, "content": "df = unified_exchange.get_kline_data(symbol, timeframe, 50)", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 59559, "content": "current_price = unified_exchange.get_ticker_price(current_config['SYMBOL']) if 'unified_exchange' in globals() and unified_exchange else 0.0", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 59599, "content": "current_price = unified_exchange.get_ticker_price(current_config['SYMBOL']) if 'unified_exchange' in globals() and unified_exchange else 0.0", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 59649, "content": "current_price = unified_exchange.get_ticker_price(current_config['SYMBOL']) if 'unified_exchange' in globals() and unified_exchange else 0.0", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 59735, "content": "current_price = unified_exchange.get_ticker_price(current_config['SYMBOL']) if 'unified_exchange' in globals() and unified_exchange else 0.0", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 59760, "content": "pinbar_signal = global_state.get_shared_data('last_pinbar_signal')", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 59768, "content": "current_price = unified_exchange.get_ticker_price(current_config['SYMBOL']) if 'unified_exchange' in globals() and unified_exchange else 0.0", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 63838, "content": "current_exchange = self.exchange_manager.get_current_exchange_name()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 63841, "content": "client = self.exchange_manager.get_okx_client()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 63845, "content": "client = self.exchange_manager.get_gate_client()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 64717, "content": "self.config['cache_ttl_ticker'] = float(config_manager.get_config_value('optimization', 'cache_ttl_ticker', '1.0'))", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 64718, "content": "self.config['cache_ttl_kline'] = float(config_manager.get_config_value('optimization', 'cache_ttl_kline', '5.0'))", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 64719, "content": "self.config['cache_ttl_balance'] = float(config_manager.get_config_value('optimization', 'cache_ttl_balance', '10.0'))", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 64800, "content": "queue_stats = self.order_queue.get_stats()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 64849, "content": "cache_stats = self.data_cache.get_cache_stats()", "analysis": {"category": "uncertain", "reason": "可能是异步调用，需要进一步确认", "confidence": "medium"}}, {"line": 743, "content": "\"type\": \"LONG\",", "analysis": {"category": "uncertain", "reason": "在异步上下文中，需要进一步确认", "confidence": "medium"}}, {"line": 281, "content": "success_count = sum([", "analysis": {"category": "uncertain", "reason": "在异步上下文中，需要进一步确认", "confidence": "medium"}}]}