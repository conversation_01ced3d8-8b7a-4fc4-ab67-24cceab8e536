#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 WMZC日志输出增强器
为WMZC系统添加更丰富的日志输出
"""

import logging
import threading
import time
from datetime import datetime

class WMZCLogEnhancer:
    """WMZC日志增强器"""
    
    def __init__(self):
        self.gui_callback = None
        self.enhanced_logging = False
        
    def set_gui_callback(self, callback):
        """设置GUI回调"""
        self.gui_callback = callback
        print("✅ 日志增强器GUI回调已设置")
    
    def start_enhanced_logging(self):
        """启动增强日志"""
        if self.enhanced_logging:
            return
        
        self.enhanced_logging = True
        
        # 启动日志增强线程
        threading.Thread(target=self._enhanced_log_worker, daemon=True).start()
        
        self.log("🚀 日志输出增强器已启动", "INFO")
        self.log("💡 将提供更丰富的系统运行信息", "INFO")
    
    def stop_enhanced_logging(self):
        """停止增强日志"""
        self.enhanced_logging = False
        self.log("🛑 日志输出增强器已停止", "INFO")
    
    def log(self, message, level="INFO"):
        """增强日志输出"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {level} - {message}"
        
        # 输出到控制台
        print(formatted_message)
        
        # 输出到GUI（如果可用）
        if self.gui_callback:
            try:
                self.gui_callback(formatted_message, level)
            except Exception as e:
                print(f"GUI日志输出失败: {e}")
    
    def _enhanced_log_worker(self):
        """增强日志工作线程"""
        while self.enhanced_logging:
            try:
                # 每30秒输出一次系统状态
                self.log("📊 系统运行状态正常", "INFO")
                self.log("🔄 监控交易信号和市场数据", "INFO")
                
                time.sleep(30)
                
            except Exception as e:
                self.log(f"日志增强器异常: {e}", "ERROR")
                time.sleep(5)
    
    def log_trading_event(self, event_type, details):
        """记录交易事件"""
        if event_type == "signal":
            self.log(f"📈 交易信号: {details}", "INFO")
        elif event_type == "order":
            self.log(f"📋 订单操作: {details}", "INFO")
        elif event_type == "indicator":
            self.log(f"📊 技术指标: {details}", "INFO")
        elif event_type == "error":
            self.log(f"❌ 交易错误: {details}", "ERROR")
        elif event_type == "warning":
            self.log(f"⚠️ 交易警告: {details}", "WARNING")
        else:
            self.log(f"ℹ️ 交易事件: {details}", "INFO")
    
    def log_system_event(self, event_type, details):
        """记录系统事件"""
        if event_type == "startup":
            self.log(f"🚀 系统启动: {details}", "INFO")
        elif event_type == "shutdown":
            self.log(f"🛑 系统关闭: {details}", "INFO")
        elif event_type == "config":
            self.log(f"⚙️ 配置变更: {details}", "INFO")
        elif event_type == "api":
            self.log(f"🔌 API调用: {details}", "INFO")
        elif event_type == "error":
            self.log(f"❌ 系统错误: {details}", "ERROR")
        else:
            self.log(f"ℹ️ 系统事件: {details}", "INFO")

# 创建全局日志增强器
wmzc_log_enhancer = WMZCLogEnhancer()

def enhance_wmzc_logging(gui_callback=None):
    """启用WMZC日志增强"""
    if gui_callback:
        wmzc_log_enhancer.set_gui_callback(gui_callback)
    wmzc_log_enhancer.start_enhanced_logging()

def log_trading_event(event_type, details):
    """记录交易事件"""
    wmzc_log_enhancer.log_trading_event(event_type, details)

def log_system_event(event_type, details):
    """记录系统事件"""
    wmzc_log_enhancer.log_system_event(event_type, details)

def enhanced_log(message, level="INFO"):
    """增强日志函数"""
    wmzc_log_enhancer.log(message, level)

if __name__ == "__main__":
    # 测试日志增强器
    enhance_wmzc_logging()
    
    # 模拟一些日志输出
    log_system_event("startup", "WMZC交易系统启动")
    log_trading_event("signal", "MACD金叉信号 - BTC-USDT-SWAP")
    log_trading_event("indicator", "RSI: 45.2, KDJ: K=52.1 D=48.3")
    
    time.sleep(2)
    wmzc_log_enhancer.stop_enhanced_logging()
