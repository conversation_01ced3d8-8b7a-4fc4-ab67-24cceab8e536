#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 修复GUI配置加载问题
解决重启后需要重新设置API密钥的问题
"""

import os
import json
import shutil
from datetime import datetime

class GUIConfigLoadingFixer:
    """GUI配置加载修复器"""
    
    def __init__(self):
        self.config_files = ['trading_config.json', 'wmzc_config.json']
        self.wmzc_file = 'WMZC.py'
        self.fixes_applied = 0
        
    def run_gui_config_fix(self):
        """运行GUI配置修复"""
        print("🔧 GUI配置加载问题修复")
        print("=" * 60)
        
        # 1. 备份文件
        self.create_backup()
        
        # 2. 修复GUI变量初始化
        self.fix_gui_variable_initialization()
        
        # 3. 增强配置加载方法
        self.enhance_config_loading_method()
        
        # 4. 确保配置文件有正确的API配置
        self.ensure_correct_api_config()
        
        # 5. 验证修复效果
        self.verify_fix()
        
        return self.fixes_applied > 0
    
    def create_backup(self):
        """创建备份"""
        print("\n💾 创建备份...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = f'gui_config_fix_backup_{timestamp}'
        
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        # 备份WMZC.py
        if os.path.exists(self.wmzc_file):
            shutil.copy2(self.wmzc_file, os.path.join(backup_dir, f'{self.wmzc_file}.backup'))
            print(f"  ✅ 已备份 {self.wmzc_file}")
        
        # 备份配置文件
        for config_file in self.config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, os.path.join(backup_dir, f'{config_file}.backup'))
                print(f"  ✅ 已备份 {config_file}")
    
    def fix_gui_variable_initialization(self):
        """修复GUI变量初始化"""
        print("\n🔧 修复GUI变量初始化...")
        
        if not os.path.exists(self.wmzc_file):
            print("  ❌ WMZC.py 文件不存在")
            return
        
        try:
            with open(self.wmzc_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找API变量初始化的位置
            api_var_patterns = [
                ('self.api_key_var = tk.StringVar()', 'self.api_key_var = tk.StringVar(value="")'),
                ('self.api_secret_var = tk.StringVar()', 'self.api_secret_var = tk.StringVar(value="")'),
                ('self.passphrase_var = tk.StringVar()', 'self.passphrase_var = tk.StringVar(value="")')
            ]
            
            modified = False
            for old_pattern, new_pattern in api_var_patterns:
                if old_pattern in content:
                    content = content.replace(old_pattern, new_pattern)
                    modified = True
                    print(f"  ✅ 修复: {old_pattern}")
            
            if modified:
                with open(self.wmzc_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print("  ✅ GUI变量初始化已修复")
                self.fixes_applied += 1
            else:
                print("  ℹ️ GUI变量初始化无需修复")
                
        except Exception as e:
            print(f"  ❌ 修复GUI变量初始化失败: {e}")
    
    def enhance_config_loading_method(self):
        """增强配置加载方法"""
        print("\n🔧 增强配置加载方法...")
        
        if not os.path.exists(self.wmzc_file):
            print("  ❌ WMZC.py 文件不存在")
            return
        
        try:
            with open(self.wmzc_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找load_config方法的结束位置
            load_config_start = content.find('def load_config(self, config_data=None, section=None):')
            if load_config_start == -1:
                print("  ❌ 未找到load_config方法")
                return
            
            # 查找方法结束位置（下一个def或类结束）
            method_end = content.find('\n    def ', load_config_start + 1)
            if method_end == -1:
                method_end = content.find('\nclass ', load_config_start + 1)
            if method_end == -1:
                method_end = len(content)
            
            # 提取当前方法内容
            current_method = content[load_config_start:method_end]
            
            # 检查是否已经有强制加载逻辑
            if '# 🔧 强制加载API配置到GUI' in current_method:
                print("  ℹ️ 配置加载方法已经增强")
                return
            
            # 在方法末尾添加强制加载逻辑
            enhanced_ending = '''
        # 🔧 强制加载API配置到GUI - 确保配置持久化
        print(f"[配置加载] 🔧 强制加载API配置到GUI变量...")
        
        # 再次确认API配置已正确加载到GUI
        current_api_key = self.api_key_var.get()
        current_api_secret = self.api_secret_var.get()
        current_passphrase = self.passphrase_var.get()
        
        if not current_api_key and api_key:
            print(f"[配置加载] 🔧 强制设置API_KEY: {api_key[:15]}...")
            self.api_key_var.set(api_key)
        
        if not current_api_secret and api_secret:
            print(f"[配置加载] 🔧 强制设置API_SECRET: {api_secret[:15]}...")
            self.api_secret_var.set(api_secret)
        
        if not current_passphrase and passphrase:
            print(f"[配置加载] 🔧 强制设置PASSPHRASE: {passphrase[:15]}...")
            self.passphrase_var.set(passphrase)
        
        # 验证设置结果
        final_api_key = self.api_key_var.get()
        final_api_secret = self.api_secret_var.get()
        final_passphrase = self.passphrase_var.get()
        
        if final_api_key and final_api_secret and final_passphrase:
            print(f"[配置加载] ✅ API配置已成功加载到GUI")
        else:
            print(f"[配置加载] ⚠️ API配置加载不完整:")
            print(f"  API_KEY: {'✅' if final_api_key else '❌'}")
            print(f"  API_SECRET: {'✅' if final_api_secret else '❌'}")
            print(f"  PASSPHRASE: {'✅' if final_passphrase else '❌'}")

        print(f"[配置加载] 📤 配置加载完成")
'''
            
            # 找到方法的最后一行（通常是print语句）
            method_lines = current_method.split('\n')
            insert_position = len(method_lines) - 1
            
            # 在最后一行之前插入增强代码
            method_lines.insert(insert_position, enhanced_ending)
            enhanced_method = '\n'.join(method_lines)
            
            # 替换原方法
            new_content = content[:load_config_start] + enhanced_method + content[method_end:]
            
            with open(self.wmzc_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("  ✅ 配置加载方法已增强")
            self.fixes_applied += 1
            
        except Exception as e:
            print(f"  ❌ 增强配置加载方法失败: {e}")
    
    def ensure_correct_api_config(self):
        """确保配置文件有正确的API配置"""
        print("\n🔧 确保配置文件有正确的API配置...")
        
        # 检查当前配置文件中的API配置
        current_api_config = {}
        
        # 从wmzc_config.json读取API配置
        if os.path.exists('wmzc_config.json'):
            try:
                with open('wmzc_config.json', 'r', encoding='utf-8') as f:
                    wmzc_config = json.load(f)
                
                api_key = wmzc_config.get('okx_api_key', '')
                api_secret = wmzc_config.get('okx_secret_key', '')
                passphrase = wmzc_config.get('okx_passphrase', '')
                
                if api_key and api_secret and passphrase:
                    current_api_config = {
                        'API_KEY': api_key,
                        'API_SECRET': api_secret,
                        'PASSPHRASE': passphrase
                    }
                    print(f"  ✅ 从wmzc_config.json读取到API配置: {api_key[:15]}...")
                
            except Exception as e:
                print(f"  ❌ 读取wmzc_config.json失败: {e}")
        
        # 如果有API配置，确保同步到trading_config.json
        if current_api_config:
            try:
                if os.path.exists('trading_config.json'):
                    with open('trading_config.json', 'r', encoding='utf-8') as f:
                        trading_config = json.load(f)
                else:
                    trading_config = {}
                
                # 更新API配置
                trading_config.update(current_api_config)
                trading_config['OKX_API_KEY'] = current_api_config['API_KEY']
                trading_config['OKX_SECRET_KEY'] = current_api_config['API_SECRET']
                trading_config['OKX_PASSPHRASE'] = current_api_config['PASSPHRASE']
                
                # 添加强化保护标记
                trading_config.update({
                    '_CONFIG_PROTECTED': True,
                    '_GUI_CONFIG_FIX_APPLIED': datetime.now().isoformat(),
                    '_DO_NOT_OVERRIDE': True
                })
                
                with open('trading_config.json', 'w', encoding='utf-8') as f:
                    json.dump(trading_config, f, indent=2, ensure_ascii=False)
                
                print("  ✅ API配置已同步到trading_config.json")
                self.fixes_applied += 1
                
            except Exception as e:
                print(f"  ❌ 同步API配置到trading_config.json失败: {e}")
        else:
            print("  ⚠️ 未找到有效的API配置")
    
    def verify_fix(self):
        """验证修复效果"""
        print("\n✅ 验证修复效果...")
        
        # 检查配置文件
        for config_file in self.config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    if config_file == 'trading_config.json':
                        has_api = (config.get('API_KEY') and 
                                  config.get('API_SECRET') and 
                                  config.get('PASSPHRASE'))
                    else:
                        has_api = (config.get('okx_api_key') and 
                                  config.get('okx_secret_key') and 
                                  config.get('okx_passphrase'))
                    
                    if has_api:
                        print(f"  ✅ {config_file}: API配置验证成功")
                    else:
                        print(f"  ❌ {config_file}: API配置验证失败")
                        
                except Exception as e:
                    print(f"  ❌ 验证 {config_file} 失败: {e}")
        
        # 检查WMZC.py修改
        if os.path.exists(self.wmzc_file):
            try:
                with open(self.wmzc_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if '# 🔧 强制加载API配置到GUI' in content:
                    print("  ✅ WMZC.py: 配置加载增强验证成功")
                else:
                    print("  ❌ WMZC.py: 配置加载增强验证失败")
                    
            except Exception as e:
                print(f"  ❌ 验证WMZC.py失败: {e}")

def main():
    """主函数"""
    print("🔧 GUI配置加载问题修复工具")
    print("=" * 60)
    
    fixer = GUIConfigLoadingFixer()
    
    try:
        success = fixer.run_gui_config_fix()
        
        print("\n" + "=" * 60)
        if success:
            print(f"🎉 GUI配置加载修复完成！共应用了 {fixer.fixes_applied} 个修复")
            print("\n💡 修复内容:")
            print("  1. ✅ 修复了GUI变量初始化")
            print("  2. ✅ 增强了配置加载方法")
            print("  3. ✅ 确保了API配置同步")
            print("  4. ✅ 添加了强制加载逻辑")
            
            print("\n🚀 现在请:")
            print("  1. 重新启动WMZC系统")
            print("  2. 检查API配置是否自动加载")
            print("  3. 如果仍为空，手动输入一次API密钥")
            print("  4. 保存配置后重启验证")
            
        else:
            print("❌ GUI配置加载修复失败")
            print("💡 请检查错误信息并手动修复")
        
        return success
        
    except Exception as e:
        print(f"❌ 修复过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
