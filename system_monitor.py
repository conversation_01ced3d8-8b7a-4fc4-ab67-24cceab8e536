#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC系统监控和性能分析工具
实时监控系统状态、性能指标和交易统计
"""

import asyncio
import sys
import os
import time
import json
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append('.')

# 导入WMZC模块
try:
    import WMZC
    print("✅ WMZC模块导入成功")
except ImportError as e:
    print(f"❌ WMZC模块导入失败: {e}")
    sys.exit(1)

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.metrics = {
            'api_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'hold_signals': 0,
            'strategy_executions': 0,
            'risk_checks': 0,
            'risk_violations': 0
        }
        self.performance_log = []
        
    def log_api_call(self, success: bool = True):
        """记录API调用"""
        self.metrics['api_calls'] += 1
        if success:
            self.metrics['successful_calls'] += 1
        else:
            self.metrics['failed_calls'] += 1
    
    def log_signal(self, signal: str):
        """记录交易信号"""
        self.metrics['total_signals'] += 1
        if signal == 'BUY':
            self.metrics['buy_signals'] += 1
        elif signal == 'SELL':
            self.metrics['sell_signals'] += 1
        else:
            self.metrics['hold_signals'] += 1
    
    def log_strategy_execution(self):
        """记录策略执行"""
        self.metrics['strategy_executions'] += 1
    
    def log_risk_check(self, violation: bool = False):
        """记录风险检查"""
        self.metrics['risk_checks'] += 1
        if violation:
            self.metrics['risk_violations'] += 1
    
    def log_performance(self, operation: str, duration: float):
        """记录性能数据"""
        self.performance_log.append({
            'timestamp': time.time(),
            'operation': operation,
            'duration': duration
        })
        
        # 保持最近1000条记录
        if len(self.performance_log) > 1000:
            self.performance_log = self.performance_log[-1000:]
    
    def get_uptime(self) -> str:
        """获取运行时间"""
        uptime_seconds = time.time() - self.start_time
        hours = int(uptime_seconds // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        seconds = int(uptime_seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def get_api_success_rate(self) -> float:
        """获取API成功率"""
        if self.metrics['api_calls'] == 0:
            return 0.0
        return self.metrics['successful_calls'] / self.metrics['api_calls'] * 100
    
    def get_signal_distribution(self) -> dict:
        """获取信号分布"""
        total = self.metrics['total_signals']
        if total == 0:
            return {'BUY': 0, 'SELL': 0, 'HOLD': 0}
        
        return {
            'BUY': self.metrics['buy_signals'] / total * 100,
            'SELL': self.metrics['sell_signals'] / total * 100,
            'HOLD': self.metrics['hold_signals'] / total * 100
        }
    
    def get_performance_stats(self) -> dict:
        """获取性能统计"""
        if not self.performance_log:
            return {}
        
        durations = [entry['duration'] for entry in self.performance_log]
        
        return {
            'avg_duration': sum(durations) / len(durations),
            'min_duration': min(durations),
            'max_duration': max(durations),
            'total_operations': len(durations)
        }
    
    def generate_report(self) -> str:
        """生成监控报告"""
        uptime = self.get_uptime()
        api_success_rate = self.get_api_success_rate()
        signal_dist = self.get_signal_distribution()
        perf_stats = self.get_performance_stats()
        
        report = f"""
📊 WMZC系统监控报告
{'='*50}
⏰ 运行时间: {uptime}
📡 API调用统计:
   总调用次数: {self.metrics['api_calls']}
   成功次数: {self.metrics['successful_calls']}
   失败次数: {self.metrics['failed_calls']}
   成功率: {api_success_rate:.1f}%

🎯 交易信号统计:
   总信号数: {self.metrics['total_signals']}
   买入信号: {self.metrics['buy_signals']} ({signal_dist['BUY']:.1f}%)
   卖出信号: {self.metrics['sell_signals']} ({signal_dist['SELL']:.1f}%)
   持有信号: {self.metrics['hold_signals']} ({signal_dist['HOLD']:.1f}%)

🔄 策略执行统计:
   策略执行次数: {self.metrics['strategy_executions']}

🛡️ 风险管理统计:
   风险检查次数: {self.metrics['risk_checks']}
   风险违规次数: {self.metrics['risk_violations']}
   风险合规率: {(1 - self.metrics['risk_violations'] / max(self.metrics['risk_checks'], 1)) * 100:.1f}%
"""
        
        if perf_stats:
            report += f"""
⚡ 性能统计:
   平均响应时间: {perf_stats['avg_duration']:.3f}秒
   最快响应时间: {perf_stats['min_duration']:.3f}秒
   最慢响应时间: {perf_stats['max_duration']:.3f}秒
   总操作次数: {perf_stats['total_operations']}
"""
        
        report += f"\n{'='*50}"
        return report

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self, monitor: SystemMonitor):
        self.monitor = monitor
    
    async def profile_api_performance(self, api_tester):
        """分析API性能"""
        print("📊 分析API性能...")
        
        operations = [
            ("账户余额查询", lambda: api_tester._make_request("GET", "/api/v4/spot/accounts", auth_required=True)),
            ("价格查询", lambda: api_tester._make_request("GET", "/api/v4/spot/tickers", params={"currency_pair": "BTC_USDT"})),
            ("K线数据查询", lambda: api_tester._make_request("GET", "/api/v4/spot/candlesticks", params={"currency_pair": "BTC_USDT", "interval": "1m", "limit": 10}))
        ]
        
        for name, operation in operations:
            start_time = time.time()
            try:
                await operation()
                duration = time.time() - start_time
                self.monitor.log_api_call(True)
                self.monitor.log_performance(name, duration)
                print(f"   ✅ {name}: {duration:.3f}秒")
            except Exception as e:
                duration = time.time() - start_time
                self.monitor.log_api_call(False)
                self.monitor.log_performance(name, duration)
                print(f"   ❌ {name}: {duration:.3f}秒 (失败: {e})")
    
    async def profile_strategy_performance(self, strategy_manager):
        """分析策略性能"""
        print("🎯 分析策略性能...")
        
        start_time = time.time()
        try:
            result = await strategy_manager.run_all_strategies()
            duration = time.time() - start_time
            
            self.monitor.log_strategy_execution()
            self.monitor.log_signal(result.get('final_signal', 'HOLD'))
            self.monitor.log_performance("多策略分析", duration)
            
            print(f"   ✅ 多策略分析: {duration:.3f}秒")
            print(f"   🎯 信号: {result.get('final_signal')} (置信度: {result.get('final_confidence', 0):.2f})")
            
        except Exception as e:
            duration = time.time() - start_time
            self.monitor.log_performance("多策略分析", duration)
            print(f"   ❌ 多策略分析: {duration:.3f}秒 (失败: {e})")
    
    async def profile_risk_management(self, risk_manager):
        """分析风险管理性能"""
        print("🛡️ 分析风险管理性能...")
        
        # 模拟风险检查
        checks = [
            ("仓位大小检查", lambda: risk_manager.check_position_size(100)),
            ("日损失检查", lambda: risk_manager.check_daily_loss_limit()),
            ("回撤检查", lambda: risk_manager.check_drawdown_limit()),
            ("交易频率检查", lambda: risk_manager.check_trade_frequency())
        ]
        
        for name, check_func in checks:
            start_time = time.time()
            try:
                result = check_func()
                duration = time.time() - start_time
                
                self.monitor.log_risk_check(not result)  # 如果检查失败，记录为违规
                self.monitor.log_performance(name, duration)
                
                print(f"   {'✅' if result else '❌'} {name}: {duration:.6f}秒")
                
            except Exception as e:
                duration = time.time() - start_time
                self.monitor.log_performance(name, duration)
                print(f"   💥 {name}: {duration:.6f}秒 (异常: {e})")

async def run_system_monitoring():
    """运行系统监控"""
    print("=" * 80)
    print("📊 WMZC系统监控和性能分析")
    print("=" * 80)
    
    # 初始化组件
    monitor = SystemMonitor()
    
    # Gate.io API 凭证
    API_KEY = "d5ea5faa068d66204bb68b75201c56d5"
    SECRET_KEY = "5b516e55788fba27e61f9bd06b22ab3661b3115797076d5e73199bea3a8afb1c"
    
    # 创建系统组件
    api_tester = WMZC.GateIOAPITester(API_KEY, SECRET_KEY)
    indicator_calculator = WMZC.TechnicalIndicatorCalculator(api_tester)
    risk_manager = WMZC.AdvancedRiskManager(1000.0)
    strategy_manager = WMZC.AdvancedStrategyManager(indicator_calculator, risk_manager)
    
    # 创建性能分析器
    profiler = PerformanceProfiler(monitor)
    
    print("🔧 初始化系统组件完成")
    
    # 预加载数据
    print("\n📊 预加载市场数据...")
    await indicator_calculator.fetch_kline_data("BTC_USDT", "1m", 100)
    
    # 运行性能分析
    print("\n🚀 开始性能分析...")
    
    # 多轮测试以获得更准确的性能数据
    for round_num in range(1, 4):
        print(f"\n📊 第 {round_num} 轮性能测试:")
        
        # API性能测试
        await profiler.profile_api_performance(api_tester)
        
        # 策略性能测试
        await profiler.profile_strategy_performance(strategy_manager)
        
        # 风险管理性能测试
        await profiler.profile_risk_management(risk_manager)
        
        # 等待一下
        await asyncio.sleep(1)
    
    # 生成最终报告
    print("\n" + "=" * 80)
    print("📋 最终监控报告")
    print("=" * 80)
    
    report = monitor.generate_report()
    print(report)
    
    # 保存报告到文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"wmzc_monitor_report_{timestamp}.txt"
    
    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(f"WMZC系统监控报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(report)
        
        print(f"\n💾 监控报告已保存到: {report_filename}")
        
    except Exception as e:
        print(f"\n⚠️ 保存报告失败: {e}")
    
    # 性能建议
    perf_stats = monitor.get_performance_stats()
    if perf_stats:
        avg_duration = perf_stats['avg_duration']
        
        print(f"\n💡 性能建议:")
        if avg_duration < 0.1:
            print("   🚀 系统性能优秀！响应时间很快")
        elif avg_duration < 0.5:
            print("   ✅ 系统性能良好，响应时间正常")
        elif avg_duration < 1.0:
            print("   ⚠️ 系统性能一般，建议优化网络连接")
        else:
            print("   🐌 系统性能较慢，建议检查网络和服务器状态")
    
    api_success_rate = monitor.get_api_success_rate()
    if api_success_rate < 95:
        print("   ⚠️ API成功率偏低，建议检查网络连接和API密钥")
    else:
        print("   ✅ API连接稳定可靠")
    
    print("\n🎉 系统监控完成！")

if __name__ == "__main__":
    try:
        asyncio.run(run_system_monitoring())
    except KeyboardInterrupt:
        print("\n⏹️ 监控被用户中断")
    except Exception as e:
        print(f"\n💥 监控异常: {e}")
        import traceback
        traceback.print_exc()
