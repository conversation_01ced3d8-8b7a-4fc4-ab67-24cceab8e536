#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC系统DataFrame歧义性错误最终修复验证
验证所有DataFrame布尔值判断问题是否已完全解决
"""

import sys
import os
import re
import traceback

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dangerous_dataframe_patterns():
    """检查危险的DataFrame模式"""
    print("🔍 检查危险的DataFrame模式...")
    
    wmzc_file = "WMZC.py"
    if not os.path.exists(wmzc_file):
        print("❌ WMZC.py文件不存在")
        return False
    
    with open(wmzc_file, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    # 检查危险模式
    dangerous_patterns = [
        (r'if\s+not\s+\w+\.empty\s*:', "直接使用.empty属性"),
        (r'if\s+\w+\.empty\s*:', "直接使用.empty属性"),
        (r'if\s+not\s+\w+\.empty\s+and', "直接使用.empty属性在条件中"),
        (r'if\s+\w+\.empty\s+and', "直接使用.empty属性在条件中"),
        (r'if\s+\w+\s*:\s*#.*[Dd]ata[Ff]rame', "可能的DataFrame布尔判断"),
    ]
    
    total_issues = 0
    for i, line in enumerate(lines, 1):
        for pattern, description in dangerous_patterns:
            if re.search(pattern, line):
                # 排除注释和修复后的安全代码
                if not ('🔧' in line or '_safe_dataframe_check' in line or 'bool(df.empty)' in line):
                    print(f"  ❌ 行 {i}: {description}")
                    print(f"     代码: {line.strip()}")
                    total_issues += 1
    
    if total_issues == 0:
        print("✅ 未发现危险的DataFrame模式")
        return True
    else:
        print(f"❌ 发现 {total_issues} 个潜在的危险DataFrame模式")
        return False

def check_safe_dataframe_usage():
    """检查安全的DataFrame使用"""
    print("\n🔍 检查安全的DataFrame使用...")
    
    wmzc_file = "WMZC.py"
    with open(wmzc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查_safe_dataframe_check的使用
    safe_check_count = len(re.findall(r'_safe_dataframe_check\(', content))
    print(f"✅ _safe_dataframe_check函数使用了 {safe_check_count} 次")
    
    # 检查修复注释
    fix_comments = len(re.findall(r'# 🔧.*DataFrame.*歧义', content))
    print(f"✅ 找到 {fix_comments} 个DataFrame歧义修复注释")
    
    # 检查bool()显式转换
    bool_conversions = len(re.findall(r'bool\(.*\.empty\)', content))
    print(f"✅ 找到 {bool_conversions} 个安全的bool()转换")
    
    return safe_check_count > 0

def test_dataframe_operations():
    """测试DataFrame操作"""
    print("\n🔍 测试DataFrame操作...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # 创建测试DataFrame
        test_data = {
            'close': [100, 101, 102, 103, 104, 105],
            'high': [101, 102, 103, 104, 105, 106],
            'low': [99, 100, 101, 102, 103, 104],
            'volume': [1000, 1100, 1200, 1300, 1400, 1500]
        }
        df = pd.DataFrame(test_data)
        
        print(f"  ✅ 测试DataFrame创建成功，包含 {len(df)} 行数据")
        
        # 测试空DataFrame
        empty_df = pd.DataFrame()
        print(f"  ✅ 空DataFrame创建成功")
        
        # 测试_safe_dataframe_check函数
        try:
            import WMZC
            if hasattr(WMZC, '_safe_dataframe_check'):
                # 测试非空DataFrame
                result1 = WMZC._safe_dataframe_check(df)
                print(f"  ✅ 非空DataFrame检查: {result1}")
                
                # 测试空DataFrame
                result2 = WMZC._safe_dataframe_check(empty_df)
                print(f"  ✅ 空DataFrame检查: {result2}")
                
                # 测试None
                result3 = WMZC._safe_dataframe_check(None)
                print(f"  ✅ None检查: {result3}")
                
                return True
            else:
                print("  ❌ _safe_dataframe_check函数不存在")
                return False
        except Exception as e:
            print(f"  ❌ _safe_dataframe_check函数测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ DataFrame操作测试失败: {e}")
        traceback.print_exc()
        return False

def test_technical_indicators():
    """测试技术指标计算"""
    print("\n🔍 测试技术指标计算...")
    
    try:
        import pandas as pd
        import WMZC
        
        # 创建测试数据
        test_data = []
        for i in range(50):
            test_data.append([
                1000000000 + i * 60000,  # timestamp
                100 + i * 0.1,           # open
                105 + i * 0.1,           # high
                95 + i * 0.1,            # low
                103 + i * 0.1,           # close
                1000 + i * 10            # volume
            ])
        
        # 转换为DataFrame
        df = pd.DataFrame(test_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        
        print(f"  ✅ 测试数据创建成功，包含 {len(df)} 行")
        
        # 测试MACD计算
        try:
            macd_result = WMZC.calculate_macd(df)
            if WMZC._safe_dataframe_check(macd_result):
                print(f"  ✅ MACD计算成功，结果包含 {len(macd_result)} 行")
            else:
                print(f"  ⚠️ MACD计算返回空结果")
        except Exception as e:
            if "ambiguous" in str(e).lower():
                print(f"  ❌ MACD计算仍有DataFrame歧义错误: {e}")
                return False
            else:
                print(f"  ⚠️ MACD计算其他错误: {e}")
        
        # 测试RSI计算
        try:
            rsi_result = WMZC.calculate_rsi(df)
            if WMZC._safe_dataframe_check(rsi_result):
                print(f"  ✅ RSI计算成功，结果包含 {len(rsi_result)} 行")
            else:
                print(f"  ⚠️ RSI计算返回空结果")
        except Exception as e:
            if "ambiguous" in str(e).lower():
                print(f"  ❌ RSI计算仍有DataFrame歧义错误: {e}")
                return False
            else:
                print(f"  ⚠️ RSI计算其他错误: {e}")
        
        # 测试KDJ计算
        try:
            kdj_result = WMZC.calculate_kdj(df)
            if WMZC._safe_dataframe_check(kdj_result):
                print(f"  ✅ KDJ计算成功，结果包含 {len(kdj_result)} 行")
            else:
                print(f"  ⚠️ KDJ计算返回空结果")
        except Exception as e:
            if "ambiguous" in str(e).lower():
                print(f"  ❌ KDJ计算仍有DataFrame歧义错误: {e}")
                return False
            else:
                print(f"  ⚠️ KDJ计算其他错误: {e}")
        
        print("  🎉 所有技术指标计算测试通过，无DataFrame歧义错误")
        return True
        
    except Exception as e:
        print(f"❌ 技术指标计算测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主验证函数"""
    print("=" * 80)
    print("🔧 WMZC系统DataFrame歧义性错误最终修复验证")
    print("=" * 80)
    print("🎯 目标：确认所有DataFrame布尔值判断问题已完全解决")
    print()
    
    # 执行所有检查
    checks = [
        ("危险DataFrame模式检查", check_dangerous_dataframe_patterns),
        ("安全DataFrame使用检查", check_safe_dataframe_usage),
        ("DataFrame操作测试", test_dataframe_operations),
        ("技术指标计算测试", test_technical_indicators),
    ]
    
    results = {}
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
        except Exception as e:
            print(f"❌ 检查 {check_name} 执行异常: {e}")
            results[check_name] = False
    
    # 生成总结报告
    print("\n" + "=" * 80)
    print("📊 DataFrame歧义性错误修复验证结果")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} - {check_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个检查通过")
    
    if passed == total:
        print("🎉 DataFrame歧义性错误修复验证完全通过！")
        print("\n📋 修复成果:")
        print("✅ 所有危险的DataFrame布尔值判断已修复")
        print("✅ _safe_dataframe_check函数正常工作")
        print("✅ 技术指标计算无歧义错误")
        print("✅ DataFrame操作安全可靠")
        print("\n🎯 预期效果：")
        print("💡 MACD、RSI、KDJ信号获取不再出现歧义错误")
        print("💡 系统运行更加稳定可靠")
        print("💡 技术指标计算正常工作")
        return True
    else:
        print("⚠️ 部分检查失败，DataFrame歧义性问题可能仍然存在")
        return False

if __name__ == "__main__":
    success = main()
    print("\n按回车键退出...")
    input()
    sys.exit(0 if success else 1)
