#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试导入是否正常
"""

def test_basic_imports():
    """测试基本导入"""
    try:
        import time
        print("✅ time 导入成功")
        
        import threading
        print("✅ threading 导入成功")
        
        import asyncio
        print("✅ asyncio 导入成功")
        
        import functools
        print("✅ functools 导入成功")
        
        import os
        print("✅ os 导入成功")
        
        import sys
        print("✅ sys 导入成功")
        
        import gc
        print("✅ gc 导入成功")
        
        import re
        print("✅ re 导入成功")
        
        import hashlib
        print("✅ hashlib 导入成功")
        
        import pickle
        print("✅ pickle 导入成功")
        
        import json
        print("✅ json 导入成功")
        
        import csv
        print("✅ csv 导入成功")
        
        import webbrowser
        print("✅ webbrowser 导入成功")
        
        from typing import Any, Dict, List, Optional, Union
        print("✅ typing 导入成功")
        
        from datetime import datetime, timezone, timedelta
        print("✅ datetime 导入成功")
        
        from collections import defaultdict, deque
        print("✅ collections 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本导入失败: {e}")
        return False

def test_tkinter_imports():
    """测试tkinter导入"""
    try:
        import tkinter as tk
        print("✅ tkinter 导入成功")
        
        from tkinter import messagebox, ttk, simpledialog, filedialog
        print("✅ tkinter 子模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ tkinter 导入失败: {e}")
        return False

def test_wmzc_import():
    """测试WMZC模块导入"""
    try:
        print("🔄 开始导入WMZC模块...")
        import WMZC
        print("✅ WMZC 模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ WMZC 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 导入测试开始")
    print("=" * 50)
    
    # 测试基本导入
    print("\n📦 测试基本导入...")
    basic_ok = test_basic_imports()
    
    # 测试tkinter导入
    print("\n🖥️ 测试GUI导入...")
    tkinter_ok = test_tkinter_imports()
    
    # 测试WMZC导入
    print("\n🎯 测试WMZC模块导入...")
    wmzc_ok = test_wmzc_import()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"基本导入: {'✅ 成功' if basic_ok else '❌ 失败'}")
    print(f"GUI导入: {'✅ 成功' if tkinter_ok else '❌ 失败'}")
    print(f"WMZC导入: {'✅ 成功' if wmzc_ok else '❌ 失败'}")
    
    if all([basic_ok, tkinter_ok, wmzc_ok]):
        print("\n🎉 所有导入测试通过！")
    else:
        print("\n⚠️ 部分导入测试失败，请检查错误信息")
    
    print("=" * 50)
    input("按回车键退出...")
