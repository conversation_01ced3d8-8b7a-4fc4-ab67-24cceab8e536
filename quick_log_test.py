#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速WMZC日志测试
"""

print("🧪 快速WMZC日志测试")
print("=" * 40)

try:
    print("📦 导入WMZC模块...")
    import WMZC
    print("✅ WMZC模块导入成功")
    
    # 测试全局log函数
    if hasattr(WMZC, 'log'):
        print("✅ 找到全局log函数")
        
        # 发送各种测试日志
        WMZC.log("🧪 这是一条测试日志", "INFO")
        WMZC.log("📡 模拟K线数据获取 - BTC-USDT-SWAP", "INFO")
        WMZC.log("💰 当前价格: $98,456.78", "INFO")
        WMZC.log("📈 模拟MACD计算: DIF=0.123456, DEA=0.098765", "INFO")
        WMZC.log("📊 模拟KDJ计算: K=45.2, D=42.1, J=51.4", "INFO")
        WMZC.log("📉 模拟RSI计算: RSI=52.3", "INFO")
        WMZC.log("🚨 模拟交易信号: MACD金叉", "WARNING")
        WMZC.log("🎯 信号强度: 85%", "INFO")
        
        print("✅ 测试日志已发送")
    else:
        print("❌ 未找到全局log函数")
    
    # 检查app变量
    if hasattr(WMZC, 'app') and WMZC.app:
        print("✅ 找到app变量")
        if hasattr(WMZC.app, 'add_log_message'):
            print("✅ 找到GUI日志方法")
            WMZC.app.add_log_message("🧪 直接GUI测试日志", "INFO")
        else:
            print("❌ 未找到GUI日志方法")
    else:
        print("❌ 未找到app变量或app为None")
    
    print("🎉 测试完成")
    print("💡 请检查WMZC日志控制台是否显示了测试日志")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
