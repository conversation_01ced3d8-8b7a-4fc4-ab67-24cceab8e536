#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate.io API优化GUI配置界面
提供用户友好的界面来配置和监控Gate.io API优化功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import time
import threading
from typing import Dict, Any

class GateIOOptimizationGUI:
    """Gate.io API优化GUI配置界面"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.window = None
        self.optimizer = None
        self.config = {}
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # GUI变量
        self.vars = {}
        self.widgets = {}
        
        self.create_gui()
        self.load_config()
    
    def create_gui(self):
        """创建GUI界面"""
        if self.parent:
            self.window = tk.Toplevel(self.parent)
        else:
            self.window = tk.Tk()
        
        self.window.title("Gate.io API优化配置")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 标签页1: 基础配置
        self.create_basic_config_tab(notebook)
        
        # 标签页2: 缓存配置
        self.create_cache_config_tab(notebook)
        
        # 标签页3: 连接配置
        self.create_connection_config_tab(notebook)
        
        # 标签页4: 安全配置
        self.create_security_config_tab(notebook)
        
        # 标签页5: 性能监控
        self.create_monitoring_tab(notebook)
        
        # 底部按钮
        self.create_bottom_buttons(main_frame)
    
    def create_basic_config_tab(self, notebook):
        """创建基础配置标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="基础配置")
        
        # 订单管理配置
        order_frame = ttk.LabelFrame(frame, text="订单管理优化", padding=10)
        order_frame.pack(fill=tk.X, pady=5)
        
        self.vars['enable_order_batching'] = tk.BooleanVar(value=True)
        ttk.Checkbutton(order_frame, text="启用智能订单队列", 
                       variable=self.vars['enable_order_batching']).pack(anchor=tk.W)
        
        # 批量间隔设置
        batch_frame = ttk.Frame(order_frame)
        batch_frame.pack(fill=tk.X, pady=5)
        ttk.Label(batch_frame, text="批量间隔(毫秒):").pack(side=tk.LEFT)
        self.vars['batch_interval'] = tk.DoubleVar(value=100.0)
        batch_scale = ttk.Scale(batch_frame, from_=50, to=500, 
                               variable=self.vars['batch_interval'], orient=tk.HORIZONTAL)
        batch_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.widgets['batch_interval_label'] = ttk.Label(batch_frame, text="100ms")
        self.widgets['batch_interval_label'].pack(side=tk.LEFT)
        
        # 绑定更新事件
        batch_scale.configure(command=self.update_batch_interval_label)
        
        # 最大批量大小
        size_frame = ttk.Frame(order_frame)
        size_frame.pack(fill=tk.X, pady=5)
        ttk.Label(size_frame, text="最大批量大小:").pack(side=tk.LEFT)
        self.vars['max_batch_size'] = tk.IntVar(value=20)
        ttk.Spinbox(size_frame, from_=5, to=50, textvariable=self.vars['max_batch_size'], 
                   width=10).pack(side=tk.LEFT, padx=5)
        
        # 并发控制配置
        concurrent_frame = ttk.LabelFrame(frame, text="并发控制", padding=10)
        concurrent_frame.pack(fill=tk.X, pady=5)
        
        concurrent_req_frame = ttk.Frame(concurrent_frame)
        concurrent_req_frame.pack(fill=tk.X, pady=5)
        ttk.Label(concurrent_req_frame, text="最大并发请求数:").pack(side=tk.LEFT)
        self.vars['max_concurrent_requests'] = tk.IntVar(value=20)
        ttk.Spinbox(concurrent_req_frame, from_=5, to=50, 
                   textvariable=self.vars['max_concurrent_requests'], width=10).pack(side=tk.LEFT, padx=5)
    
    def create_cache_config_tab(self, notebook):
        """创建缓存配置标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="缓存配置")
        
        # 启用缓存
        self.vars['enable_data_caching'] = tk.BooleanVar(value=True)
        ttk.Checkbutton(frame, text="启用数据缓存", 
                       variable=self.vars['enable_data_caching']).pack(anchor=tk.W, pady=5)
        
        # 缓存TTL配置
        cache_types = [
            ('ticker', 'Ticker数据', 1.0, 0.1, 10.0),
            ('kline', 'K线数据', 5.0, 1.0, 60.0),
            ('balance', '账户余额', 10.0, 5.0, 300.0),
            ('orderbook', '订单簿', 0.5, 0.1, 5.0),
            ('trades', '成交记录', 2.0, 0.5, 30.0)
        ]
        
        for cache_type, label, default, min_val, max_val in cache_types:
            cache_frame = ttk.LabelFrame(frame, text=f"{label}缓存设置", padding=10)
            cache_frame.pack(fill=tk.X, pady=5)
            
            ttl_frame = ttk.Frame(cache_frame)
            ttl_frame.pack(fill=tk.X)
            
            ttk.Label(ttl_frame, text="过期时间(秒):").pack(side=tk.LEFT)
            var_name = f'cache_ttl_{cache_type}'
            self.vars[var_name] = tk.DoubleVar(value=default)
            
            ttl_scale = ttk.Scale(ttl_frame, from_=min_val, to=max_val,
                                 variable=self.vars[var_name], orient=tk.HORIZONTAL)
            ttl_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
            
            label_name = f'{cache_type}_ttl_label'
            self.widgets[label_name] = ttk.Label(ttl_frame, text=f"{default}s")
            self.widgets[label_name].pack(side=tk.LEFT)
            
            # 绑定更新事件
            ttl_scale.configure(command=lambda val, ct=cache_type: self.update_ttl_label(ct, val))
    
    def create_connection_config_tab(self, notebook):
        """创建连接配置标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="连接配置")
        
        # HTTP连接池配置
        http_frame = ttk.LabelFrame(frame, text="HTTP连接池", padding=10)
        http_frame.pack(fill=tk.X, pady=5)
        
        self.vars['enable_connection_pooling'] = tk.BooleanVar(value=True)
        ttk.Checkbutton(http_frame, text="启用连接池", 
                       variable=self.vars['enable_connection_pooling']).pack(anchor=tk.W)
        
        # 最大连接数
        conn_frame = ttk.Frame(http_frame)
        conn_frame.pack(fill=tk.X, pady=5)
        ttk.Label(conn_frame, text="最大连接数:").pack(side=tk.LEFT)
        self.vars['max_connections'] = tk.IntVar(value=5)
        ttk.Spinbox(conn_frame, from_=1, to=20, textvariable=self.vars['max_connections'], 
                   width=10).pack(side=tk.LEFT, padx=5)
        
        # 连接超时
        timeout_frame = ttk.Frame(http_frame)
        timeout_frame.pack(fill=tk.X, pady=5)
        ttk.Label(timeout_frame, text="连接超时(秒):").pack(side=tk.LEFT)
        self.vars['connection_timeout'] = tk.DoubleVar(value=30.0)
        ttk.Spinbox(timeout_frame, from_=5.0, to=120.0, increment=5.0,
                   textvariable=self.vars['connection_timeout'], width=10).pack(side=tk.LEFT, padx=5)
        
        # WebSocket配置
        ws_frame = ttk.LabelFrame(frame, text="WebSocket配置", padding=10)
        ws_frame.pack(fill=tk.X, pady=5)
        
        self.vars['enable_websocket_balancing'] = tk.BooleanVar(value=True)
        ttk.Checkbutton(ws_frame, text="启用WebSocket负载均衡", 
                       variable=self.vars['enable_websocket_balancing']).pack(anchor=tk.W)
        
        # WebSocket连接数
        ws_conn_frame = ttk.Frame(ws_frame)
        ws_conn_frame.pack(fill=tk.X, pady=5)
        ttk.Label(ws_conn_frame, text="最大WebSocket连接数:").pack(side=tk.LEFT)
        self.vars['max_websocket_connections'] = tk.IntVar(value=3)
        ttk.Spinbox(ws_conn_frame, from_=1, to=10, 
                   textvariable=self.vars['max_websocket_connections'], width=10).pack(side=tk.LEFT, padx=5)
    
    def create_security_config_tab(self, notebook):
        """创建安全配置标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="安全配置")
        
        # 安全增强
        security_frame = ttk.LabelFrame(frame, text="安全增强", padding=10)
        security_frame.pack(fill=tk.X, pady=5)
        
        self.vars['enable_security_enhancement'] = tk.BooleanVar(value=True)
        ttk.Checkbutton(security_frame, text="启用安全增强", 
                       variable=self.vars['enable_security_enhancement']).pack(anchor=tk.W)
        
        # 时间戳容忍度
        timestamp_frame = ttk.Frame(security_frame)
        timestamp_frame.pack(fill=tk.X, pady=5)
        ttk.Label(timestamp_frame, text="时间戳容忍度(秒):").pack(side=tk.LEFT)
        self.vars['timestamp_tolerance'] = tk.DoubleVar(value=30.0)
        ttk.Spinbox(timestamp_frame, from_=10.0, to=120.0, increment=10.0,
                   textvariable=self.vars['timestamp_tolerance'], width=10).pack(side=tk.LEFT, padx=5)
        
        # 告警阈值配置
        alert_frame = ttk.LabelFrame(frame, text="告警阈值", padding=10)
        alert_frame.pack(fill=tk.X, pady=5)
        
        # 最大失败率
        failure_frame = ttk.Frame(alert_frame)
        failure_frame.pack(fill=tk.X, pady=5)
        ttk.Label(failure_frame, text="最大失败率(%):").pack(side=tk.LEFT)
        self.vars['max_failure_rate'] = tk.DoubleVar(value=5.0)
        ttk.Spinbox(failure_frame, from_=1.0, to=20.0, increment=1.0,
                   textvariable=self.vars['max_failure_rate'], width=10).pack(side=tk.LEFT, padx=5)
        
        # 最大响应时间
        response_frame = ttk.Frame(alert_frame)
        response_frame.pack(fill=tk.X, pady=5)
        ttk.Label(response_frame, text="最大响应时间(秒):").pack(side=tk.LEFT)
        self.vars['max_response_time'] = tk.DoubleVar(value=3.0)
        ttk.Spinbox(response_frame, from_=1.0, to=10.0, increment=0.5,
                   textvariable=self.vars['max_response_time'], width=10).pack(side=tk.LEFT, padx=5)
    
    def create_monitoring_tab(self, notebook):
        """创建性能监控标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="性能监控")
        
        # 监控控制
        control_frame = ttk.LabelFrame(frame, text="监控控制", padding=10)
        control_frame.pack(fill=tk.X, pady=5)
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        self.widgets['start_monitoring_btn'] = ttk.Button(button_frame, text="开始监控", 
                                                         command=self.start_monitoring)
        self.widgets['start_monitoring_btn'].pack(side=tk.LEFT, padx=5)
        
        self.widgets['stop_monitoring_btn'] = ttk.Button(button_frame, text="停止监控", 
                                                        command=self.stop_monitoring, state=tk.DISABLED)
        self.widgets['stop_monitoring_btn'].pack(side=tk.LEFT, padx=5)
        
        # 性能指标显示
        metrics_frame = ttk.LabelFrame(frame, text="性能指标", padding=10)
        metrics_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建文本框显示监控信息
        self.widgets['monitoring_text'] = tk.Text(metrics_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(metrics_frame, orient=tk.VERTICAL, command=self.widgets['monitoring_text'].yview)
        self.widgets['monitoring_text'].configure(yscrollcommand=scrollbar.set)
        
        self.widgets['monitoring_text'].pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_bottom_buttons(self, parent):
        """创建底部按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置默认", command=self.reset_to_defaults).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="应用配置", command=self.apply_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=self.close_window).pack(side=tk.RIGHT, padx=5)
    
    def update_batch_interval_label(self, value):
        """更新批量间隔标签"""
        self.widgets['batch_interval_label'].config(text=f"{float(value):.0f}ms")
    
    def update_ttl_label(self, cache_type, value):
        """更新TTL标签"""
        label_name = f'{cache_type}_ttl_label'
        if label_name in self.widgets:
            self.widgets[label_name].config(text=f"{float(value):.1f}s")
    
    def load_config(self):
        """加载配置"""
        try:
            with open('gate_io_optimization_config.json', 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            optimization_settings = config_data.get('optimization_settings', {})
            
            # 加载订单管理配置
            order_config = optimization_settings.get('order_management', {})
            if 'enable_order_batching' in self.vars:
                self.vars['enable_order_batching'].set(order_config.get('enable_order_batching', True))
            if 'batch_interval' in self.vars:
                self.vars['batch_interval'].set(order_config.get('batch_interval_ms', 100))
            if 'max_batch_size' in self.vars:
                self.vars['max_batch_size'].set(order_config.get('max_batch_size', 20))
            
            # 加载缓存配置
            cache_config = optimization_settings.get('data_caching', {})
            if 'enable_data_caching' in self.vars:
                self.vars['enable_data_caching'].set(cache_config.get('enable_caching', True))
            
            cache_layers = cache_config.get('cache_layers', {})
            for cache_type in ['ticker', 'kline', 'balance', 'orderbook', 'trades']:
                var_name = f'cache_ttl_{cache_type}'
                if var_name in self.vars and cache_type in cache_layers:
                    self.vars[var_name].set(cache_layers[cache_type].get('ttl_seconds', 1.0))
            
            # 加载连接配置
            conn_config = optimization_settings.get('connection_pooling', {})
            if 'enable_connection_pooling' in self.vars:
                self.vars['enable_connection_pooling'].set(conn_config.get('enable_pooling', True))
            if 'max_connections' in self.vars:
                self.vars['max_connections'].set(conn_config.get('max_connections', 5))
            if 'connection_timeout' in self.vars:
                self.vars['connection_timeout'].set(conn_config.get('connection_timeout_seconds', 30.0))
            
            # 加载WebSocket配置
            ws_config = optimization_settings.get('websocket_optimization', {})
            if 'enable_websocket_balancing' in self.vars:
                self.vars['enable_websocket_balancing'].set(ws_config.get('enable_load_balancing', True))
            if 'max_websocket_connections' in self.vars:
                self.vars['max_websocket_connections'].set(ws_config.get('max_connections', 3))
            
            # 加载安全配置
            security_config = optimization_settings.get('security_enhancement', {})
            if 'enable_security_enhancement' in self.vars:
                self.vars['enable_security_enhancement'].set(security_config.get('enable_timestamp_validation', True))
            if 'timestamp_tolerance' in self.vars:
                self.vars['timestamp_tolerance'].set(security_config.get('timestamp_tolerance_seconds', 30.0))
            if 'max_failure_rate' in self.vars:
                self.vars['max_failure_rate'].set(security_config.get('max_failure_rate', 0.05) * 100)
            if 'max_response_time' in self.vars:
                self.vars['max_response_time'].set(security_config.get('max_response_time_seconds', 3.0))
            
            self.log_message("✅ 配置加载成功")
            
        except FileNotFoundError:
            self.log_message("⚠️ 配置文件不存在，使用默认配置")
        except Exception as e:
            self.log_message(f"❌ 配置加载失败: {e}")
            messagebox.showerror("错误", f"配置加载失败: {e}")
    
    def save_config(self):
        """保存配置"""
        try:
            config_data = {
                'optimization_settings': {
                    'order_management': {
                        'enable_order_batching': self.vars.get('enable_order_batching', tk.BooleanVar(True)).get(),
                        'batch_interval_ms': self.vars.get('batch_interval', tk.DoubleVar(100)).get(),
                        'max_batch_size': self.vars.get('max_batch_size', tk.IntVar(20)).get()
                    },
                    'data_caching': {
                        'enable_caching': self.vars.get('enable_data_caching', tk.BooleanVar(True)).get(),
                        'cache_layers': {}
                    },
                    'connection_pooling': {
                        'enable_pooling': self.vars.get('enable_connection_pooling', tk.BooleanVar(True)).get(),
                        'max_connections': self.vars.get('max_connections', tk.IntVar(5)).get(),
                        'connection_timeout_seconds': self.vars.get('connection_timeout', tk.DoubleVar(30.0)).get()
                    },
                    'websocket_optimization': {
                        'enable_load_balancing': self.vars.get('enable_websocket_balancing', tk.BooleanVar(True)).get(),
                        'max_connections': self.vars.get('max_websocket_connections', tk.IntVar(3)).get()
                    },
                    'security_enhancement': {
                        'enable_timestamp_validation': self.vars.get('enable_security_enhancement', tk.BooleanVar(True)).get(),
                        'timestamp_tolerance_seconds': self.vars.get('timestamp_tolerance', tk.DoubleVar(30.0)).get(),
                        'max_failure_rate': self.vars.get('max_failure_rate', tk.DoubleVar(5.0)).get() / 100,
                        'max_response_time_seconds': self.vars.get('max_response_time', tk.DoubleVar(3.0)).get()
                    }
                }
            }
            
            # 添加缓存层配置
            cache_layers = config_data['optimization_settings']['data_caching']['cache_layers']
            for cache_type in ['ticker', 'kline', 'balance', 'orderbook', 'trades']:
                var_name = f'cache_ttl_{cache_type}'
                if var_name in self.vars:
                    cache_layers[cache_type] = {
                        'ttl_seconds': self.vars[var_name].get(),
                        'enable_version_control': True
                    }
            
            with open('gate_io_optimization_config.json', 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.log_message("✅ 配置保存成功")
            messagebox.showinfo("成功", "配置已保存")
            
        except Exception as e:
            self.log_message(f"❌ 配置保存失败: {e}")
            messagebox.showerror("错误", f"配置保存失败: {e}")
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        if messagebox.askyesno("确认", "确定要重置为默认配置吗？"):
            # 重置所有变量为默认值
            defaults = {
                'enable_order_batching': True,
                'batch_interval': 100.0,
                'max_batch_size': 20,
                'max_concurrent_requests': 20,
                'enable_data_caching': True,
                'cache_ttl_ticker': 1.0,
                'cache_ttl_kline': 5.0,
                'cache_ttl_balance': 10.0,
                'cache_ttl_orderbook': 0.5,
                'cache_ttl_trades': 2.0,
                'enable_connection_pooling': True,
                'max_connections': 5,
                'connection_timeout': 30.0,
                'enable_websocket_balancing': True,
                'max_websocket_connections': 3,
                'enable_security_enhancement': True,
                'timestamp_tolerance': 30.0,
                'max_failure_rate': 5.0,
                'max_response_time': 3.0
            }
            
            for var_name, default_value in defaults.items():
                if var_name in self.vars:
                    self.vars[var_name].set(default_value)
            
            self.log_message("✅ 已重置为默认配置")
    
    def apply_config(self):
        """应用配置到优化器"""
        try:
            # 这里可以将配置应用到实际的优化器实例
            self.log_message("✅ 配置已应用")
            messagebox.showinfo("成功", "配置已应用到优化器")
        except Exception as e:
            self.log_message(f"❌ 配置应用失败: {e}")
            messagebox.showerror("错误", f"配置应用失败: {e}")
    
    def start_monitoring(self):
        """开始性能监控"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.widgets['start_monitoring_btn'].config(state=tk.DISABLED)
            self.widgets['stop_monitoring_btn'].config(state=tk.NORMAL)
            
            # 启动监控线程
            self.monitoring_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            self.log_message("🚀 性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        if self.monitoring_active:
            self.monitoring_active = False
            self.widgets['start_monitoring_btn'].config(state=tk.NORMAL)
            self.widgets['stop_monitoring_btn'].config(state=tk.DISABLED)
            
            self.log_message("⏹️ 性能监控已停止")
    
    def monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 模拟获取性能指标
                timestamp = time.strftime("%H:%M:%S")
                metrics = {
                    'timestamp': timestamp,
                    'cache_hit_rate': 85.6,
                    'api_calls_saved': 156,
                    'avg_response_time': 0.234,
                    'active_connections': 3,
                    'queue_size': 5
                }
                
                # 更新GUI显示
                self.window.after(0, self.update_monitoring_display, metrics)
                
                time.sleep(5)  # 每5秒更新一次
                
            except Exception as e:
                self.window.after(0, self.log_message, f"❌ 监控异常: {e}")
                break
    
    def update_monitoring_display(self, metrics):
        """更新监控显示"""
        if 'monitoring_text' in self.widgets:
            text_widget = self.widgets['monitoring_text']
            
            # 添加新的监控信息
            monitor_info = (
                f"[{metrics['timestamp']}] "
                f"缓存命中率: {metrics['cache_hit_rate']:.1f}% | "
                f"API调用节省: {metrics['api_calls_saved']} | "
                f"平均响应时间: {metrics['avg_response_time']:.3f}s | "
                f"活跃连接: {metrics['active_connections']} | "
                f"队列大小: {metrics['queue_size']}\n"
            )
            
            text_widget.insert(tk.END, monitor_info)
            text_widget.see(tk.END)
            
            # 限制文本长度
            lines = text_widget.get("1.0", tk.END).split('\n')
            if len(lines) > 100:
                text_widget.delete("1.0", "10.0")
    
    def log_message(self, message):
        """记录日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def close_window(self):
        """关闭窗口"""
        if self.monitoring_active:
            self.stop_monitoring()
        
        if self.window:
            self.window.destroy()
    
    def run(self):
        """运行GUI"""
        if self.window:
            self.window.mainloop()

def main():
    """主函数"""
    app = GateIOOptimizationGUI()
    app.run()

if __name__ == "__main__":
    main()
