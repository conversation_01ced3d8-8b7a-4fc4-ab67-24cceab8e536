#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能限频管理器 - 为WMZC量化交易系统优化API调用频率控制
支持OKX和Gate.io交易所的动态限频管理，避免触发API限制
"""

import asyncio
import time
import logging
from collections import deque, defaultdict
from dataclasses import dataclass
from typing import Dict, Optional, Union, Deque
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ExchangeType(Enum):
    """交易所类型枚举"""
    OKX = "okx"
    GATE = "gate"


@dataclass
class RateLimitConfig:
    """限频配置数据类"""
    requests_per_second: int
    weight_limit: int
    burst_limit: int
    window_size: int = 1  # 时间窗口大小（秒）


@dataclass
class APIEndpoint:
    """API端点配置"""
    name: str
    weight: int
    category: str  # 'public', 'private', 'trading'


class ExchangeRateLimiter:
    """
    智能交易所限频管理器
    
    功能特性：
    1. 支持多交易所配置（OKX、Gate.io）
    2. 动态权重计算和限频控制
    3. 突发请求处理
    4. 智能等待时间计算
    5. 实时监控和统计
    """
    
    def __init__(self, exchange_type: ExchangeType):
        self.exchange_type = exchange_type
        self.config = self._get_exchange_config(exchange_type)
        
        # 请求历史记录
        self.request_history: Deque[float] = deque(maxlen=1000)
        self.weight_history: Deque[tuple] = deque(maxlen=1000)  # (timestamp, weight)
        
        # 当前状态
        self.current_weight = 0
        self.last_reset_time = time.time()
        self.total_requests = 0
        self.blocked_requests = 0
        
        # 异步锁
        self._lock = asyncio.Lock()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'blocked_requests': 0,
            'avg_wait_time': 0.0,
            'max_wait_time': 0.0,
            'success_rate': 100.0
        }
        
        # API端点配置
        self.endpoints = self._init_endpoints()
        
        logger.info(f"✅ {exchange_type.value.upper()} 限频管理器初始化完成")
        logger.info(f"配置: {self.config.requests_per_second}req/s, 权重限制: {self.config.weight_limit}")

    def _get_exchange_config(self, exchange_type: ExchangeType) -> RateLimitConfig:
        """获取交易所限频配置"""
        configs = {
            ExchangeType.OKX: RateLimitConfig(
                requests_per_second=20,
                weight_limit=100,
                burst_limit=40,
                window_size=1
            ),
            ExchangeType.GATE: RateLimitConfig(
                requests_per_second=10,
                weight_limit=50,
                burst_limit=30,
                window_size=1
            )
        }
        return configs[exchange_type]

    def _init_endpoints(self) -> Dict[str, APIEndpoint]:
        """初始化API端点配置"""
        if self.exchange_type == ExchangeType.OKX:
            return {
                # 公共接口
                'get_ticker': APIEndpoint('get_ticker', 1, 'public'),
                'get_orderbook': APIEndpoint('get_orderbook', 1, 'public'),
                'get_trades': APIEndpoint('get_trades', 1, 'public'),
                'get_klines': APIEndpoint('get_klines', 1, 'public'),
                
                # 私有接口
                'get_account': APIEndpoint('get_account', 5, 'private'),
                'get_positions': APIEndpoint('get_positions', 5, 'private'),
                'get_orders': APIEndpoint('get_orders', 2, 'private'),
                
                # 交易接口
                'place_order': APIEndpoint('place_order', 10, 'trading'),
                'cancel_order': APIEndpoint('cancel_order', 5, 'trading'),
                'batch_orders': APIEndpoint('batch_orders', 20, 'trading'),
            }
        else:  # Gate.io
            return {
                # 公共接口
                'get_ticker': APIEndpoint('get_ticker', 1, 'public'),
                'get_orderbook': APIEndpoint('get_orderbook', 2, 'public'),
                'get_trades': APIEndpoint('get_trades', 1, 'public'),
                'get_klines': APIEndpoint('get_klines', 1, 'public'),
                
                # 私有接口
                'get_account': APIEndpoint('get_account', 3, 'private'),
                'get_positions': APIEndpoint('get_positions', 3, 'private'),
                'get_orders': APIEndpoint('get_orders', 2, 'private'),
                
                # 交易接口
                'place_order': APIEndpoint('place_order', 5, 'trading'),
                'cancel_order': APIEndpoint('cancel_order', 3, 'trading'),
                'batch_orders': APIEndpoint('batch_orders', 15, 'trading'),
            }

    async def acquire_permit(self, endpoint_name: str, custom_weight: Optional[int] = None) -> float:
        """
        获取API调用许可
        
        Args:
            endpoint_name: API端点名称
            custom_weight: 自定义权重（可选）
            
        Returns:
            float: 等待时间（秒）
        """
        async with self._lock:
            # 获取端点配置
            endpoint = self.endpoints.get(endpoint_name)
            if not endpoint:
                logger.warning(f"⚠️ 未知的API端点: {endpoint_name}")
                weight = custom_weight or 1
            else:
                weight = custom_weight or endpoint.weight
            
            # 清理过期的历史记录
            self._cleanup_history()
            
            # 计算当前负载
            current_time = time.time()
            recent_requests = len([t for t in self.request_history 
                                 if current_time - t <= self.config.window_size])
            
            # 计算当前权重
            current_weight = sum([w for t, w in self.weight_history 
                                if current_time - t <= self.config.window_size])
            
            # 检查是否需要等待
            wait_time = self._calculate_wait_time(recent_requests, current_weight, weight)
            
            if wait_time > 0:
                self.blocked_requests += 1
                self.stats['blocked_requests'] += 1
                logger.debug(f"⏳ {endpoint_name} 需要等待 {wait_time:.3f}s")
                await asyncio.sleep(wait_time)
            
            # 记录请求
            request_time = time.time()
            self.request_history.append(request_time)
            self.weight_history.append((request_time, weight))
            self.total_requests += 1
            self.stats['total_requests'] += 1
            
            # 更新统计
            self._update_stats(wait_time)
            
            logger.debug(f"✅ {endpoint_name} 许可已获取 (权重: {weight}, 等待: {wait_time:.3f}s)")
            return wait_time

    def _calculate_wait_time(self, recent_requests: int, current_weight: int, new_weight: int) -> float:
        """计算需要等待的时间"""
        wait_time = 0.0
        
        # 检查请求频率限制
        if recent_requests >= self.config.requests_per_second:
            # 计算到下一个时间窗口的等待时间
            oldest_request = min(self.request_history) if self.request_history else time.time()
            wait_time = max(wait_time, self.config.window_size - (time.time() - oldest_request) + 0.01)
        
        # 检查权重限制
        if current_weight + new_weight > self.config.weight_limit:
            # 找到最早的权重记录，计算等待时间
            if self.weight_history:
                oldest_weight_time = min([t for t, w in self.weight_history])
                wait_time = max(wait_time, self.config.window_size - (time.time() - oldest_weight_time) + 0.01)
        
        # 检查突发限制
        if recent_requests >= self.config.burst_limit:
            wait_time = max(wait_time, 1.0)  # 强制等待1秒
        
        return wait_time

    def _cleanup_history(self):
        """清理过期的历史记录"""
        current_time = time.time()
        cutoff_time = current_time - self.config.window_size * 2  # 保留2个窗口的数据
        
        # 清理请求历史
        while self.request_history and self.request_history[0] < cutoff_time:
            self.request_history.popleft()
        
        # 清理权重历史
        while self.weight_history and self.weight_history[0][0] < cutoff_time:
            self.weight_history.popleft()

    def _update_stats(self, wait_time: float):
        """更新统计信息"""
        if wait_time > self.stats['max_wait_time']:
            self.stats['max_wait_time'] = wait_time

        # 🛠️ BUG修复 #2: 防止除零错误
        # 计算平均等待时间
        if self.stats['total_requests'] > 1:
            total_wait = self.stats['avg_wait_time'] * (self.stats['total_requests'] - 1) + wait_time
            self.stats['avg_wait_time'] = total_wait / self.stats['total_requests']
        else:
            self.stats['avg_wait_time'] = wait_time

        # 计算成功率
        if self.stats['total_requests'] > 0:
            self.stats['success_rate'] = ((self.stats['total_requests'] - self.stats['blocked_requests'])
                                        / self.stats['total_requests'] * 100)

    def get_stats(self) -> Dict:
        """获取统计信息"""
        current_time = time.time()
        recent_requests = len([t for t in self.request_history 
                             if current_time - t <= self.config.window_size])
        current_weight = sum([w for t, w in self.weight_history 
                            if current_time - t <= self.config.window_size])
        
        return {
            **self.stats,
            'current_requests_per_second': recent_requests,
            'current_weight': current_weight,
            'weight_utilization': (current_weight / self.config.weight_limit) * 100,
            'request_utilization': (recent_requests / self.config.requests_per_second) * 100,
            'exchange': self.exchange_type.value
        }

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'blocked_requests': 0,
            'avg_wait_time': 0.0,
            'max_wait_time': 0.0,
            'success_rate': 100.0
        }
        logger.info(f"📊 {self.exchange_type.value.upper()} 限频管理器统计已重置")


class UnifiedRateLimiter:
    """
    统一限频管理器 - 管理多个交易所的限频器
    """
    
    def __init__(self):
        self.limiters: Dict[ExchangeType, ExchangeRateLimiter] = {}
        logger.info("🚀 统一限频管理器初始化完成")

    def get_limiter(self, exchange_type: ExchangeType) -> ExchangeRateLimiter:
        """获取指定交易所的限频器"""
        if exchange_type not in self.limiters:
            self.limiters[exchange_type] = ExchangeRateLimiter(exchange_type)
        return self.limiters[exchange_type]

    async def acquire_permit(self, exchange_type: ExchangeType, endpoint_name: str, 
                           custom_weight: Optional[int] = None) -> float:
        """统一的许可获取接口"""
        limiter = self.get_limiter(exchange_type)
        return await limiter.acquire_permit(endpoint_name, custom_weight)

    def get_all_stats(self) -> Dict[str, Dict]:
        """获取所有交易所的统计信息"""
        return {exchange.value: limiter.get_stats() 
                for exchange, limiter in self.limiters.items()}


# 全局实例
unified_rate_limiter = UnifiedRateLimiter()


# 便捷函数
async def acquire_okx_permit(endpoint_name: str, custom_weight: Optional[int] = None) -> float:
    """获取OKX API调用许可"""
    return await unified_rate_limiter.acquire_permit(ExchangeType.OKX, endpoint_name, custom_weight)


async def acquire_gate_permit(endpoint_name: str, custom_weight: Optional[int] = None) -> float:
    """获取Gate.io API调用许可"""
    return await unified_rate_limiter.acquire_permit(ExchangeType.GATE, endpoint_name, custom_weight)


if __name__ == "__main__":
    # 测试代码
    async def test_rate_limiter():
        """测试限频管理器"""
        print("🧪 开始测试智能限频管理器...")
        
        # 测试OKX限频器
        okx_limiter = ExchangeRateLimiter(ExchangeType.OKX)
        
        # 模拟高频请求
        start_time = time.time()
        for i in range(25):  # 超过20req/s的限制
            wait_time = await okx_limiter.acquire_permit('get_ticker')
            print(f"请求 {i+1}: 等待时间 {wait_time:.3f}s")
        
        total_time = time.time() - start_time
        stats = okx_limiter.get_stats()
        
        print(f"\n📊 测试结果:")
        print(f"总耗时: {total_time:.2f}s")
        print(f"总请求: {stats['total_requests']}")
        print(f"被阻塞请求: {stats['blocked_requests']}")
        print(f"平均等待时间: {stats['avg_wait_time']:.3f}s")
        print(f"成功率: {stats['success_rate']:.1f}%")
        print(f"当前利用率: {stats['request_utilization']:.1f}%")
    
    # 运行测试
    asyncio.run(test_rate_limiter())
