#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🆓 通用免费API管理器 - 智能路径版本
"""

import asyncio
import aiohttp
import json
import os
from pathlib import Path

class UniversalFreeAPIManager:
    def __init__(self):
        self.config_file = self.find_config_file()
        self.sources = []
        self.current_index = 0
        self.load_config()
    
    def find_config_file(self):
        """智能查找配置文件"""
        possible_files = [
            "free_api_config.json",
            "wmzc_config.json", 
            "api_config.json"
        ]
        
        for file_name in possible_files:
            if Path(file_name).exists():
                return file_name
        
        return "free_api_config.json"
    
    def load_config(self):
        """加载配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.sources = config.get('free_api_sources', [])
            print(f"✅ 从 {self.config_file} 加载了 {len(self.sources)} 个API源")
            
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            self.sources = []
    
    async def call_ai(self, prompt, max_tokens=200):
        """调用AI"""
        for source in self.sources:
            if not source.get('enabled', True):
                continue
                
            try:
                response = await self._call_source(source, prompt, max_tokens)
                if response:
                    print(f"✅ {source['name']} 调用成功")
                    return response
            except Exception as e:
                print(f"❌ {source['name']} 失败: {e}")
                continue
        
        print("❌ 所有API源都不可用")
        return None
    
    async def _call_source(self, source, prompt, max_tokens):
        """调用单个源"""
        headers = {'Content-Type': 'application/json'}
        
        if source['name'].startswith('huggingface'):
            headers['Authorization'] = f"Bearer {source['api_key']}"
            data = {
                "inputs": prompt,
                "parameters": {"max_new_tokens": max_tokens}
            }
            url = f"{source['base_url']}/{source['model']}"
        else:
            headers['Authorization'] = f"Bearer {source['api_key']}"
            data = {
                "model": source['model'],
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": max_tokens
            }
            url = f"{source['base_url']}/chat/completions"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data, timeout=30) as resp:
                if resp.status == 200:
                    return await resp.json()
        return None

# 全局实例
api_manager = UniversalFreeAPIManager()

async def get_ai_response(prompt, **kwargs):
    """获取AI响应的便捷函数"""
    return await api_manager.call_ai(prompt, **kwargs)

def test_api():
    """测试API"""
    async def test():
        print("🧪 测试免费API...")
        response = await get_ai_response("你好，请简单介绍量化交易")
        if response:
            print("🎉 测试成功！")
        else:
            print("❌ 测试失败")
    
    asyncio.run(test())

if __name__ == "__main__":
    test_api()
