#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试tkinter导入修复
"""

def test_tkinter_import():
    """测试tkinter导入是否正确"""
    try:
        print("🔍 测试tkinter导入...")
        
        # 测试基本导入
        import tkinter as tk
        from tkinter import messagebox, ttk, simpledialog
        print("✅ tkinter基本导入成功")
        
        # 测试TradingApp类定义语法
        print("🔍 测试TradingApp类定义语法...")
        
        # 模拟TradingApp类定义
        class TestTradingApp(tk.Tk):
            def __init__(self):
                super().__init__()
                self.title("测试窗口")
        
        print("✅ TradingApp类定义语法正确")
        
        # 测试创建实例（不显示窗口）
        print("🔍 测试创建实例...")
        app = TestTradingApp()
        app.withdraw()  # 隐藏窗口
        print("✅ TradingApp实例创建成功")
        
        # 清理
        app.destroy()
        print("✅ 所有测试通过！tkinter导入修复成功")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_tkinter_import()
