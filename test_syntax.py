#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语法检查测试脚本
"""

import ast
import sys

def check_syntax(filename):
    """检查Python文件语法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 解析语法树
        ast.parse(source, filename=filename)
        print(f"✅ {filename} 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} 语法错误:")
        print(f"   行 {e.lineno}: {e.text}")
        print(f"   错误: {e.msg}")
        return False
        
    except Exception as e:
        print(f"❌ {filename} 检查失败: {e}")
        return False

if __name__ == "__main__":
    success = check_syntax("WMZC.py")
    if success:
        print("🎉 所有语法检查通过！")
    else:
        print("💥 发现语法错误！")
        sys.exit(1)
