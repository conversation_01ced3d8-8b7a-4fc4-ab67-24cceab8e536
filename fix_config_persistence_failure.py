#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC配置持久化失效问题修复脚本
解决配置保存后在系统重启时被重置的根本问题
"""

import os
import json
import shutil
from datetime import datetime

class ConfigPersistenceFailureFixer:
    """配置持久化失效问题修复器"""
    
    def __init__(self):
        self.config_files = {
            'trading_config.json': 'trading_config.json',
            'wmzc_config.json': 'wmzc_config.json'
        }
        self.backup_dir = 'config_persistence_fix_backups'
        self.fixes_applied = 0
        
    def fix_persistence_failure(self):
        """修复配置持久化失效问题"""
        print("🔧 开始修复配置持久化失效问题...")
        print("=" * 60)
        
        # 1. 创建备份
        self.create_backup()
        
        # 2. 禁用配置覆盖脚本
        self.disable_config_override_scripts()
        
        # 3. 修复配置文件结构
        self.fix_config_file_structure()
        
        # 4. 创建配置保护机制
        self.create_config_protection()
        
        # 5. 验证修复结果
        self.verify_fixes()
        
        return self.fixes_applied > 0
    
    def create_backup(self):
        """创建配置文件备份"""
        print("💾 创建配置文件备份...")
        
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        for name, path in self.config_files.items():
            if os.path.exists(path):
                backup_path = os.path.join(self.backup_dir, f"{name}.backup_{timestamp}")
                try:
                    shutil.copy2(path, backup_path)
                    print(f"  ✅ {name} -> {backup_path}")
                except Exception as e:
                    print(f"  ❌ 备份 {name} 失败: {e}")
    
    def disable_config_override_scripts(self):
        """禁用配置覆盖脚本"""
        print("\n🚫 禁用配置覆盖脚本...")
        
        override_scripts = [
            'wmzc_config_warning_silencer.py',
            'wmzc_final_config_optimizer.py',
            'wmzc_config_fixer.py'
        ]
        
        for script in override_scripts:
            if os.path.exists(script):
                try:
                    # 重命名脚本，添加.disabled后缀
                    disabled_name = f"{script}.disabled"
                    if not os.path.exists(disabled_name):
                        shutil.move(script, disabled_name)
                        print(f"  ✅ 已禁用: {script} -> {disabled_name}")
                        self.fixes_applied += 1
                    else:
                        print(f"  ⚠️ {script} 已经被禁用")
                except Exception as e:
                    print(f"  ❌ 禁用 {script} 失败: {e}")
            else:
                print(f"  ✅ {script} 不存在，无需禁用")
    
    def fix_config_file_structure(self):
        """修复配置文件结构"""
        print("\n📋 修复配置文件结构...")
        
        # 修复trading_config.json
        self.fix_trading_config()
        
        # 修复wmzc_config.json
        self.fix_wmzc_config()
    
    def fix_trading_config(self):
        """修复trading_config.json"""
        print("  🔧 修复 trading_config.json...")
        
        config_path = 'trading_config.json'
        
        try:
            # 读取现有配置
            existing_config = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
            
            # 检查是否有用户API配置
            has_user_api = (existing_config.get('API_KEY') and 
                           existing_config.get('API_SECRET') and 
                           existing_config.get('PASSPHRASE'))
            
            if has_user_api:
                print(f"    🔒 检测到用户API配置，将保护现有配置")
                user_api = {
                    'API_KEY': existing_config['API_KEY'],
                    'API_SECRET': existing_config['API_SECRET'],
                    'PASSPHRASE': existing_config['PASSPHRASE']
                }
            else:
                print(f"    ⚠️ 未检测到用户API配置")
                user_api = {}
            
            # 创建完整的配置结构
            complete_config = {
                # 保护用户API配置
                "API_KEY": user_api.get('API_KEY', ''),
                "API_SECRET": user_api.get('API_SECRET', ''),
                "PASSPHRASE": user_api.get('PASSPHRASE', ''),
                "OKX_API_KEY": user_api.get('API_KEY', ''),
                "OKX_SECRET_KEY": user_api.get('API_SECRET', ''),
                "OKX_PASSPHRASE": user_api.get('PASSPHRASE', ''),
                
                # 基本交易配置
                "EXCHANGE": existing_config.get('EXCHANGE', 'OKX'),
                "SYMBOL": existing_config.get('SYMBOL', 'BTC-USDT-SWAP'),
                "TIMEFRAME": existing_config.get('TIMEFRAME', '1m'),
                "ORDER_USDT_AMOUNT": existing_config.get('ORDER_USDT_AMOUNT', 10),
                "LEVERAGE": existing_config.get('LEVERAGE', 3),
                "RISK_PERCENT": existing_config.get('RISK_PERCENT', 1.0),
                
                # 策略配置
                "ENABLE_KDJ": existing_config.get('ENABLE_KDJ', True),
                "ENABLE_MACD": existing_config.get('ENABLE_MACD', True),
                "ENABLE_PINBAR": existing_config.get('ENABLE_PINBAR', True),
                "ENABLE_RSI": existing_config.get('ENABLE_RSI', True),
                
                # 系统配置
                "LOG_LEVEL": existing_config.get('LOG_LEVEL', 'INFO'),
                "TEST_MODE": existing_config.get('TEST_MODE', True),
                "ENABLE_TRADING": existing_config.get('ENABLE_TRADING', False),
                
                # 配置保护标记
                "_CONFIG_PROTECTED": True,
                "_LAST_UPDATED": datetime.now().isoformat()
            }
            
            # 合并其他现有配置
            for key, value in existing_config.items():
                if key not in complete_config:
                    complete_config[key] = value
            
            # 保存配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(complete_config, f, indent=2, ensure_ascii=False)
            
            print(f"    ✅ trading_config.json 修复完成")
            self.fixes_applied += 1
            
        except Exception as e:
            print(f"    ❌ 修复 trading_config.json 失败: {e}")
    
    def fix_wmzc_config(self):
        """修复wmzc_config.json"""
        print("  🔧 修复 wmzc_config.json...")
        
        config_path = 'wmzc_config.json'
        
        try:
            # 读取现有配置
            existing_config = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
            
            # 从trading_config.json读取用户API配置
            user_api = {}
            if os.path.exists('trading_config.json'):
                with open('trading_config.json', 'r', encoding='utf-8') as f:
                    trading_config = json.load(f)
                    if trading_config.get('API_KEY'):
                        user_api = {
                            'okx_api_key': trading_config['API_KEY'],
                            'okx_secret_key': trading_config['API_SECRET'],
                            'okx_passphrase': trading_config['PASSPHRASE']
                        }
            
            # 如果没有用户API配置，保留现有的
            if not user_api:
                user_api = {
                    'okx_api_key': existing_config.get('okx_api_key', ''),
                    'okx_secret_key': existing_config.get('okx_secret_key', ''),
                    'okx_passphrase': existing_config.get('okx_passphrase', '')
                }
            
            # 创建完整的配置结构
            complete_config = {
                # 保护用户API配置
                "okx_api_key": user_api['okx_api_key'],
                "okx_secret_key": user_api['okx_secret_key'],
                "okx_passphrase": user_api['okx_passphrase'],
                
                # 基本配置
                "exchange_selection": existing_config.get('exchange_selection', 'OKX'),
                "default_symbol": existing_config.get('default_symbol', 'BTC-USDT-SWAP'),
                "default_timeframe": existing_config.get('default_timeframe', '1m'),
                
                # 配置保护标记
                "_CONFIG_PROTECTED": True,
                "_LAST_UPDATED": datetime.now().isoformat()
            }
            
            # 合并其他现有配置
            for key, value in existing_config.items():
                if key not in complete_config:
                    complete_config[key] = value
            
            # 保存配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(complete_config, f, indent=2, ensure_ascii=False)
            
            print(f"    ✅ wmzc_config.json 修复完成")
            self.fixes_applied += 1
            
        except Exception as e:
            print(f"    ❌ 修复 wmzc_config.json 失败: {e}")
    
    def create_config_protection(self):
        """创建配置保护机制"""
        print("\n🛡️ 创建配置保护机制...")
        
        protection_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 🛡️ WMZC配置保护脚本 - 防止配置被意外覆盖

import os
import json
from datetime import datetime

def protect_user_config():
    \"\"\"保护用户配置不被覆盖\"\"\"
    config_files = ['trading_config.json', 'wmzc_config.json']
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查是否有保护标记
                if config.get('_CONFIG_PROTECTED'):
                    print(f"🛡️ {config_file} 受保护，跳过覆盖")
                    continue
                
                # 添加保护标记
                config['_CONFIG_PROTECTED'] = True
                config['_PROTECTION_ENABLED'] = datetime.now().isoformat()
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                print(f"🛡️ {config_file} 保护已启用")
                
            except Exception as e:
                print(f"❌ 保护 {config_file} 失败: {e}")

if __name__ == "__main__":
    protect_user_config()
"""
        
        try:
            with open('protect_config.py', 'w', encoding='utf-8') as f:
                f.write(protection_script)
            print("  ✅ 配置保护脚本已创建: protect_config.py")
            self.fixes_applied += 1
        except Exception as e:
            print(f"  ❌ 创建保护脚本失败: {e}")
    
    def verify_fixes(self):
        """验证修复结果"""
        print("\n✅ 验证修复结果...")
        
        success_count = 0
        
        for name, path in self.config_files.items():
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 检查保护标记
                    if config.get('_CONFIG_PROTECTED'):
                        print(f"  ✅ {name}: 配置保护已启用")
                        success_count += 1
                    else:
                        print(f"  ⚠️ {name}: 配置保护未启用")
                    
                    # 检查API配置
                    if name == 'trading_config.json':
                        has_api = config.get('API_KEY') or config.get('OKX_API_KEY')
                    else:
                        has_api = config.get('okx_api_key')
                    
                    if has_api:
                        print(f"  ✅ {name}: API配置存在")
                    else:
                        print(f"  ⚠️ {name}: API配置为空")
                        
                except Exception as e:
                    print(f"  ❌ {name}: 验证失败 {e}")
            else:
                print(f"  ❌ {name}: 文件不存在")
        
        return success_count == len(self.config_files)

def main():
    """主函数"""
    print("🔧 WMZC配置持久化失效问题修复工具")
    print("=" * 60)
    
    fixer = ConfigPersistenceFailureFixer()
    
    try:
        success = fixer.fix_persistence_failure()
        
        print("\n" + "=" * 60)
        if success:
            print(f"🎉 配置持久化问题修复完成！共应用了 {fixer.fixes_applied} 个修复")
            print("\n💡 修复内容:")
            print("  1. ✅ 禁用了配置覆盖脚本")
            print("  2. ✅ 修复了配置文件结构")
            print("  3. ✅ 创建了配置保护机制")
            print("  4. ✅ 保护了用户API配置")
            
            print("\n🚀 下一步操作:")
            print("  1. 重新启动WMZC系统")
            print("  2. 在主配置页面填写API密钥")
            print("  3. 点击保存配置")
            print("  4. 重启验证配置是否保留")
            
        else:
            print("❌ 配置持久化问题修复失败")
            print("💡 请检查错误信息并手动修复")
        
        return success
        
    except Exception as e:
        print(f"❌ 修复过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
