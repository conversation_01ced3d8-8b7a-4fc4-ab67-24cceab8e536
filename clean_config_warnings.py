#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 WMZC配置文件清理工具
清理未知字段，消除配置警告
"""

import os
import json
import shutil
from datetime import datetime

def clean_unknown_fields():
    """清理未知字段"""
    print("🧹 清理WMZC配置文件中的未知字段")
    print("=" * 50)
    
    # 已知字段列表（从CONFIG_SCHEMA提取）
    known_fields = {
        'API_KEY', 'API_SECRET', 'PASSPHRASE',
        'OKX_API_KEY', 'OKX_SECRET_KEY', 'OKX_PASSPHRASE',
        'GATE_API_KEY', 'GATE_SECRET_KEY',
        'SYMBOL', 'TIMEFRAME', 'EXCHANGE', 'CURRENT_EXCHANGE',
        'AMOUNT', 'ORDER_AMOUNT', 'ORDER_USDT_AMOUNT', 'LEVERAGE', 'RISK_PERCENT',
        'ENABLE_TRADING', 'AUTO_TRADING', 'TEST_MODE',
        'ENABLE_KDJ', 'ENABLE_MACD', 'ENABLE_PINBAR', 'ENABLE_STOP_LOSS',
        'ENABLE_TAKE_PROFIT', 'ENABLE_ADVANCED_MACD',
        'STOP_LOSS_PCT', 'TAKE_PROFIT_PCT', 'PROFIT_TARGET_PCT',
        'MAX_POSITION_SIZE', 'RISK_PER_TRADE', 'MAX_DAILY_TRADES',
        'KDJ', 'MACD', 'RSI', 'PINBAR', 'TECHNICAL_INDICATORS',
        'THEME', 'window_geometry', 'current_tab', 'font_size', 'update_interval',
        'LOG_LEVEL', 'AUTO_SAVE', 'SANDBOX', 'DEFAULT_SYMBOL', 'DEFAULT_TIMEFRAME',
        'INDICATOR_SYNC',
        'exchange_selection', 'okx_api_key', 'okx_secret_key', 'okx_passphrase',
        'default_symbol', 'default_timeframe'
    }
    
    config_dirs = [
        os.getcwd(),
        os.path.expanduser("~/.wmzc_trading")
    ]
    
    config_files = [
        'trading_config.json',
        'wmzc_config.json',
        'user_settings.json',
        'misc_optimization_config.json',
        'ai_config.json'
    ]
    
    cleaned_count = 0
    
    for config_dir in config_dirs:
        if not os.path.exists(config_dir):
            continue
            
        print(f"\n📁 清理目录: {config_dir}")
        
        for config_file in config_files:
            file_path = os.path.join(config_dir, config_file)
            
            if not os.path.exists(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                if not content or content == '{}':
                    continue
                
                config = json.loads(content)
                
                # 找出未知字段
                unknown_fields = []
                for key in config.keys():
                    if not key.startswith('_') and key not in known_fields:
                        unknown_fields.append(key)
                
                if unknown_fields:
                    # 备份原文件
                    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.copy2(file_path, backup_path)
                    
                    # 创建清理后的配置
                    cleaned_config = {}
                    for key, value in config.items():
                        if key.startswith('_') or key in known_fields:
                            cleaned_config[key] = value
                    
                    # 保存清理后的配置
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(cleaned_config, f, indent=2, ensure_ascii=False)
                    
                    print(f"  ✅ {config_file}: 清理了 {len(unknown_fields)} 个未知字段")
                    print(f"    已备份: {os.path.basename(backup_path)}")
                    cleaned_count += 1
                else:
                    print(f"  ✅ {config_file}: 无需清理")
                    
            except Exception as e:
                print(f"  ❌ {config_file}: 清理失败 - {e}")
    
    print(f"\n🎉 清理完成！共清理了 {cleaned_count} 个配置文件")
    print("💡 重新启动WMZC系统，配置警告应该消失")

if __name__ == "__main__":
    clean_unknown_fields()
