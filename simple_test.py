#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 验证WMZC系统基本功能
"""

import sys
import os

def main():
    print("=" * 50)
    print("🚀 WMZC交易系统 - 简单测试")
    print("=" * 50)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查文件
    print(f"当前目录: {os.getcwd()}")
    
    if os.path.exists('WMZC.py'):
        print("✅ 找到WMZC.py文件")
        file_size = os.path.getsize('WMZC.py')
        print(f"文件大小: {file_size:,} 字节")
    else:
        print("❌ 未找到WMZC.py文件")
        return False
    
    # 测试基础导入
    print("\n🔍 测试基础库...")
    try:
        import tkinter
        print("✅ tkinter - OK")
    except:
        print("❌ tkinter - 失败")
    
    try:
        import pandas
        print(f"✅ pandas v{pandas.__version__} - OK")
    except:
        print("❌ pandas - 失败")
    
    try:
        import numpy
        print(f"✅ numpy v{numpy.__version__} - OK")
    except:
        print("❌ numpy - 失败")
    
    try:
        import requests
        print(f"✅ requests v{requests.__version__} - OK")
    except:
        print("❌ requests - 失败")
    
    # 测试WMZC导入
    print("\n🔍 测试WMZC模块...")
    try:
        import WMZC
        print("✅ WMZC模块导入成功")
        
        # 检查主要组件
        if hasattr(WMZC, 'TradingApp'):
            print("✅ 找到TradingApp类")
        if hasattr(WMZC, 'main'):
            print("✅ 找到main函数")
            
    except Exception as e:
        print(f"❌ WMZC模块导入失败: {e}")
        return False
    
    print("\n🎉 基本测试完成！")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n💡 可以尝试运行: python run_trading_system.py")
    else:
        print("\n⚠️ 请先解决上述问题")
