#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC系统专业检测分析器
基于100%代码理解的全面检测
"""

import re
import ast
import time
from typing import Dict, List, Any

class WMZCDetectionAnalyzer:
    """WMZC系统专业检测分析器"""
    
    def __init__(self, file_path: str = 'WMZC.py'):
        self.file_path = file_path
        self.content = ""
        self.lines = []
        self.detection_results = {}
        
    def load_file(self):
        """加载文件内容"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.content = f.read()
                self.lines = self.content.split('\n')
            print(f"✅ 文件加载成功: {len(self.lines)}行代码")
            return True
        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
            return False
    
    def detect_async_architecture(self):
        """检测异步架构合规性"""
        print("\n🔍 检测1: 异步架构合规性")
        
        # time.sleep检测
        time_sleep_pattern = r'time\.sleep\s*\('
        time_sleep_matches = re.findall(time_sleep_pattern, self.content)
        
        # async def检测
        async_def_pattern = r'async\s+def\s+'
        async_def_matches = re.findall(async_def_pattern, self.content)
        
        # await检测
        await_pattern = r'await\s+'
        await_matches = re.findall(await_pattern, self.content)
        
        # await asyncio.sleep检测
        await_sleep_pattern = r'await\s+asyncio\.sleep\s*\('
        await_sleep_matches = re.findall(await_sleep_pattern, self.content)
        
        results = {
            'time_sleep_count': len(time_sleep_matches),
            'async_def_count': len(async_def_matches),
            'await_count': len(await_matches),
            'await_sleep_count': len(await_sleep_matches),
            'async_compliance': len(time_sleep_matches) == 0
        }
        
        print(f"  ✓ time.sleep使用次数: {results['time_sleep_count']}")
        print(f"  ✓ async def函数数量: {results['async_def_count']}")
        print(f"  ✓ await调用次数: {results['await_count']}")
        print(f"  ✓ await asyncio.sleep次数: {results['await_sleep_count']}")
        print(f"  ✓ 异步架构合规: {'✅ 是' if results['async_compliance'] else '❌ 否'}")
        
        self.detection_results['async_architecture'] = results
        return results
    
    def detect_security_issues(self):
        """检测安全问题"""
        print("\n🛡️ 检测2: 安全性检测")
        
        # 硬编码API密钥检测
        api_key_pattern = r'(api_key|secret_key|access_token)\s*=\s*["\'][^"\']{10,}["\']'
        api_key_matches = re.findall(api_key_pattern, self.content, re.IGNORECASE)
        
        # 加密实现检测
        encryption_pattern = r'(AES|encrypt|decrypt|cipher)'
        encryption_matches = re.findall(encryption_pattern, self.content, re.IGNORECASE)
        
        # HTTPS/WSS检测
        secure_protocol_pattern = r'(https://|wss://)'
        secure_matches = re.findall(secure_protocol_pattern, self.content)
        
        # 签名算法检测
        signature_pattern = r'(hmac|sha|sign)'
        signature_matches = re.findall(signature_pattern, self.content, re.IGNORECASE)
        
        results = {
            'hardcoded_keys': len(api_key_matches),
            'encryption_usage': len(encryption_matches),
            'secure_protocols': len(secure_matches),
            'signature_algorithms': len(signature_matches),
            'security_score': 0
        }
        
        # 计算安全评分
        if results['hardcoded_keys'] == 0:
            results['security_score'] += 25
        if results['encryption_usage'] > 0:
            results['security_score'] += 25
        if results['secure_protocols'] > 0:
            results['security_score'] += 25
        if results['signature_algorithms'] > 0:
            results['security_score'] += 25
        
        print(f"  ✓ 硬编码密钥: {results['hardcoded_keys']}个")
        print(f"  ✓ 加密实现: {results['encryption_usage']}处")
        print(f"  ✓ 安全协议: {results['secure_protocols']}处")
        print(f"  ✓ 签名算法: {results['signature_algorithms']}处")
        print(f"  ✓ 安全评分: {results['security_score']}/100")
        
        self.detection_results['security'] = results
        return results
    
    def detect_resource_management(self):
        """检测资源管理"""
        print("\n🔧 检测3: 资源管理")
        
        # with语句检测
        with_pattern = r'with\s+.*open\s*\('
        with_matches = re.findall(with_pattern, self.content)
        
        # 不安全的open调用
        unsafe_open_lines = []
        for i, line in enumerate(self.lines, 1):
            if 'open(' in line and 'with' not in line and not line.strip().startswith('#'):
                unsafe_open_lines.append(i)
        
        # close()调用检测
        close_pattern = r'\.close\s*\(\s*\)'
        close_matches = re.findall(close_pattern, self.content)
        
        # 异步上下文管理器检测
        async_with_pattern = r'async\s+with\s+'
        async_with_matches = re.findall(async_with_pattern, self.content)
        
        results = {
            'with_statements': len(with_matches),
            'unsafe_opens': len(unsafe_open_lines),
            'close_calls': len(close_matches),
            'async_with': len(async_with_matches),
            'resource_safety': len(unsafe_open_lines) == 0
        }
        
        print(f"  ✓ with语句使用: {results['with_statements']}次")
        print(f"  ✓ 不安全open调用: {results['unsafe_opens']}个")
        print(f"  ✓ close()调用: {results['close_calls']}次")
        print(f"  ✓ async with使用: {results['async_with']}次")
        print(f"  ✓ 资源安全: {'✅ 是' if results['resource_safety'] else '❌ 否'}")
        
        if unsafe_open_lines:
            print(f"  ⚠️ 不安全open调用位置: {unsafe_open_lines[:5]}...")
        
        self.detection_results['resource_management'] = results
        return results
    
    def detect_business_logic(self):
        """检测业务逻辑"""
        print("\n📊 检测4: 业务逻辑正确性")
        
        # 技术指标类检测
        indicator_classes = ['SMA', 'EMA', 'MACD', 'RSI', 'KDJ', 'Bollinger']
        indicator_implementations = {}
        
        for indicator in indicator_classes:
            pattern = rf'def\s+calculate_{indicator.lower()}\s*\('
            matches = re.findall(pattern, self.content, re.IGNORECASE)
            indicator_implementations[indicator] = len(matches)
        
        # 策略类检测
        strategy_pattern = r'class\s+.*Strategy.*:'
        strategy_matches = re.findall(strategy_pattern, self.content)
        
        # 风险管理检测
        risk_pattern = r'class\s+.*Risk.*Manager.*:'
        risk_matches = re.findall(risk_pattern, self.content)
        
        # 交易执行检测
        trading_pattern = r'def\s+.*trade.*\('
        trading_matches = re.findall(trading_pattern, self.content, re.IGNORECASE)
        
        results = {
            'technical_indicators': indicator_implementations,
            'strategy_classes': len(strategy_matches),
            'risk_managers': len(risk_matches),
            'trading_functions': len(trading_matches),
            'business_completeness': sum(indicator_implementations.values()) >= 6
        }
        
        print(f"  ✓ 技术指标实现:")
        for indicator, count in indicator_implementations.items():
            print(f"    - {indicator}: {count}个")
        print(f"  ✓ 策略类: {results['strategy_classes']}个")
        print(f"  ✓ 风险管理器: {results['risk_managers']}个")
        print(f"  ✓ 交易函数: {results['trading_functions']}个")
        print(f"  ✓ 业务完整性: {'✅ 是' if results['business_completeness'] else '❌ 否'}")
        
        self.detection_results['business_logic'] = results
        return results
    
    def detect_exception_handling(self):
        """检测异常处理"""
        print("\n🚨 检测5: 异常处理机制")
        
        # try-except检测
        try_pattern = r'try\s*:'
        try_matches = re.findall(try_pattern, self.content)
        
        # 异常类定义检测
        exception_class_pattern = r'class\s+\w*Exception\s*\('
        exception_classes = re.findall(exception_class_pattern, self.content)
        
        # 异常处理器检测
        handler_pattern = r'class\s+.*ExceptionHandler.*:'
        handler_matches = re.findall(handler_pattern, self.content)
        
        # 日志记录检测
        log_pattern = r'log\s*\('
        log_matches = re.findall(log_pattern, self.content)
        
        results = {
            'try_blocks': len(try_matches),
            'exception_classes': len(exception_classes),
            'exception_handlers': len(handler_matches),
            'log_calls': len(log_matches),
            'exception_coverage': len(try_matches) > 50
        }
        
        print(f"  ✓ try-except块: {results['try_blocks']}个")
        print(f"  ✓ 异常类定义: {results['exception_classes']}个")
        print(f"  ✓ 异常处理器: {results['exception_handlers']}个")
        print(f"  ✓ 日志调用: {results['log_calls']}次")
        print(f"  ✓ 异常覆盖: {'✅ 充分' if results['exception_coverage'] else '❌ 不足'}")
        
        self.detection_results['exception_handling'] = results
        return results
    
    def detect_performance_optimization(self):
        """检测性能优化"""
        print("\n⚡ 检测6: 性能优化")
        
        # 缓存实现检测
        cache_pattern = r'class\s+.*Cache.*:'
        cache_matches = re.findall(cache_pattern, self.content)
        
        # 连接池检测
        pool_pattern = r'class\s+.*Pool.*:'
        pool_matches = re.findall(pool_pattern, self.content)
        
        # 限频器检测
        limiter_pattern = r'class\s+.*Limiter.*:'
        limiter_matches = re.findall(limiter_pattern, self.content)
        
        # 优化器检测
        optimizer_pattern = r'class\s+.*Optimizer.*:'
        optimizer_matches = re.findall(optimizer_pattern, self.content)
        
        results = {
            'cache_implementations': len(cache_matches),
            'connection_pools': len(pool_matches),
            'rate_limiters': len(limiter_matches),
            'optimizers': len(optimizer_matches),
            'performance_optimized': (len(cache_matches) + len(pool_matches) + 
                                    len(limiter_matches) + len(optimizer_matches)) >= 4
        }
        
        print(f"  ✓ 缓存实现: {results['cache_implementations']}个")
        print(f"  ✓ 连接池: {results['connection_pools']}个")
        print(f"  ✓ 限频器: {results['rate_limiters']}个")
        print(f"  ✓ 优化器: {results['optimizers']}个")
        print(f"  ✓ 性能优化: {'✅ 充分' if results['performance_optimized'] else '❌ 不足'}")
        
        self.detection_results['performance'] = results
        return results
    
    def calculate_overall_score(self):
        """计算总体评分"""
        print("\n🏆 总体评分计算")
        
        scores = {
            'async_architecture': 0,
            'security': 0,
            'resource_management': 0,
            'business_logic': 0,
            'exception_handling': 0,
            'performance': 0
        }
        
        # 异步架构评分
        async_result = self.detection_results.get('async_architecture', {})
        if async_result.get('async_compliance', False):
            scores['async_architecture'] = 100
        elif async_result.get('time_sleep_count', 0) == 0:
            scores['async_architecture'] = 95
        else:
            scores['async_architecture'] = max(0, 80 - async_result.get('time_sleep_count', 0) * 5)
        
        # 安全性评分
        security_result = self.detection_results.get('security', {})
        scores['security'] = security_result.get('security_score', 0)
        
        # 资源管理评分
        resource_result = self.detection_results.get('resource_management', {})
        if resource_result.get('resource_safety', False):
            scores['resource_management'] = 100
        else:
            unsafe_count = resource_result.get('unsafe_opens', 0)
            scores['resource_management'] = max(0, 100 - unsafe_count * 10)
        
        # 业务逻辑评分
        business_result = self.detection_results.get('business_logic', {})
        if business_result.get('business_completeness', False):
            scores['business_logic'] = 95
        else:
            indicator_count = sum(business_result.get('technical_indicators', {}).values())
            scores['business_logic'] = min(95, indicator_count * 15)
        
        # 异常处理评分
        exception_result = self.detection_results.get('exception_handling', {})
        if exception_result.get('exception_coverage', False):
            scores['exception_handling'] = 90
        else:
            try_count = exception_result.get('try_blocks', 0)
            scores['exception_handling'] = min(90, try_count * 1.5)
        
        # 性能优化评分
        performance_result = self.detection_results.get('performance', {})
        if performance_result.get('performance_optimized', False):
            scores['performance'] = 95
        else:
            opt_count = (performance_result.get('cache_implementations', 0) + 
                        performance_result.get('connection_pools', 0) + 
                        performance_result.get('rate_limiters', 0) + 
                        performance_result.get('optimizers', 0))
            scores['performance'] = min(95, opt_count * 20)
        
        # 计算总分
        total_score = sum(scores.values()) / len(scores)
        
        print(f"  📊 各项评分:")
        for category, score in scores.items():
            print(f"    - {category}: {score:.1f}/100")
        
        print(f"\n  🎯 总体评分: {total_score:.1f}/100")
        
        # 评级
        if total_score >= 95:
            grade = "世界级"
            emoji = "🏆"
        elif total_score >= 85:
            grade = "企业级"
            emoji = "🥇"
        elif total_score >= 75:
            grade = "专业级"
            emoji = "🥈"
        else:
            grade = "基础级"
            emoji = "🥉"
        
        print(f"  {emoji} 系统等级: {grade}")
        
        self.detection_results['overall'] = {
            'scores': scores,
            'total_score': total_score,
            'grade': grade
        }
        
        return total_score, grade
    
    def run_full_detection(self):
        """运行完整检测"""
        print("🚀 开始WMZC系统专业检测")
        print("=" * 60)
        
        if not self.load_file():
            return False
        
        # 执行各项检测
        self.detect_async_architecture()
        self.detect_security_issues()
        self.detect_resource_management()
        self.detect_business_logic()
        self.detect_exception_handling()
        self.detect_performance_optimization()
        
        # 计算总体评分
        total_score, grade = self.calculate_overall_score()
        
        print("\n" + "=" * 60)
        print("✅ 检测完成")
        
        return True

if __name__ == "__main__":
    analyzer = WMZCDetectionAnalyzer()
    analyzer.run_full_detection()
