@echo off
echo 🔧 创建干净的WMZC配置文件
echo ================================

REM 创建WMZC配置目录
if not exist "C:\Users\<USER>\.wmzc_trading" (
    mkdir "C:\Users\<USER>\.wmzc_trading"
    echo ✅ 已创建WMZC配置目录
)

REM 切换到配置目录
cd "C:\Users\<USER>\.wmzc_trading"

REM 备份现有文件
if exist wmzc_config.json ren wmzc_config.json wmzc_config.json.backup
if exist trading_config.json ren trading_config.json trading_config.json.backup
if exist user_settings.json ren user_settings.json user_settings.json.backup
if exist misc_optimization_config.json ren misc_optimization_config.json misc_optimization_config.json.backup
if exist ai_config.json ren ai_config.json ai_config.json.backup

echo ✅ 已备份现有配置文件

REM 创建干净的wmzc_config.json
echo {> wmzc_config.json
echo   "exchange_selection": "OKX",>> wmzc_config.json
echo   "okx_api_key": "",>> wmzc_config.json
echo   "okx_secret_key": "",>> wmzc_config.json
echo   "okx_passphrase": "",>> wmzc_config.json
echo   "default_symbol": "BTC-USDT-SWAP",>> wmzc_config.json
echo   "default_timeframe": "1m",>> wmzc_config.json
echo   "_CONFIG_CLEANED": true>> wmzc_config.json
echo }>> wmzc_config.json

REM 创建干净的trading_config.json
echo {> trading_config.json
echo   "API_KEY": "",>> trading_config.json
echo   "API_SECRET": "",>> trading_config.json
echo   "PASSPHRASE": "",>> trading_config.json
echo   "EXCHANGE": "OKX",>> trading_config.json
echo   "SYMBOL": "BTC-USDT-SWAP",>> trading_config.json
echo   "TIMEFRAME": "1m",>> trading_config.json
echo   "ORDER_USDT_AMOUNT": 10,>> trading_config.json
echo   "LEVERAGE": 3,>> trading_config.json
echo   "TEST_MODE": true,>> trading_config.json
echo   "_CONFIG_CLEANED": true>> trading_config.json
echo }>> trading_config.json

REM 复制相同的配置到其他文件
copy trading_config.json user_settings.json
copy trading_config.json misc_optimization_config.json
copy trading_config.json ai_config.json

echo ✅ 已创建干净的配置文件
echo 💡 现在重新启动WMZC系统，配置警告应该消失
echo 💡 请在主配置页面填写您的真实API密钥

pause
