#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔒 WMZC配置锁定保护脚本
防止配置被任何方式覆盖或重置
"""

import os
import json
import time
from datetime import datetime

class ConfigLockProtector:
    """配置锁定保护器"""
    
    def __init__(self):
        self.config_files = ['trading_config.json', 'wmzc_config.json']
        self.lock_file = '.config_lock'
        
    def create_lock(self):
        """创建配置锁"""
        lock_info = {
            'locked_at': datetime.now().isoformat(),
            'protected_files': self.config_files,
            'lock_reason': 'Ultimate protection against config override'
        }
        
        try:
            with open(self.lock_file, 'w', encoding='utf-8') as f:
                json.dump(lock_info, f, indent=2, ensure_ascii=False)
            print("🔒 配置锁已创建")
            return True
        except Exception as e:
            print(f"❌ 创建配置锁失败: {e}")
            return False
    
    def check_and_protect(self):
        """检查并保护配置"""
        for config_file in self.config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 检查保护标记
                    if not config.get('_CONFIG_PROTECTED'):
                        print(f"⚠️ {config_file} 缺少保护标记，正在修复...")
                        config['_CONFIG_PROTECTED'] = True
                        config['_EMERGENCY_PROTECTION'] = datetime.now().isoformat()
                        
                        with open(config_file, 'w', encoding='utf-8') as f:
                            json.dump(config, f, indent=2, ensure_ascii=False)
                        
                        print(f"✅ {config_file} 保护已修复")
                    
                except Exception as e:
                    print(f"❌ 检查 {config_file} 失败: {e}")
    
    def monitor_config(self, duration=60):
        """监控配置文件变化"""
        print(f"👁️ 开始监控配置文件 {duration} 秒...")
        
        # 记录初始状态
        initial_states = {}
        for config_file in self.config_files:
            if os.path.exists(config_file):
                initial_states[config_file] = os.path.getmtime(config_file)
        
        start_time = time.time()
        while time.time() - start_time < duration:
            for config_file in self.config_files:
                if os.path.exists(config_file):
                    current_mtime = os.path.getmtime(config_file)
                    if config_file in initial_states and current_mtime != initial_states[config_file]:
                        print(f"⚠️ 检测到 {config_file} 被修改，正在检查保护状态...")
                        self.check_and_protect()
                        initial_states[config_file] = current_mtime
            
            time.sleep(1)
        
        print("✅ 配置监控完成")

def main():
    protector = ConfigLockProtector()
    protector.create_lock()
    protector.check_and_protect()
    
    # 可选：启动监控
    # protector.monitor_config(300)  # 监控5分钟

if __name__ == "__main__":
    main()
