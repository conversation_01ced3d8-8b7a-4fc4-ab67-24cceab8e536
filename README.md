# 🚀 量化交易系统 (WMZC Trading System)

一个功能完整的量化交易系统，支持OKX和Gate.io交易所，提供多种技术指标和交易策略。

## ✨ 主要功能

### 📊 技术指标
- **MACD** - 移动平均收敛发散指标
- **RSI** - 相对强弱指数
- **KDJ** - 随机指标
- **布林带** - 布林格带状指标
- **EMA** - 指数移动平均线

### 🎯 交易策略
- **三重确认策略** - RSI + 布林带 + MACD组合
- **布林带突破策略** - 成交量确认突破
- **双金叉策略** - KDJ + MACD双重金叉
- **RSI背离策略** - 顶底背离检测
- **网格交易策略** - 动态网格交易
- **海龟交易法** - 唐奇安通道突破

### 🌐 交易所支持
- **OKX** - 完整API集成
- **Gate.io** - 完整API集成
- 统一接口设计，轻松切换

### 🤖 AI功能
- **内置AI系统** - 智能信号增强
- **LSTM预测** - 深度学习价格预测
- **情感分析** - 市场情绪分析
- **风险评估** - AI驱动的风险管理

## 🛠️ 安装要求

### Python版本
- Python 3.7 或更高版本

### 必需依赖包
```bash
pip install pandas numpy requests tkinter
```

### 可选依赖包（增强功能）
```bash
# 深度学习功能
pip install torch torchvision

# 高级图表
pip install matplotlib plotly

# 技术指标库
pip install talib

# 异步HTTP
pip install aiohttp

# 加密功能
pip install cryptography
```

## 🚀 快速开始

### 1. 下载文件
确保以下文件在同一目录中：
- `WMZC.py` - 主程序文件
- `run_trading_system.py` - 启动脚本

### 2. 检查依赖
```bash
python run_trading_system.py --check-deps
```

### 3. 启动系统
```bash
python run_trading_system.py
```

### 4. 配置API
在GUI界面中配置您的交易所API密钥：
- OKX: API_KEY, SECRET_KEY, PASSPHRASE
- Gate.io: API_KEY, SECRET_KEY

## 📋 使用说明

### 主配置页面
1. **交易所选择** - 选择OKX或Gate.io
2. **交易对设置** - 设置要交易的币种对
3. **API配置** - 输入交易所API密钥
4. **基础参数** - 设置杠杆、金额等

### 策略赶集页面
1. **策略选择** - 选择要使用的交易策略
2. **参数调整** - 调整策略参数
3. **时间周期** - 选择交易时间框架
4. **回测验证** - 验证策略效果

### 技术指标页面
1. **指标计算** - 实时计算技术指标
2. **参数设置** - 调整指标参数
3. **精度验证** - 验证指标准确性

### AI功能页面
1. **AI配置** - 设置AI相关参数
2. **信号增强** - 启用AI信号增强
3. **风险评估** - AI风险管理
4. **预测功能** - 价格预测

## ⚙️ 配置说明

### OKX配置
```json
{
  "EXCHANGE": "OKX",
  "API_KEY": "your_okx_api_key",
  "SECRET_KEY": "your_okx_secret_key", 
  "PASSPHRASE": "your_okx_passphrase",
  "SYMBOL": "BTC-USDT-SWAP"
}
```

### Gate.io配置
```json
{
  "EXCHANGE": "Gate.io",
  "GATE_API_KEY": "your_gate_api_key",
  "GATE_SECRET_KEY": "your_gate_secret_key",
  "SYMBOL": "BTC_USDT"
}
```

## 🔒 安全提醒

1. **API权限** - 只给予必要的交易权限
2. **密钥保护** - 不要在代码中硬编码API密钥
3. **风险控制** - 设置合理的止损和仓位大小
4. **测试环境** - 先在测试环境验证策略

## 📊 监控功能

### 实时监控
- 价格监控
- 持仓监控
- 盈亏监控
- 指标监控

### 风险监控
- 仓位风险
- 流动性风险
- 市场风险
- 系统风险

## 🐛 故障排除

### 常见问题

**1. GUI启动失败**
```bash
# 检查tkinter是否安装
python -c "import tkinter; print('tkinter OK')"
```

**2. API连接失败**
- 检查API密钥是否正确
- 检查网络连接
- 检查API权限设置

**3. 指标计算错误**
- 检查数据源是否正常
- 检查参数设置是否合理
- 查看日志输出

**4. 策略执行异常**
- 检查策略参数
- 检查市场数据
- 查看错误日志

### 日志查看
系统会在以下位置生成日志：
- 控制台输出
- GUI日志面板
- 日志文件（如果配置）

## 📞 支持

如果遇到问题，请：
1. 查看控制台错误信息
2. 检查配置是否正确
3. 确认依赖包已安装
4. 查看日志输出

## ⚠️ 免责声明

本软件仅供学习和研究使用。量化交易存在风险，请：
- 充分了解风险
- 合理控制仓位
- 不要投入超过承受能力的资金
- 在实盘使用前充分测试

使用本软件进行交易的任何损失，开发者不承担责任。

## 📄 许可证

本项目仅供个人学习使用，请勿用于商业用途。
