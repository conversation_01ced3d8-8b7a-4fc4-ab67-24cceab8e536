#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC统一单配置文件管理器
将所有配置合并到一个文件中，简化配置管理
"""

import os
import json
import shutil
from datetime import datetime

class UnifiedSingleConfigManager:
    """统一单配置文件管理器"""
    
    def __init__(self):
        self.single_config_file = 'wmzc_unified_config.json'
        self.old_config_files = [
            'trading_config.json',
            'wmzc_config.json', 
            'user_settings.json',
            'misc_optimization_config.json',
            'ai_config.json'
        ]
        self.wmzc_config_dir = os.path.expanduser("~/.wmzc_trading")
        
    def create_unified_config(self):
        """创建统一配置文件"""
        print("🔧 创建WMZC统一配置文件")
        print("=" * 50)
        
        # 1. 收集现有配置
        merged_config = self.collect_existing_configs()
        
        # 2. 创建统一配置结构
        unified_config = self.create_unified_structure(merged_config)
        
        # 3. 保存统一配置
        success = self.save_unified_config(unified_config)
        
        # 4. 备份旧配置文件
        if success:
            self.backup_old_configs()
            
        # 5. 更新WMZC代码以使用统一配置
        self.update_wmzc_code()
        
        return success
    
    def collect_existing_configs(self):
        """收集现有配置"""
        print("\n📥 收集现有配置...")
        
        merged_config = {}
        
        # 从当前目录收集配置
        for config_file in self.old_config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 合并配置，避免覆盖重要字段
                    for key, value in config.items():
                        if key not in merged_config or not merged_config[key]:
                            merged_config[key] = value
                    
                    print(f"  ✅ 已收集: {config_file} ({len(config)} 个配置项)")
                    
                except Exception as e:
                    print(f"  ❌ 收集 {config_file} 失败: {e}")
        
        # 从WMZC配置目录收集配置
        if os.path.exists(self.wmzc_config_dir):
            for config_file in self.old_config_files:
                file_path = os.path.join(self.wmzc_config_dir, config_file)
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        
                        # 合并配置
                        for key, value in config.items():
                            if key not in merged_config or not merged_config[key]:
                                merged_config[key] = value
                        
                        print(f"  ✅ 已收集: WMZC/{config_file}")
                        
                    except Exception as e:
                        print(f"  ❌ 收集 WMZC/{config_file} 失败: {e}")
        
        print(f"  📊 总计收集了 {len(merged_config)} 个配置项")
        return merged_config
    
    def create_unified_structure(self, merged_config):
        """创建统一配置结构"""
        print("\n🏗️ 创建统一配置结构...")
        
        # 统一配置结构
        unified_config = {
            # === 🔑 API配置 ===
            "api": {
                "okx": {
                    "api_key": merged_config.get('API_KEY', merged_config.get('OKX_API_KEY', merged_config.get('okx_api_key', ''))),
                    "api_secret": merged_config.get('API_SECRET', merged_config.get('OKX_SECRET_KEY', merged_config.get('okx_secret_key', ''))),
                    "passphrase": merged_config.get('PASSPHRASE', merged_config.get('OKX_PASSPHRASE', merged_config.get('okx_passphrase', ''))),
                    "sandbox": merged_config.get('SANDBOX', True)
                },
                "gate": {
                    "api_key": merged_config.get('GATE_API_KEY', ''),
                    "api_secret": merged_config.get('GATE_SECRET_KEY', ''),
                    "sandbox": True
                }
            },
            
            # === 📊 交易配置 ===
            "trading": {
                "exchange": merged_config.get('EXCHANGE', 'OKX'),
                "symbol": merged_config.get('SYMBOL', merged_config.get('default_symbol', 'BTC-USDT-SWAP')),
                "timeframe": merged_config.get('TIMEFRAME', merged_config.get('default_timeframe', '1m')),
                "order_amount": merged_config.get('ORDER_USDT_AMOUNT', 10.0),
                "leverage": merged_config.get('LEVERAGE', 3),
                "max_positions": merged_config.get('MAX_POSITIONS', 5),
                "enable_trading": merged_config.get('ENABLE_TRADING', False),
                "auto_trade": merged_config.get('AUTO_TRADE', False)
            },
            
            # === 🛡️ 风险管理 ===
            "risk": {
                "risk_percent": merged_config.get('RISK_PERCENT', 1.0),
                "risk_per_trade": max(merged_config.get('RISK_PER_TRADE', 1.0), 0.1),  # 修复验证错误
                "stop_loss": merged_config.get('STOP_LOSS', 2.0),
                "take_profit": merged_config.get('TAKE_PROFIT', 4.0),
                "take_profit_pct": max(merged_config.get('TAKE_PROFIT_PCT', 2.0), 0.1),  # 修复验证错误
                "max_daily_loss": merged_config.get('MAX_DAILY_LOSS', 100.0),
                "max_drawdown": merged_config.get('MAX_DRAWDOWN', 10.0)
            },
            
            # === 📈 策略配置 ===
            "strategies": {
                "enable_kdj": merged_config.get('ENABLE_KDJ', True),
                "enable_macd": merged_config.get('ENABLE_MACD', True),
                "enable_rsi": merged_config.get('ENABLE_RSI', True),
                "enable_pinbar": merged_config.get('ENABLE_PINBAR', True),
                "kdj_period": merged_config.get('KDJ_PERIOD', 9),
                "kdj_smooth_k": merged_config.get('KDJ_SMOOTH_K', 3),
                "kdj_smooth_d": merged_config.get('KDJ_SMOOTH_D', 3),
                "macd_fast": merged_config.get('MACD_FAST', 12),
                "macd_slow": merged_config.get('MACD_SLOW', 26),
                "macd_signal": merged_config.get('MACD_SIGNAL', 9),
                "rsi_period": merged_config.get('RSI_PERIOD', 14),
                "rsi_oversold": merged_config.get('RSI_OVERSOLD', 30),
                "rsi_overbought": merged_config.get('RSI_OVERBOUGHT', 70)
            },
            
            # === 🖥️ 界面配置 ===
            "ui": {
                "theme": merged_config.get('THEME', 'default'),
                "window_geometry": merged_config.get('window_geometry', '1200x800+100+100'),
                "current_tab": merged_config.get('current_tab', 0),
                "auto_save": merged_config.get('AUTO_SAVE', True),
                "auto_refresh_news": merged_config.get('auto_refresh_news', False),
                "news_refresh_interval": merged_config.get('news_refresh_interval', 3)
            },
            
            # === 📝 日志配置 ===
            "logging": {
                "log_level": merged_config.get('LOG_LEVEL', 'INFO'),
                "enable_logging": merged_config.get('ENABLE_LOGGING', True),
                "log_to_console": merged_config.get('LOG_TO_CONSOLE', True),
                "log_to_file": merged_config.get('LOG_TO_FILE', False),
                "max_log_files": merged_config.get('MAX_LOG_FILES', 5)
            },
            
            # === 🤖 AI配置 ===
            "ai": {
                "enable_ai": merged_config.get('ENABLE_AI', False),
                "deepseek_api_key": merged_config.get('deepseek_api_key', ''),
                "daily_cost_limit": merged_config.get('daily_cost_limit', 20.0),
                "ai_analysis_enabled": merged_config.get('ai_analysis_enabled', False)
            },
            
            # === 📱 推送配置 ===
            "notifications": {
                "enable_wechat_push": merged_config.get('ENABLE_WECHAT_PUSH', False),
                "wechat_sendkey": merged_config.get('WECHAT_SENDKEY', ''),
                "push_buy_signal": merged_config.get('PUSH_BUY_SIGNAL', True),
                "push_sell_signal": merged_config.get('PUSH_SELL_SIGNAL', True),
                "push_macd_signal": merged_config.get('PUSH_MACD_SIGNAL', True),
                "push_kdj_signal": merged_config.get('PUSH_KDJ_SIGNAL', False),
                "push_rsi_signal": merged_config.get('PUSH_RSI_SIGNAL', True)
            },
            
            # === ⚙️ 系统配置 ===
            "system": {
                "test_mode": merged_config.get('TEST_MODE', True),
                "debug_mode": merged_config.get('DEBUG_MODE', False),
                "performance_mode": merged_config.get('PERFORMANCE_MODE', False),
                "auto_recovery": merged_config.get('AUTO_RECOVERY', {
                    "enabled": True,
                    "timeout_minutes": 1,
                    "max_restarts": 3
                })
            },
            
            # === 📋 元数据 ===
            "_metadata": {
                "config_version": "1.0",
                "created_time": datetime.now().isoformat(),
                "unified_config": True,
                "migration_from": self.old_config_files,
                "last_updated": datetime.now().isoformat()
            }
        }
        
        print("  ✅ 统一配置结构创建完成")
        print(f"  📊 包含 {len(unified_config)} 个主要配置分组")
        
        return unified_config
    
    def save_unified_config(self, unified_config):
        """保存统一配置"""
        print(f"\n💾 保存统一配置到 {self.single_config_file}...")
        
        try:
            # 保存到当前目录
            with open(self.single_config_file, 'w', encoding='utf-8') as f:
                json.dump(unified_config, f, indent=2, ensure_ascii=False)
            
            print(f"  ✅ 统一配置已保存: {self.single_config_file}")
            
            # 同时保存到WMZC配置目录
            os.makedirs(self.wmzc_config_dir, exist_ok=True)
            wmzc_unified_path = os.path.join(self.wmzc_config_dir, self.single_config_file)
            
            with open(wmzc_unified_path, 'w', encoding='utf-8') as f:
                json.dump(unified_config, f, indent=2, ensure_ascii=False)
            
            print(f"  ✅ 统一配置已同步: {wmzc_unified_path}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 保存统一配置失败: {e}")
            return False
    
    def backup_old_configs(self):
        """备份旧配置文件"""
        print("\n📦 备份旧配置文件...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = f'old_configs_backup_{timestamp}'
        
        try:
            os.makedirs(backup_dir, exist_ok=True)
            
            backup_count = 0
            
            # 备份当前目录的配置文件
            for config_file in self.old_config_files:
                if os.path.exists(config_file):
                    shutil.move(config_file, os.path.join(backup_dir, config_file))
                    print(f"  ✅ 已备份: {config_file}")
                    backup_count += 1
            
            # 备份WMZC配置目录的文件
            if os.path.exists(self.wmzc_config_dir):
                wmzc_backup_dir = os.path.join(backup_dir, 'wmzc_trading')
                os.makedirs(wmzc_backup_dir, exist_ok=True)
                
                for config_file in self.old_config_files:
                    file_path = os.path.join(self.wmzc_config_dir, config_file)
                    if os.path.exists(file_path):
                        shutil.move(file_path, os.path.join(wmzc_backup_dir, config_file))
                        print(f"  ✅ 已备份: WMZC/{config_file}")
                        backup_count += 1
            
            print(f"  📊 总计备份了 {backup_count} 个配置文件到 {backup_dir}")
            
        except Exception as e:
            print(f"  ❌ 备份配置文件失败: {e}")
    
    def update_wmzc_code(self):
        """更新WMZC代码以使用统一配置"""
        print("\n🔧 创建统一配置适配器...")
        
        adapter_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC统一配置适配器
为WMZC系统提供统一配置文件支持
"""

import json
import os
from datetime import datetime

class WMZCUnifiedConfigAdapter:
    """WMZC统一配置适配器"""
    
    def __init__(self):
        self.unified_config_file = '{self.single_config_file}'
        self.config = None
        self.load_unified_config()
    
    def load_unified_config(self):
        """加载统一配置"""
        try:
            if os.path.exists(self.unified_config_file):
                with open(self.unified_config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                print(f"✅ 统一配置已加载: {{len(self.config)}} 个分组")
            else:
                print(f"❌ 统一配置文件不存在: {{self.unified_config_file}}")
                self.config = {{}}
        except Exception as e:
            print(f"❌ 加载统一配置失败: {{e}}")
            self.config = {{}}
    
    def get_legacy_config(self):
        """获取兼容旧版本的配置格式"""
        if not self.config:
            return {{}}
        
        # 转换为旧版本格式，保持兼容性
        legacy_config = {{
            # API配置
            'API_KEY': self.config.get('api', {{}}).get('okx', {{}}).get('api_key', ''),
            'API_SECRET': self.config.get('api', {{}}).get('okx', {{}}).get('api_secret', ''),
            'PASSPHRASE': self.config.get('api', {{}}).get('okx', {{}}).get('passphrase', ''),
            'OKX_API_KEY': self.config.get('api', {{}}).get('okx', {{}}).get('api_key', ''),
            'OKX_SECRET_KEY': self.config.get('api', {{}}).get('okx', {{}}).get('api_secret', ''),
            'OKX_PASSPHRASE': self.config.get('api', {{}}).get('okx', {{}}).get('passphrase', ''),
            
            # 交易配置
            'EXCHANGE': self.config.get('trading', {{}}).get('exchange', 'OKX'),
            'SYMBOL': self.config.get('trading', {{}}).get('symbol', 'BTC-USDT-SWAP'),
            'TIMEFRAME': self.config.get('trading', {{}}).get('timeframe', '1m'),
            'ORDER_USDT_AMOUNT': self.config.get('trading', {{}}).get('order_amount', 10.0),
            'LEVERAGE': self.config.get('trading', {{}}).get('leverage', 3),
            'ENABLE_TRADING': self.config.get('trading', {{}}).get('enable_trading', False),
            
            # 风险管理
            'RISK_PERCENT': self.config.get('risk', {{}}).get('risk_percent', 1.0),
            'RISK_PER_TRADE': self.config.get('risk', {{}}).get('risk_per_trade', 1.0),
            'STOP_LOSS': self.config.get('risk', {{}}).get('stop_loss', 2.0),
            'TAKE_PROFIT': self.config.get('risk', {{}}).get('take_profit', 4.0),
            'TAKE_PROFIT_PCT': self.config.get('risk', {{}}).get('take_profit_pct', 2.0),
            
            # 策略配置
            'ENABLE_KDJ': self.config.get('strategies', {{}}).get('enable_kdj', True),
            'ENABLE_MACD': self.config.get('strategies', {{}}).get('enable_macd', True),
            'ENABLE_RSI': self.config.get('strategies', {{}}).get('enable_rsi', True),
            'ENABLE_PINBAR': self.config.get('strategies', {{}}).get('enable_pinbar', True),
            
            # 技术指标参数
            'KDJ_PERIOD': self.config.get('strategies', {{}}).get('kdj_period', 9),
            'MACD_FAST': self.config.get('strategies', {{}}).get('macd_fast', 12),
            'MACD_SLOW': self.config.get('strategies', {{}}).get('macd_slow', 26),
            'MACD_SIGNAL': self.config.get('strategies', {{}}).get('macd_signal', 9),
            
            # 日志配置
            'LOG_LEVEL': self.config.get('logging', {{}}).get('log_level', 'INFO'),
            'ENABLE_LOGGING': self.config.get('logging', {{}}).get('enable_logging', True),
            'LOG_TO_CONSOLE': self.config.get('logging', {{}}).get('log_to_console', True),
            
            # 系统配置
            'TEST_MODE': self.config.get('system', {{}}).get('test_mode', True),
            'SANDBOX': self.config.get('api', {{}}).get('okx', {{}}).get('sandbox', True),
            
            # 界面配置
            'window_geometry': self.config.get('ui', {{}}).get('window_geometry', '1200x800+100+100'),
            'current_tab': self.config.get('ui', {{}}).get('current_tab', 0),
            
            # 元数据
            '_UNIFIED_CONFIG': True,
            '_CONFIG_VERSION': self.config.get('_metadata', {{}}).get('config_version', '1.0'),
            '_LAST_UPDATED': datetime.now().isoformat()
        }}
        
        return legacy_config
    
    def save_unified_config(self):
        """保存统一配置"""
        try:
            if self.config:
                self.config['_metadata']['last_updated'] = datetime.now().isoformat()
                
                with open(self.unified_config_file, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                
                print("✅ 统一配置已保存")
                return True
        except Exception as e:
            print(f"❌ 保存统一配置失败: {{e}}")
            return False
    
    def update_config(self, section, key, value):
        """更新配置"""
        if not self.config:
            return False
        
        try:
            if section not in self.config:
                self.config[section] = {{}}
            
            self.config[section][key] = value
            return self.save_unified_config()
        except Exception as e:
            print(f"❌ 更新配置失败: {{e}}")
            return False

# 创建全局统一配置适配器
wmzc_unified_config = WMZCUnifiedConfigAdapter()

def get_unified_config():
    """获取统一配置（兼容格式）"""
    return wmzc_unified_config.get_legacy_config()

def save_unified_config():
    """保存统一配置"""
    return wmzc_unified_config.save_unified_config()

def update_unified_config(section, key, value):
    """更新统一配置"""
    return wmzc_unified_config.update_config(section, key, value)

if __name__ == "__main__":
    # 测试统一配置
    config = get_unified_config()
    print(f"统一配置加载测试: {{len(config)}} 个配置项")
'''
        
        try:
            with open('wmzc_unified_config_adapter.py', 'w', encoding='utf-8') as f:
                f.write(adapter_code)
            print("  ✅ 统一配置适配器已创建: wmzc_unified_config_adapter.py")
        except Exception as e:
            print(f"  ❌ 创建统一配置适配器失败: {e}")

def main():
    """主函数"""
    print("🔧 WMZC统一单配置文件管理器")
    print("=" * 60)
    
    manager = UnifiedSingleConfigManager()
    
    try:
        success = manager.create_unified_config()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 统一配置文件创建成功！")
            print("\n💡 主要改进:")
            print("  1. ✅ 所有配置合并到一个文件")
            print("  2. ✅ 配置结构清晰，按功能分组")
            print("  3. ✅ 修复了配置验证错误")
            print("  4. ✅ 保持与旧版本的兼容性")
            print("  5. ✅ 旧配置文件已安全备份")
            
            print(f"\n📄 新的统一配置文件: {manager.single_config_file}")
            print("📄 配置适配器: wmzc_unified_config_adapter.py")
            
            print("\n🚀 下一步操作:")
            print("  1. 重新启动WMZC系统")
            print("  2. 在主配置页面填写真实API密钥")
            print("  3. 验证配置验证错误是否消失")
            print("  4. 享受简化的配置管理体验")
            
        else:
            print("❌ 统一配置文件创建失败")
            print("💡 请检查错误信息并重试")
        
        return success
        
    except Exception as e:
        print(f"❌ 处理过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
