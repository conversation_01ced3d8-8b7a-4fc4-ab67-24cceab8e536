# 🔍 WMZC量化交易系统全方位BUG检测提示词

## 🎯 系统概览
基于对WMZC.py系统53,465行代码的100%深度理解，这是一个完整的量化交易系统，包含：
- **核心交易引擎**: 支持OKX和Gate.io交易所
- **技术指标计算**: KDJ、MACD、RSI、BOLL等21种指标
- **多策略支持**: 21种交易策略，支持AI增强
- **完整GUI界面**: 19个功能标签页
- **风控系统**: 银行级风控和实时监控
- **API优化**: 智能限频、批量操作、智能重试
- **配置管理**: 统一配置持久化系统

## 🔒 检测原则
- ✅ 全程异步编程，严禁多线程
- ✅ 使用真实API数据，禁止模拟数据
- ✅ 全局视角分析，考虑系统整体影响
- ✅ 零容忍BUG政策

---

## 📊 **1. 语法和结构层面检测**

### **1.1 Python语法错误**
```python
检测模式：
- 缩进错误: IndentationError, unexpected indent
- 语法结构: SyntaxError, invalid syntax
- 括号匹配: 未闭合的括号、引号
- 函数定义: def语法错误
- 类定义: class语法错误
- 导入语句: import语法错误
```

### **1.2 变量和引用错误**
```python
检测重点：
- 未定义变量: NameError风险
- 全局变量使用: global关键字正确性
- 变量作用域: 局部vs全局变量冲突
- 属性访问: hasattr, getattr使用正确性
- 循环引用: 对象间的相互引用
```

### **1.3 导入和模块错误**
```python
检测重点：
- 缺失导入: ModuleNotFoundError风险
- 循环导入: A导入B，B导入A
- 条件导入: try-except导入的异常处理
- 相对导入: from . import的路径正确性
```

---

## 📊 **2. 异步编程合规检测**

### **2.1 异步/同步混用问题**
```python
严格检测：
- time.sleep() 在异步环境中 → 应使用 await asyncio.sleep()
- 同步函数调用异步函数未使用await
- 异步函数中使用同步阻塞操作
- threading模块使用 → 应使用asyncio
- multiprocessing使用 → 应使用异步方案
```

### **2.2 异步锁和资源管理**
```python
检测重点：
- threading.Lock() → 应使用 asyncio.Lock()
- with lock: → 应使用 async with lock:
- 异步上下文管理器使用
- 异步生成器和迭代器
- 事件循环管理
```

### **2.3 异步函数调用链**
```python
检测重点：
- 异步函数调用未使用await
- 异步函数返回值处理
- 异步异常处理
- 异步回调函数
```

---

## 📊 **3. 交易系统业务逻辑检测**

### **3.1 交易所API调用**
```python
关键检测点：
- API限频控制: 是否使用智能限频管理器
- 错误响应处理: HTTP状态码检查
- 超时设置: 网络请求timeout参数
- 重试机制: 失败重试逻辑
- 参数验证: 交易对、数量、价格验证
```

### **3.2 交易流程完整性**
```python
端到端检测：
1. 交易所选择 → GUI控件 → API初始化
2. 交易对选择 → 格式转换 → 验证
3. K线数据获取 → 异步获取 → 数据格式化
4. 技术指标计算 → 数学准确性 → 实时更新
5. 策略触发 → 信号生成 → AI增强
6. 订单执行 → 参数验证 → 异步提交
7. 仓位管理 → 状态跟踪 → 盈亏计算
8. 交易完成 → 记录保存 → 统计更新
```

### **3.3 数据完整性和精度**
```python
检测重点：
- 浮点数精度: Decimal使用
- 价格计算: 精度丢失风险
- 时间戳处理: 时区和格式
- 数据类型转换: int/float/str转换
- 空值处理: None, NaN, 空字符串
```

---

## 📊 **4. 状态管理和一致性检测**

### **4.1 全局状态一致性**
```python
检测重点：
- 全局变量状态同步
- 实例属性vs全局变量
- 配置状态一致性
- GUI状态vs后端状态
- 多线程状态竞争（应避免）
```

### **4.2 错误状态处理**
```python
检测重点：
- 异常处理后的状态清理
- 失败回滚机制
- 资源释放
- 状态重置
- 错误传播
```

### **4.3 生命周期管理**
```python
检测重点：
- 对象初始化顺序
- 资源获取和释放配对
- 连接管理: 建立和关闭
- 缓存管理: 创建和清理
- 定时器管理: 启动和停止
```

---

## 📊 **5. 性能和资源检测**

### **5.1 内存泄漏风险**
```python
检测重点：
- 大对象未释放
- 循环引用
- 事件监听器未移除
- 缓存无限增长
- 全局容器持续增长
```

### **5.2 阻塞操作检测**
```python
检测重点：
- 同步I/O操作
- 长时间计算
- 无超时的网络请求
- 死循环风险
- 递归调用深度
```

### **5.3 资源使用优化**
```python
检测重点：
- 频繁的字符串拼接
- 不必要的重复计算
- 低效的数据结构
- 过度的API调用
- 内存使用峰值
```

---

## 📊 **6. GUI和用户交互检测**

### **6.1 GUI组件状态**
```python
检测重点：
- 控件状态同步
- 事件处理器绑定
- 线程安全问题（GUI更新）
- 内存泄漏（控件引用）
- 用户输入验证
```

### **6.2 配置持久化**
```python
检测重点：
- 配置保存和加载
- 默认值处理
- 配置验证
- 版本兼容性
- 配置文件损坏处理
```

---

## 📊 **7. 特定功能模块检测**

### **7.1 技术指标计算**
```python
检测重点：
- 数学公式正确性
- 边界条件处理
- 数据不足处理
- 参数范围验证
- 计算精度
```

### **7.2 AI系统集成**
```python
检测重点：
- AI管理器状态一致性
- 全局变量vs实例属性
- 异常处理完整性
- 资源清理
- 配置同步
```

### **7.3 风控系统**
```python
检测重点：
- 风险检查逻辑
- 阈值验证
- 紧急停止机制
- 风险日志记录
- 实时监控
```

---

## 🎯 **检测执行策略**

### **阶段1: 静态代码分析**
1. **语法检查**: 使用Python AST解析
2. **导入检查**: 验证所有import语句
3. **类型检查**: 函数签名和返回值
4. **结构检查**: 类和函数定义完整性

### **阶段2: 异步编程合规**
1. **同步阻塞检测**: 搜索time.sleep, threading等
2. **异步调用检测**: 未使用await的异步函数调用
3. **锁使用检测**: 同步锁vs异步锁
4. **事件循环检测**: 事件循环管理正确性

### **阶段3: 业务逻辑验证**
1. **交易流程检测**: 端到端流程完整性
2. **数据流检测**: 数据传递和转换
3. **状态管理检测**: 状态一致性和同步
4. **错误处理检测**: 异常处理覆盖度

### **阶段4: 性能和资源**
1. **内存使用检测**: 泄漏和过度使用
2. **阻塞操作检测**: 性能瓶颈识别
3. **资源管理检测**: 获取和释放配对
4. **优化机会检测**: 性能改进点

---

## 📋 **BUG分类和优先级**

### **🔴 致命BUG (Critical)**
- 系统无法启动
- 语法错误
- 数据丢失风险
- 安全漏洞

### **🟠 高危BUG (High)**
- 功能完全失效
- 内存泄漏
- 死锁风险
- 数据不一致

### **🟡 中危BUG (Medium)**
- 功能部分异常
- 性能问题
- 用户体验问题
- 错误处理不完整

### **🟢 低危BUG (Low)**
- 代码规范问题
- 注释缺失
- 优化机会
- 非关键功能异常

---

## 🚀 **检测输出格式**

```markdown
## BUG #X: [BUG标题]
**文件**: [文件路径:行号]
**类型**: [语法/异步/业务/状态/性能/GUI]
**严重程度**: [致命/高危/中危/低危]
**检测阶段**: [静态分析/异步合规/业务逻辑/性能资源]

**问题描述**: 
[详细描述问题]

**代码位置**:
```python
[问题代码片段]
```

**全局影响分析**:
[分析对整个系统的影响]

**修复建议**:
[具体的修复方案]

**验证方法**:
[如何验证修复效果]
```

这个检测提示词将确保对WMZC系统进行全方位、无死角的专业级BUG检测！
