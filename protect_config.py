#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 🛡️ WMZC配置保护脚本 - 防止配置被意外覆盖

import os
import json
from datetime import datetime

def protect_user_config():
    """保护用户配置不被覆盖"""
    config_files = ['trading_config.json', 'wmzc_config.json']
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查是否有保护标记
                if config.get('_CONFIG_PROTECTED'):
                    print(f"🛡️ {config_file} 受保护，跳过覆盖")
                    continue
                
                # 添加保护标记
                config['_CONFIG_PROTECTED'] = True
                config['_PROTECTION_ENABLED'] = datetime.now().isoformat()
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                print(f"🛡️ {config_file} 保护已启用")
                
            except Exception as e:
                print(f"❌ 保护 {config_file} 失败: {e}")

if __name__ == "__main__":
    protect_user_config()
