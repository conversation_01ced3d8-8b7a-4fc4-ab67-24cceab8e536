{"current_memory_mb": 366.375, "memory_growth_mb": 351.20703125, "exceeds_336mb_threshold": true, "analysis_results": {"known_sources": [], "circular_references": {"objects_before_gc": 393403, "objects_collected": 54, "objects_after_gc": 393268, "potential_circular_refs": 54}, "cache_systems": {"smart_cache_manager": {"type": "SmartCacheManager", "size": 0, "estimated_memory_mb": 0}, "indicator_cache": {"type": "EnhancedLR<PERSON>ache", "size": 0, "estimated_memory_mb": 0, "null_cache_size": 0, "hit_count": 0, "miss_count": 0, "null_hit_count": 0, "hit_rate": 0, "memory_usage_mb": 0.00030517578125}, "kline_cache": {"type": "function", "size": 0, "estimated_memory_mb": 0}, "kdj_cache": {"type": "EnhancedLR<PERSON>ache", "size": 0, "estimated_memory_mb": 0, "null_cache_size": 0, "hit_count": 0, "miss_count": 0, "null_hit_count": 0, "hit_rate": 0, "memory_usage_mb": 0.00030517578125}, "macd_cache": {"type": "EnhancedLR<PERSON>ache", "size": 0, "estimated_memory_mb": 0, "null_cache_size": 0, "hit_count": 0, "miss_count": 0, "null_hit_count": 0, "hit_rate": 0, "memory_usage_mb": 0.00030517578125}, "total_cache_memory_mb": 0}, "thread_analysis": {"active_threads": 8, "thread_names": ["MainThread", "Thread-1 (optimize_loop)", "Thread-2 (monitoring_loop)", "Thread-3 (monitoring_loop)", "Thread-5 (run_in_thread)", "Thread-6 (_run_performance_monitoring)", "Thread-9 (_run_quality_monitoring)", "Thread-8 (run_in_thread)"], "daemon_threads": 7}, "dataframe_analysis": {"total_dataframes": 26, "estimated_memory_mb": 0.000125885009765625, "large_dataframes": []}, "websocket_analysis": {"websocket_objects": 11, "connection_objects": 0, "potential_leaks": []}}}