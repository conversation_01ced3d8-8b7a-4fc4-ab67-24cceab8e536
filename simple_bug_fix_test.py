#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 简化的Bug修复验证脚本
验证关键修复的有效性
"""

import sys
import os
import traceback
from datetime import datetime

def test_imports():
    """测试基本导入"""
    print("🧪 测试1: 基本导入")
    
    try:
        # 测试pandas导入
        import pandas as pd
        print("   ✅ pandas导入成功")
        
        # 测试numpy导入
        import numpy as np
        print("   ✅ numpy导入成功")
        
        # 测试WMZC模块导入
        sys.path.append('.')
        import WMZC
        print("   ✅ WMZC模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入测试失败: {e}")
        return False

def test_dataframe_safety():
    """测试DataFrame安全检查"""
    print("\n🧪 测试2: DataFrame安全检查")
    
    try:
        import pandas as pd
        import WMZC
        
        # 测试空DataFrame
        empty_df = pd.DataFrame()
        result1 = WMZC._safe_dataframe_check(empty_df)
        print(f"   空DataFrame检查: {result1} (期望: False)")
        
        # 测试有数据的DataFrame
        data_df = pd.DataFrame({'close': [1, 2, 3, 4, 5]})
        result2 = WMZC._safe_dataframe_check(data_df)
        print(f"   有数据DataFrame检查: {result2} (期望: True)")
        
        # 测试None值
        result3 = WMZC._safe_dataframe_check(None)
        print(f"   None值检查: {result3} (期望: False)")
        
        print("   ✅ DataFrame安全检查测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ DataFrame安全检查测试失败: {e}")
        return False

def test_technical_indicators():
    """测试技术指标函数"""
    print("\n🧪 测试3: 技术指标函数")
    
    try:
        import pandas as pd
        import numpy as np
        import WMZC
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'open': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115],
            'high': [105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120],
            'low': [99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114],
            'close': [104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119],
            'volume': [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000, 2100, 2200, 2300, 2400, 2500]
        })
        
        # 测试MACD函数
        try:
            macd_result = WMZC.calculate_macd(test_data)
            if 'macd' in macd_result.columns:
                print("   ✅ MACD函数测试通过")
            else:
                print("   ❌ MACD函数返回结果缺少macd列")
                return False
        except Exception as e:
            print(f"   ❌ MACD函数测试失败: {e}")
            return False
        
        # 测试RSI函数
        try:
            rsi_result = WMZC.calculate_rsi(test_data)
            if 'rsi' in rsi_result.columns:
                print("   ✅ RSI函数测试通过")
            else:
                print("   ❌ RSI函数返回结果缺少rsi列")
                return False
        except Exception as e:
            print(f"   ❌ RSI函数测试失败: {e}")
            return False
        
        # 测试KDJ函数
        try:
            kdj_result = WMZC.calculate_kdj(test_data)
            if all(col in kdj_result.columns for col in ['k', 'd', 'j']):
                print("   ✅ KDJ函数测试通过")
            else:
                print("   ❌ KDJ函数返回结果缺少k/d/j列")
                return False
        except Exception as e:
            print(f"   ❌ KDJ函数测试失败: {e}")
            return False
        
        print("   ✅ 技术指标函数测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 技术指标函数测试失败: {e}")
        traceback.print_exc()
        return False

def test_cache_safety():
    """测试缓存安全性"""
    print("\n🧪 测试4: 缓存安全性")
    
    try:
        import pandas as pd
        import numpy as np
        import WMZC
        
        # 创建测试DataFrame
        df = pd.DataFrame({
            'close': np.random.randn(100),
            'high': np.random.randn(100),
            'low': np.random.randn(100)
        })
        
        params = {'period': 14, 'timeframe': '1m'}
        
        # 测试缓存键生成
        cache_key = WMZC._get_cache_key(df, 'test_indicator', params)
        if cache_key is not None:
            print("   ✅ 缓存键生成成功")
        else:
            print("   ❌ 缓存键生成失败")
            return False
        
        # 测试大型DataFrame（模拟递归风险场景）
        large_df = pd.DataFrame(np.random.randn(1000, 10))
        cache_key2 = WMZC._get_cache_key(large_df, 'test_indicator', params)
        if cache_key2 is not None:
            print("   ✅ 大型DataFrame缓存键生成成功")
        else:
            print("   ❌ 大型DataFrame缓存键生成失败")
            return False
        
        print("   ✅ 缓存安全性测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 缓存安全性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🔧 WMZC系统Bug修复简化验证")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        test_imports,
        test_dataframe_safety,
        test_technical_indicators,
        test_cache_safety
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"   ❌ 测试执行异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Bug修复验证成功")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n退出代码: {0 if success else 1}")
