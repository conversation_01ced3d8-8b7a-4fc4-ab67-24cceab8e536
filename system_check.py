#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC交易系统启动检查脚本
检查系统状态和依赖
"""

import os
import sys
import time
import json
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("✅ Python版本符合要求 (>=3.8)")
        return True
    else:
        print("❌ Python版本过低，建议升级到3.8+")
        return False

def check_dependencies():
    """检查依赖包"""
    dependencies = {
        'core': [
            ('time', '时间处理'),
            ('threading', '线程支持'),
            ('asyncio', '异步支持'),
            ('tkinter', 'GUI界面'),
            ('json', 'JSON处理'),
            ('os', '系统操作')
        ],
        'optional': [
            ('numpy', '数值计算'),
            ('pandas', '数据处理'),
            ('matplotlib', '图表绘制'),
            ('requests', 'HTTP请求'),
            ('talib', '技术指标'),
            ('sklearn', '机器学习'),
            ('torch', '深度学习')
        ]
    }
    
    print("\n📦 检查核心依赖...")
    core_ok = True
    for module, desc in dependencies['core']:
        try:
            __import__(module)
            print(f"✅ {module} ({desc})")
        except ImportError:
            print(f"❌ {module} ({desc}) - 缺失")
            core_ok = False
    
    print("\n📦 检查可选依赖...")
    optional_count = 0
    for module, desc in dependencies['optional']:
        try:
            __import__(module)
            print(f"✅ {module} ({desc})")
            optional_count += 1
        except ImportError:
            print(f"⚠️ {module} ({desc}) - 缺失，将使用模拟对象")
    
    print(f"\n📊 依赖检查结果:")
    print(f"   核心依赖: {'✅ 完整' if core_ok else '❌ 不完整'}")
    print(f"   可选依赖: {optional_count}/{len(dependencies['optional'])} 可用")
    
    return core_ok

def check_config_directory():
    """检查配置目录"""
    print("\n📁 检查配置目录...")
    
    # 获取用户目录
    user_home = os.path.expanduser("~")
    config_dir = os.path.join(user_home, ".wmzc_trading")
    
    try:
        os.makedirs(config_dir, exist_ok=True)
        print(f"✅ 配置目录: {config_dir}")
        
        # 测试写入权限
        test_file = os.path.join(config_dir, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("✅ 配置目录写入权限正常")
        return True
        
    except Exception as e:
        print(f"❌ 配置目录创建失败: {e}")
        
        # 尝试临时目录
        import tempfile
        temp_dir = os.path.join(tempfile.gettempdir(), "wmzc_trading")
        try:
            os.makedirs(temp_dir, exist_ok=True)
            print(f"⚠️ 使用临时目录: {temp_dir}")
            return True
        except Exception as e2:
            print(f"❌ 临时目录也无法创建: {e2}")
            return False

def check_wmzc_module():
    """检查WMZC模块"""
    print("\n🎯 检查WMZC模块...")
    
    try:
        # 检查文件是否存在
        if not os.path.exists("WMZC.py"):
            print("❌ WMZC.py文件不存在")
            return False
        
        print("✅ WMZC.py文件存在")
        
        # 尝试导入模块
        print("🔄 尝试导入WMZC模块...")
        import WMZC
        print("✅ WMZC模块导入成功")
        
        # 检查关键组件
        components = [
            'high_performance_trading_engine',
            'high_performance_data_pipeline',
            'unified_exchange_manager',
            'config_path_manager'
        ]
        
        for component in components:
            if hasattr(WMZC, component):
                print(f"✅ {component} 组件可用")
            else:
                print(f"⚠️ {component} 组件不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ WMZC模块导入失败: {e}")
        return False

def check_system_resources():
    """检查系统资源"""
    print("\n💻 检查系统资源...")
    
    try:
        import psutil
        
        # 内存检查
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"💾 总内存: {memory_gb:.1f} GB")
        print(f"💾 可用内存: {memory.available / (1024**3):.1f} GB ({memory.percent:.1f}% 已使用)")
        
        if memory.available > 1024**3:  # 1GB
            print("✅ 内存充足")
        else:
            print("⚠️ 内存不足，可能影响性能")
        
        # CPU检查
        cpu_count = psutil.cpu_count()
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"🖥️ CPU核心数: {cpu_count}")
        print(f"🖥️ CPU使用率: {cpu_percent:.1f}%")
        
        return True
        
    except ImportError:
        print("⚠️ psutil未安装，跳过系统资源检查")
        return True
    except Exception as e:
        print(f"⚠️ 系统资源检查失败: {e}")
        return True

def main():
    """主检查函数"""
    print("=" * 60)
    print("🔍 WMZC交易系统启动检查")
    print("=" * 60)
    
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("配置目录", check_config_directory),
        ("WMZC模块", check_wmzc_module),
        ("系统资源", check_system_resources)
    ]
    
    results = []
    for name, check_func in checks:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}检查异常: {e}")
            results.append((name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 检查结果总结:")
    print("=" * 60)
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 项检查通过")
    
    if passed >= len(results) - 1:  # 允许1项失败
        print("🎉 系统检查通过，可以启动WMZC交易系统！")
        print("\n💡 启动建议:")
        print("   1. 运行 python install_dependencies.py 安装缺失依赖")
        print("   2. 运行 python 2019启动ZC.py 启动系统")
    else:
        print("⚠️ 系统检查未完全通过，建议先解决问题")
        print("\n🔧 修复建议:")
        print("   1. 升级Python到3.8+版本")
        print("   2. 运行 python install_dependencies.py 安装依赖")
        print("   3. 检查文件权限和磁盘空间")
    
    print("=" * 60)
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
