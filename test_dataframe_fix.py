#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DataFrame布尔值歧义错误修复验证脚本
测试修复后的WMZC系统是否还存在DataFrame布尔值判断问题
"""

import sys
import os
import pandas as pd
import numpy as np
import time

# 添加当前目录到Python路径
sys.path.append('.')

def test_dataframe_ambiguity_fixes():
    """测试DataFrame布尔值歧义修复"""
    print("🔍 测试DataFrame布尔值歧义修复...")
    print("=" * 60)
    
    try:
        # 导入WMZC模块
        import WMZC
        print("✅ WMZC模块导入成功")
        
        # 创建测试数据
        test_data = []
        for i in range(100):
            test_data.append([
                int(time.time() * 1000) + i * 60000,  # timestamp
                50000 + i * 10 + np.random.uniform(-50, 50),  # open
                50000 + i * 10 + np.random.uniform(0, 100),   # high
                50000 + i * 10 + np.random.uniform(-100, 0),  # low
                50000 + i * 10 + np.random.uniform(-25, 25),  # close
                1000 + np.random.uniform(0, 500)              # volume
            ])
        
        print(f"📊 创建测试K线数据: {len(test_data)}条")
        
        # 测试UnifiedExchangeManager的信号获取函数
        if hasattr(WMZC, 'UnifiedExchangeManager'):
            print("\n🔍 测试UnifiedExchangeManager信号获取函数...")
            
            manager = WMZC.UnifiedExchangeManager()
            
            # 测试MACD信号获取
            print("  测试MACD信号获取...")
            try:
                macd_signal = manager._get_macd_signal(test_data)
                print(f"    ✅ MACD信号获取成功: {type(macd_signal)}")
            except Exception as e:
                if "ambiguous" in str(e).lower():
                    print(f"    ❌ MACD信号获取仍有DataFrame歧义错误: {e}")
                else:
                    print(f"    ⚠️ MACD信号获取其他错误: {e}")
            
            # 测试RSI信号获取
            print("  测试RSI信号获取...")
            try:
                rsi_signal = manager._get_rsi_signal(test_data)
                print(f"    ✅ RSI信号获取成功: {type(rsi_signal)}")
            except Exception as e:
                if "ambiguous" in str(e).lower():
                    print(f"    ❌ RSI信号获取仍有DataFrame歧义错误: {e}")
                else:
                    print(f"    ⚠️ RSI信号获取其他错误: {e}")
            
            # 测试KDJ信号获取
            print("  测试KDJ信号获取...")
            try:
                kdj_signal = manager._get_kdj_signal(test_data)
                print(f"    ✅ KDJ信号获取成功: {type(kdj_signal)}")
            except Exception as e:
                if "ambiguous" in str(e).lower():
                    print(f"    ❌ KDJ信号获取仍有DataFrame歧义错误: {e}")
                else:
                    print(f"    ⚠️ KDJ信号获取其他错误: {e}")
            
            # 测试传统信号获取
            print("  测试传统信号获取...")
            try:
                signals = manager._get_traditional_signals(test_data, "BTC-USDT-SWAP")
                print(f"    ✅ 传统信号获取成功: {type(signals)}, 信号数量: {len(signals) if isinstance(signals, dict) else 0}")
            except Exception as e:
                if "ambiguous" in str(e).lower():
                    print(f"    ❌ 传统信号获取仍有DataFrame歧义错误: {e}")
                else:
                    print(f"    ⚠️ 传统信号获取其他错误: {e}")
        
        # 测试技术指标计算函数
        print("\n🔍 测试技术指标计算函数...")
        
        # 创建DataFrame测试数据
        df_test = pd.DataFrame({
            'open': [50000 + i * 10 for i in range(50)],
            'high': [50100 + i * 10 for i in range(50)],
            'low': [49900 + i * 10 for i in range(50)],
            'close': [50050 + i * 10 for i in range(50)],
            'volume': [1000 + i * 10 for i in range(50)]
        })
        
        # 测试MACD计算
        print("  测试MACD计算...")
        try:
            macd_result = WMZC.calculate_macd(df_test)
            if isinstance(macd_result, pd.DataFrame) and not macd_result.empty:
                print(f"    ✅ MACD计算成功: {len(macd_result)}行数据")
            else:
                print(f"    ⚠️ MACD计算返回空数据: {type(macd_result)}")
        except Exception as e:
            if "ambiguous" in str(e).lower():
                print(f"    ❌ MACD计算仍有DataFrame歧义错误: {e}")
            else:
                print(f"    ⚠️ MACD计算其他错误: {e}")
        
        # 测试RSI计算
        print("  测试RSI计算...")
        try:
            rsi_result = WMZC.calculate_rsi(df_test)
            if isinstance(rsi_result, pd.DataFrame) and not rsi_result.empty:
                print(f"    ✅ RSI计算成功: {len(rsi_result)}行数据")
            else:
                print(f"    ⚠️ RSI计算返回空数据: {type(rsi_result)}")
        except Exception as e:
            if "ambiguous" in str(e).lower():
                print(f"    ❌ RSI计算仍有DataFrame歧义错误: {e}")
            else:
                print(f"    ⚠️ RSI计算其他错误: {e}")
        
        # 测试KDJ计算
        print("  测试KDJ计算...")
        try:
            kdj_result = WMZC.calculate_kdj(df_test)
            if isinstance(kdj_result, pd.DataFrame) and not kdj_result.empty:
                print(f"    ✅ KDJ计算成功: {len(kdj_result)}行数据")
            else:
                print(f"    ⚠️ KDJ计算返回空数据: {type(kdj_result)}")
        except Exception as e:
            if "ambiguous" in str(e).lower():
                print(f"    ❌ KDJ计算仍有DataFrame歧义错误: {e}")
            else:
                print(f"    ⚠️ KDJ计算其他错误: {e}")
        
        print("\n🎉 DataFrame布尔值歧义修复测试完成")
        
    except ImportError as e:
        print(f"❌ WMZC模块导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_specific_dataframe_conditions():
    """测试特定的DataFrame条件判断"""
    print("\n🔍 测试特定的DataFrame条件判断...")
    print("=" * 60)
    
    try:
        import pandas as pd
        
        # 创建测试DataFrame
        df = pd.DataFrame({
            'close': [100, 101, 102, 103, 104],
            'macd': [0.1, 0.2, 0.3, 0.4, 0.5],
            'macd_signal': [0.15, 0.25, 0.35, 0.45, 0.55],
            'j': [20, 30, 40, 50, 60]
        })
        
        print("📊 创建测试DataFrame成功")
        
        # 测试修复后的条件判断
        print("  测试修复后的条件判断...")
        
        # 测试1: 分步检查DataFrame
        if df is not None:
            if isinstance(df, pd.DataFrame):
                if not df.empty:
                    if 'close' in df.columns:
                        print("    ✅ 分步DataFrame检查成功")
                    else:
                        print("    ❌ 缺少close列")
                else:
                    print("    ❌ DataFrame为空")
            else:
                print("    ❌ 不是DataFrame类型")
        else:
            print("    ❌ DataFrame为None")
        
        # 测试2: 避免复合条件判断
        macd_check_passed = False
        if df is not None:
            if isinstance(df, pd.DataFrame):
                if not df.empty:
                    if 'macd' in df.columns and 'macd_signal' in df.columns:
                        macd_check_passed = True
        
        if macd_check_passed:
            print("    ✅ MACD列检查成功")
        else:
            print("    ❌ MACD列检查失败")
        
        print("🎉 特定DataFrame条件判断测试完成")
        
    except Exception as e:
        print(f"❌ 特定条件判断测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始DataFrame布尔值歧义修复验证")
    print("=" * 80)
    
    test_dataframe_ambiguity_fixes()
    test_specific_dataframe_conditions()
    
    print("\n" + "=" * 80)
    print("✅ 验证完成")
