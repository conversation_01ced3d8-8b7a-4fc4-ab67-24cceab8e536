#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确检查WMZC.py中的文件操作安全性
"""

import re

def check_unsafe_file_operations():
    """精确检查不安全的文件操作"""
    
    with open('WMZC.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    unsafe_operations = []
    
    for i, line in enumerate(lines, 1):
        line_stripped = line.strip()
        
        # 跳过注释行
        if line_stripped.startswith('#'):
            continue
            
        # 检查是否包含open(但不在with语句中
        if 'open(' in line and 'with' not in line:
            # 排除一些特殊情况
            if any(keyword in line for keyword in [
                'webbrowser.open',  # 浏览器打开
                'urlopen',          # URL打开
                '_is_circuit_breaker_open',  # 函数名
                'def.*open',        # 函数定义
                'class.*open',      # 类定义
                'open_',            # 以open_开头的函数名
                '.open(',           # 方法调用
                'gzip.open'         # gzip.open已经是安全的
            ]):
                continue
                
            # 检查是否是真正的文件open调用
            if re.search(r'\bopen\s*\(', line):
                unsafe_operations.append({
                    'line_number': i,
                    'line_content': line_stripped,
                    'context': lines[max(0, i-2):i+2] if i > 1 else lines[i-1:i+2]
                })
    
    return unsafe_operations

def main():
    print("🔍 精确检查WMZC.py文件操作安全性")
    print("=" * 50)
    
    unsafe_ops = check_unsafe_file_operations()
    
    if not unsafe_ops:
        print("✅ 未发现真正的不安全文件操作")
        print("所有文件操作都正确使用了with语句或其他安全方式")
    else:
        print(f"⚠️ 发现 {len(unsafe_ops)} 个潜在的不安全文件操作:")
        print()
        
        for i, op in enumerate(unsafe_ops, 1):
            print(f"问题 {i}:")
            print(f"  行号: {op['line_number']}")
            print(f"  内容: {op['line_content']}")
            print(f"  上下文:")
            for j, context_line in enumerate(op['context']):
                marker = ">>> " if j == 1 else "    "
                print(f"    {marker}{context_line.rstrip()}")
            print()
    
    print("=" * 50)
    print("✅ 检查完成")

if __name__ == "__main__":
    main()
