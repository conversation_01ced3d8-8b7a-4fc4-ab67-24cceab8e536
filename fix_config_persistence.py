#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC配置持久化问题修复脚本
解决API密钥配置丢失和其他配置项无法持久化的问题
"""

import os
import json
import shutil
from datetime import datetime

class ConfigPersistenceFixer:
    """配置持久化修复器"""
    
    def __init__(self):
        self.config_files = {
            'trading_config.json': 'trading_config.json',
            'wmzc_config.json': 'wmzc_config.json',
            'user_settings.json': 'user_settings.json'
        }
        self.backup_dir = 'config_backups'
        
    def diagnose_config_issues(self):
        """诊断配置持久化问题"""
        print("🔍 开始诊断配置持久化问题...")
        print("=" * 60)
        
        issues = []
        
        # 1. 检查配置文件是否存在
        print("📁 检查配置文件存在性:")
        for name, path in self.config_files.items():
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        content = json.load(f)
                    size = len(content) if isinstance(content, dict) else 0
                    print(f"  ✅ {name}: 存在 ({size} 个配置项)")
                    
                    # 检查是否为空
                    if not content or (isinstance(content, dict) and len(content) == 0):
                        issues.append(f"{name} 文件为空")
                        print(f"    ⚠️ 文件为空")
                        
                except json.JSONDecodeError as e:
                    issues.append(f"{name} JSON格式错误: {e}")
                    print(f"    ❌ JSON格式错误: {e}")
                except Exception as e:
                    issues.append(f"{name} 读取失败: {e}")
                    print(f"    ❌ 读取失败: {e}")
            else:
                issues.append(f"{name} 文件不存在")
                print(f"  ❌ {name}: 不存在")
        
        # 2. 检查API密钥配置
        print("\n🔑 检查API密钥配置:")
        api_keys_found = False
        
        for name, path in self.config_files.items():
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 检查不同的API密钥字段
                    api_fields = [
                        'API_KEY', 'OKX_API_KEY', 'okx_api_key',
                        'API_SECRET', 'SECRET_KEY', 'OKX_SECRET_KEY', 'okx_secret_key',
                        'PASSPHRASE', 'OKX_PASSPHRASE', 'okx_passphrase'
                    ]
                    
                    found_fields = []
                    for field in api_fields:
                        if field in config and config[field]:
                            found_fields.append(field)
                            api_keys_found = True
                    
                    if found_fields:
                        print(f"  ✅ {name}: 找到API字段 {found_fields}")
                    else:
                        print(f"  ⚠️ {name}: 未找到API配置")
                        
                except Exception as e:
                    print(f"  ❌ {name}: 检查失败 {e}")
        
        if not api_keys_found:
            issues.append("所有配置文件中都未找到API密钥")
        
        # 3. 检查文件权限
        print("\n🔒 检查文件权限:")
        for name, path in self.config_files.items():
            try:
                # 测试写入权限
                test_file = f"{path}.test"
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                print(f"  ✅ {name}: 写入权限正常")
            except Exception as e:
                issues.append(f"{name} 写入权限问题: {e}")
                print(f"  ❌ {name}: 写入权限问题 {e}")
        
        # 4. 检查配置文件结构一致性
        print("\n📋 检查配置文件结构:")
        configs = {}
        for name, path in self.config_files.items():
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        configs[name] = json.load(f)
                except:
                    configs[name] = {}
        
        # 检查API字段一致性
        api_configs = {}
        for name, config in configs.items():
            api_configs[name] = {
                'api_key': config.get('API_KEY') or config.get('OKX_API_KEY') or config.get('okx_api_key'),
                'api_secret': config.get('API_SECRET') or config.get('SECRET_KEY') or config.get('OKX_SECRET_KEY') or config.get('okx_secret_key'),
                'passphrase': config.get('PASSPHRASE') or config.get('OKX_PASSPHRASE') or config.get('okx_passphrase')
            }
        
        # 检查不一致性
        if len(set(str(v) for v in api_configs.values())) > 1:
            issues.append("配置文件间API配置不一致")
            print("  ⚠️ 配置文件间API配置不一致")
            for name, api_config in api_configs.items():
                print(f"    {name}: {api_config}")
        else:
            print("  ✅ 配置文件间API配置一致")
        
        print("\n" + "=" * 60)
        if issues:
            print(f"❌ 发现 {len(issues)} 个问题:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
        else:
            print("✅ 未发现配置持久化问题")
        
        return issues
    
    def create_backup(self):
        """创建配置文件备份"""
        print("\n💾 创建配置文件备份...")
        
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        for name, path in self.config_files.items():
            if os.path.exists(path):
                backup_path = os.path.join(self.backup_dir, f"{name}.backup_{timestamp}")
                try:
                    shutil.copy2(path, backup_path)
                    print(f"  ✅ {name} -> {backup_path}")
                except Exception as e:
                    print(f"  ❌ 备份 {name} 失败: {e}")
    
    def fix_config_persistence(self):
        """修复配置持久化问题"""
        print("\n🔧 开始修复配置持久化问题...")
        
        # 创建备份
        self.create_backup()
        
        # 1. 修复trading_config.json
        self.fix_trading_config()
        
        # 2. 统一API配置
        self.unify_api_config()
        
        # 3. 验证修复结果
        self.verify_fixes()
    
    def fix_trading_config(self):
        """修复trading_config.json"""
        print("\n📝 修复 trading_config.json...")
        
        config_path = 'trading_config.json'
        
        # 读取现有配置
        existing_config = {}
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
            except:
                existing_config = {}
        
        # 创建完整的默认配置
        default_config = {
            # API配置
            "API_KEY": "",
            "API_SECRET": "",
            "PASSPHRASE": "",
            "OKX_API_KEY": "",
            "OKX_SECRET_KEY": "",
            "OKX_PASSPHRASE": "",
            
            # 基本交易配置
            "EXCHANGE": "OKX",
            "SYMBOL": "BTC-USDT-SWAP",
            "TIMEFRAME": "1m",
            "ORDER_USDT_AMOUNT": 10,
            "LEVERAGE": 3,
            "RISK_PERCENT": 1.0,
            
            # 策略配置
            "ENABLE_KDJ": True,
            "ENABLE_MACD": True,
            "ENABLE_PINBAR": True,
            "ENABLE_RSI": True,
            
            # 系统配置
            "LOG_LEVEL": "INFO",
            "AUTO_SAVE": True,
            "THEME": "default"
        }
        
        # 合并配置（保留现有值）
        for key, default_value in default_config.items():
            if key not in existing_config:
                existing_config[key] = default_value
        
        # 保存配置
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, indent=2, ensure_ascii=False)
            print(f"  ✅ {config_path} 修复完成")
        except Exception as e:
            print(f"  ❌ 修复 {config_path} 失败: {e}")
    
    def unify_api_config(self):
        """统一API配置"""
        print("\n🔗 统一API配置...")
        
        # 从所有配置文件中收集API配置
        api_config = {
            'api_key': '',
            'api_secret': '',
            'passphrase': ''
        }
        
        for name, path in self.config_files.items():
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 提取API配置
                    if not api_config['api_key']:
                        api_config['api_key'] = (config.get('API_KEY') or 
                                               config.get('OKX_API_KEY') or 
                                               config.get('okx_api_key') or '')
                    
                    if not api_config['api_secret']:
                        api_config['api_secret'] = (config.get('API_SECRET') or 
                                                   config.get('SECRET_KEY') or
                                                   config.get('OKX_SECRET_KEY') or 
                                                   config.get('okx_secret_key') or '')
                    
                    if not api_config['passphrase']:
                        api_config['passphrase'] = (config.get('PASSPHRASE') or 
                                                   config.get('OKX_PASSPHRASE') or 
                                                   config.get('okx_passphrase') or '')
                        
                except Exception as e:
                    print(f"  ⚠️ 读取 {name} 失败: {e}")
        
        # 将统一的API配置写入所有文件
        for name, path in self.config_files.items():
            try:
                # 读取现有配置
                config = {}
                if os.path.exists(path):
                    with open(path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                
                # 更新API配置
                if name == 'wmzc_config.json':
                    # wmzc_config.json使用特定字段名
                    config['okx_api_key'] = api_config['api_key']
                    config['okx_secret_key'] = api_config['api_secret']
                    config['okx_passphrase'] = api_config['passphrase']
                else:
                    # 其他文件使用标准字段名
                    config['API_KEY'] = api_config['api_key']
                    config['OKX_API_KEY'] = api_config['api_key']
                    config['API_SECRET'] = api_config['api_secret']
                    config['SECRET_KEY'] = api_config['api_secret']
                    config['OKX_SECRET_KEY'] = api_config['api_secret']
                    config['PASSPHRASE'] = api_config['passphrase']
                    config['OKX_PASSPHRASE'] = api_config['passphrase']
                
                # 保存配置
                with open(path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                print(f"  ✅ {name} API配置已统一")
                
            except Exception as e:
                print(f"  ❌ 统一 {name} API配置失败: {e}")
    
    def verify_fixes(self):
        """验证修复结果"""
        print("\n✅ 验证修复结果...")
        
        issues = self.diagnose_config_issues()
        
        if not issues:
            print("\n🎉 配置持久化问题修复成功！")
            print("💡 现在您可以:")
            print("  1. 重新启动WMZC系统")
            print("  2. 在主配置页面填写API密钥")
            print("  3. 配置将自动保存并在重启后保留")
        else:
            print(f"\n⚠️ 仍有 {len(issues)} 个问题需要手动处理")

def main():
    """主函数"""
    print("🚀 WMZC配置持久化问题修复工具")
    print("=" * 60)
    
    fixer = ConfigPersistenceFixer()
    
    # 诊断问题
    issues = fixer.diagnose_config_issues()
    
    if issues:
        print(f"\n🔧 发现 {len(issues)} 个问题，开始修复...")
        fixer.fix_config_persistence()
    else:
        print("\n✅ 配置持久化系统正常，无需修复")
    
    print("\n" + "=" * 60)
    print("🎯 修复完成！请重启WMZC系统测试配置持久化功能")

if __name__ == "__main__":
    main()
