#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC日志初始化增强脚本
确保日志控制台在正确的时机初始化并连接
"""

import logging
import sys
from datetime import datetime

class WMZCLogInitializer:
    """WMZC日志初始化器"""
    
    def __init__(self):
        self.log_handlers = []
        self.gui_callback = None
        
    def setup_enhanced_logging(self):
        """设置增强的日志系统"""
        try:
            # 创建根日志记录器
            root_logger = logging.getLogger()
            root_logger.setLevel(logging.DEBUG)
            
            # 清除现有处理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            
            # 创建格式器
            formatter = logging.Formatter(
                '[%(asctime)s] %(levelname)s - %(message)s',
                datefmt='%H:%M:%S'
            )
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            root_logger.addHandler(console_handler)
            self.log_handlers.append(console_handler)
            
            print("✅ 增强日志系统初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 增强日志系统初始化失败: {e}")
            return False
    
    def set_gui_callback(self, callback):
        """设置GUI回调"""
        self.gui_callback = callback
        print("✅ GUI日志回调已设置")
    
    def test_log_output(self):
        """测试日志输出"""
        try:
            # 测试不同级别的日志
            logging.debug("DEBUG级别测试消息")
            logging.info("INFO级别测试消息")
            logging.warning("WARNING级别测试消息")
            logging.error("ERROR级别测试消息")
            
            # 如果有GUI回调，也测试GUI输出
            if self.gui_callback:
                self.gui_callback("GUI日志测试消息", "INFO")
                self.gui_callback("GUI警告测试消息", "WARNING")
                self.gui_callback("GUI错误测试消息", "ERROR")
            
            print("✅ 日志输出测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 日志输出测试失败: {e}")
            return False

# 创建全局日志初始化器
wmzc_log_initializer = WMZCLogInitializer()

def initialize_wmzc_logging():
    """初始化WMZC日志系统"""
    return wmzc_log_initializer.setup_enhanced_logging()

def set_wmzc_gui_callback(callback):
    """设置WMZC GUI回调"""
    wmzc_log_initializer.set_gui_callback(callback)

def test_wmzc_logging():
    """测试WMZC日志系统"""
    return wmzc_log_initializer.test_log_output()

if __name__ == "__main__":
    initialize_wmzc_logging()
    test_wmzc_logging()
