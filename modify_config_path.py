#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC配置路径修改工具
支持将配置文件存储位置从 ~/.wmzc_trading 修改到其他位置
"""

import os
import json
import shutil
from datetime import datetime

class ConfigPathModifier:
    """配置路径修改器"""
    
    def __init__(self):
        self.current_config_dir = os.path.expanduser("~/.wmzc_trading")
        self.config_files = [
            'wmzc_config.json',
            'trading_config.json', 
            'user_settings.json',
            'misc_optimization_config.json',
            'ai_config.json'
        ]
        
    def get_available_paths(self):
        """获取可用的配置路径选项"""
        paths = {
            'current': self.current_config_dir,
            'appdata': os.path.join(os.environ.get('APPDATA', ''), 'WMZC'),
            'localappdata': os.path.join(os.environ.get('LOCALAPPDATA', ''), 'WMZC'),
            'programdata': os.path.join(os.environ.get('PROGRAMDATA', ''), 'WMZC'),
            'program_dir': os.path.join(os.getcwd(), 'config'),
            'documents': os.path.join(os.path.expanduser("~"), 'Documents', 'WMZC'),
            'desktop': os.path.join(os.path.expanduser("~"), 'Desktop', 'WMZC_Config')
        }
        return paths
    
    def show_path_options(self):
        """显示路径选项"""
        print("📁 可用的配置路径选项:")
        print("=" * 60)
        
        paths = self.get_available_paths()
        
        for i, (name, path) in enumerate(paths.items(), 1):
            exists = "✅ 存在" if os.path.exists(path) else "❌ 不存在"
            writable = self.test_write_permission(path)
            permission = "✅ 可写" if writable else "❌ 无权限"
            
            print(f"{i}. {name.upper()}")
            print(f"   路径: {path}")
            print(f"   状态: {exists} | 权限: {permission}")
            
            if name == 'current':
                file_count = self.count_config_files(path)
                print(f"   配置文件: {file_count} 个")
            
            print()
    
    def test_write_permission(self, path):
        """测试写入权限"""
        try:
            # 确保目录存在
            os.makedirs(path, exist_ok=True)
            
            # 测试写入
            test_file = os.path.join(path, 'test_write.tmp')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            return True
        except:
            return False
    
    def count_config_files(self, path):
        """统计配置文件数量"""
        if not os.path.exists(path):
            return 0
        
        count = 0
        for config_file in self.config_files:
            if os.path.exists(os.path.join(path, config_file)):
                count += 1
        return count
    
    def migrate_configs(self, target_path):
        """迁移配置文件"""
        print(f"🔄 迁移配置文件到: {target_path}")
        print("=" * 60)
        
        # 创建目标目录
        try:
            os.makedirs(target_path, exist_ok=True)
            print(f"✅ 目标目录已创建: {target_path}")
        except Exception as e:
            print(f"❌ 创建目标目录失败: {e}")
            return False
        
        # 备份现有配置
        backup_dir = os.path.join(target_path, f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        
        migrated_count = 0
        
        for config_file in self.config_files:
            source_path = os.path.join(self.current_config_dir, config_file)
            target_file_path = os.path.join(target_path, config_file)
            
            if os.path.exists(source_path):
                try:
                    # 如果目标文件已存在，先备份
                    if os.path.exists(target_file_path):
                        os.makedirs(backup_dir, exist_ok=True)
                        backup_file = os.path.join(backup_dir, config_file)
                        shutil.copy2(target_file_path, backup_file)
                        print(f"  💾 已备份现有文件: {config_file}")
                    
                    # 复制配置文件
                    shutil.copy2(source_path, target_file_path)
                    print(f"  ✅ 已迁移: {config_file}")
                    migrated_count += 1
                    
                except Exception as e:
                    print(f"  ❌ 迁移 {config_file} 失败: {e}")
            else:
                print(f"  ⚠️ 源文件不存在: {config_file}")
        
        print(f"\n📊 迁移结果: 成功迁移 {migrated_count}/{len(self.config_files)} 个文件")
        return migrated_count > 0
    
    def update_wmzc_code(self, new_config_path):
        """更新WMZC代码中的配置路径"""
        print(f"\n🔧 更新WMZC代码中的配置路径...")
        
        # 需要修改的文件
        files_to_modify = ['WMZC.py']
        
        for file_name in files_to_modify:
            if os.path.exists(file_name):
                try:
                    # 备份原文件
                    backup_name = f"{file_name}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.copy2(file_name, backup_name)
                    print(f"  💾 已备份: {file_name} -> {backup_name}")
                    
                    # 读取文件内容
                    with open(file_name, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 替换配置路径
                    old_pattern = 'os.path.join(self.user_home, ".wmzc_trading")'
                    new_pattern = f'"{new_config_path}"'
                    
                    if old_pattern in content:
                        content = content.replace(old_pattern, new_pattern)
                        
                        # 保存修改后的文件
                        with open(file_name, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        print(f"  ✅ 已更新: {file_name}")
                    else:
                        print(f"  ⚠️ 未找到需要替换的模式: {file_name}")
                        
                except Exception as e:
                    print(f"  ❌ 更新 {file_name} 失败: {e}")
            else:
                print(f"  ❌ 文件不存在: {file_name}")
    
    def create_path_config_file(self, new_config_path):
        """创建配置路径配置文件"""
        path_config = {
            'config_directory': new_config_path,
            'migration_date': datetime.now().isoformat(),
            'original_path': self.current_config_dir,
            'migration_reason': 'User requested path change'
        }
        
        try:
            config_file = os.path.join(new_config_path, 'path_config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(path_config, f, indent=2, ensure_ascii=False)
            print(f"  ✅ 路径配置文件已创建: path_config.json")
        except Exception as e:
            print(f"  ❌ 创建路径配置文件失败: {e}")

def main():
    """主函数"""
    print("🔧 WMZC配置路径修改工具")
    print("=" * 60)
    
    modifier = ConfigPathModifier()
    
    # 显示当前状态
    print("📊 当前配置状态:")
    current_files = modifier.count_config_files(modifier.current_config_dir)
    print(f"当前路径: {modifier.current_config_dir}")
    print(f"配置文件: {current_files} 个")
    print()
    
    # 显示路径选项
    modifier.show_path_options()
    
    # 用户选择
    print("请选择新的配置路径:")
    print("1. APPDATA (推荐Windows用户)")
    print("2. LOCALAPPDATA")
    print("3. 程序目录/config")
    print("4. 文档/WMZC")
    print("5. 桌面/WMZC_Config")
    print("6. 自定义路径")
    print("0. 取消")
    
    try:
        choice = input("\n请输入选择 (0-6): ").strip()
        
        if choice == '0':
            print("操作已取消")
            return
        
        paths = modifier.get_available_paths()
        path_list = list(paths.values())[1:]  # 排除current
        
        if choice in ['1', '2', '3', '4', '5']:
            target_path = path_list[int(choice) - 1]
        elif choice == '6':
            target_path = input("请输入自定义路径: ").strip()
            if not target_path:
                print("路径不能为空")
                return
        else:
            print("无效选择")
            return
        
        print(f"\n目标路径: {target_path}")
        
        # 确认操作
        confirm = input("确认迁移配置文件? (y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return
        
        # 执行迁移
        success = modifier.migrate_configs(target_path)
        
        if success:
            # 更新代码
            update_code = input("是否更新WMZC代码中的配置路径? (y/N): ").strip().lower()
            if update_code == 'y':
                modifier.update_wmzc_code(target_path)
            
            # 创建路径配置文件
            modifier.create_path_config_file(target_path)
            
            print("\n🎉 配置路径迁移完成!")
            print(f"新配置路径: {target_path}")
            print("💡 请重新启动WMZC系统以使用新路径")
        else:
            print("\n❌ 配置路径迁移失败")
        
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        print(f"\n❌ 操作失败: {e}")

if __name__ == "__main__":
    main()
