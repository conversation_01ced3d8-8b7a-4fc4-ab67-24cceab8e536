#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC配置警告诊断工具
分析配置文件加载行为和未知字段警告
"""

import os
import json
from pathlib import Path

class ConfigWarningDiagnoser:
    """配置警告诊断器"""
    
    def __init__(self):
        self.current_dir = os.getcwd()
        self.user_config_dir = os.path.expanduser("~/.wmzc_trading")
        self.config_files = [
            'trading_config.json',
            'wmzc_config.json',
            'user_settings.json',
            'misc_optimization_config.json',
            'ai_config.json'
        ]
        
        # WMZC系统认可的配置字段（从CONFIG_SCHEMA提取）
        self.known_fields = {
            # API配置
            'API_KEY', 'API_SECRET', 'PASSPHRASE',
            'OKX_API_KEY', 'OKX_SECRET_KEY', 'OKX_PASSPHRASE',
            'GATE_API_KEY', 'GATE_SECRET_KEY',
            
            # 交易配置
            'SYMBOL', 'TIMEFRAME', 'EXCHANGE', 'CURRENT_EXCHANGE',
            'AMOUNT', 'ORDER_AMOUNT', 'ORDER_USDT_AMOUNT', 'LEVERAGE', 'RISK_PERCENT',
            
            # 交易开关
            'ENABLE_TRADING', 'AUTO_TRADING', 'TEST_MODE',
            
            # 策略开关
            'ENABLE_KDJ', 'ENABLE_MACD', 'ENABLE_PINBAR', 'ENABLE_STOP_LOSS',
            'ENABLE_TAKE_PROFIT', 'ENABLE_ADVANCED_MACD',
            
            # 止盈止损
            'STOP_LOSS_PCT', 'TAKE_PROFIT_PCT', 'PROFIT_TARGET_PCT',
            'MAX_POSITION_SIZE', 'RISK_PER_TRADE', 'MAX_DAILY_TRADES',
            
            # 技术指标
            'KDJ', 'MACD', 'RSI', 'PINBAR', 'TECHNICAL_INDICATORS',
            
            # 界面配置
            'THEME', 'window_geometry', 'current_tab', 'font_size', 'update_interval',
            
            # 系统配置
            'LOG_LEVEL', 'AUTO_SAVE', 'SANDBOX', 'DEFAULT_SYMBOL', 'DEFAULT_TIMEFRAME',
            'INDICATOR_SYNC',
            
            # WMZC特定字段
            'exchange_selection', 'okx_api_key', 'okx_secret_key', 'okx_passphrase',
            'default_symbol', 'default_timeframe'
        }
    
    def run_diagnosis(self):
        """运行配置诊断"""
        print("🔍 WMZC配置警告诊断")
        print("=" * 60)
        
        # 1. 分析配置文件加载路径
        self.analyze_config_paths()
        
        # 2. 检查配置文件内容
        self.check_config_files()
        
        # 3. 识别未知字段
        self.identify_unknown_fields()
        
        # 4. 提供解决方案
        self.provide_solutions()
    
    def analyze_config_paths(self):
        """分析配置文件加载路径"""
        print("\n📁 配置文件路径分析...")
        
        print(f"当前工作目录: {self.current_dir}")
        print(f"用户配置目录: {self.user_config_dir}")
        
        # 检查各目录中的配置文件
        print("\n📄 配置文件分布:")
        
        for config_file in self.config_files:
            # 当前目录
            current_path = os.path.join(self.current_dir, config_file)
            current_exists = os.path.exists(current_path)
            
            # 用户目录
            user_path = os.path.join(self.user_config_dir, config_file)
            user_exists = os.path.exists(user_path)
            
            print(f"  {config_file}:")
            print(f"    当前目录: {'✅ 存在' if current_exists else '❌ 不存在'}")
            print(f"    用户目录: {'✅ 存在' if user_exists else '❌ 不存在'}")
            
            if user_exists:
                try:
                    with open(user_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            config = json.loads(content)
                            print(f"    用户目录文件大小: {len(config)} 个字段")
                        else:
                            print(f"    用户目录文件: 空文件")
                except Exception as e:
                    print(f"    用户目录文件: 读取失败 - {e}")
    
    def check_config_files(self):
        """检查配置文件内容"""
        print("\n📋 配置文件内容检查...")
        
        # 检查用户目录的配置文件
        if os.path.exists(self.user_config_dir):
            for config_file in self.config_files:
                file_path = os.path.join(self.user_config_dir, config_file)
                if os.path.exists(file_path):
                    self.analyze_config_file(file_path, config_file)
        else:
            print("⚠️ 用户配置目录不存在")
        
        # 检查当前目录的配置文件
        print("\n📋 当前目录配置文件:")
        for config_file in self.config_files:
            file_path = os.path.join(self.current_dir, config_file)
            if os.path.exists(file_path):
                self.analyze_config_file(file_path, config_file)
    
    def analyze_config_file(self, file_path, file_name):
        """分析单个配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
            if not content or content == '{}':
                print(f"  {file_name}: 空配置文件")
                return
            
            config = json.loads(content)
            print(f"\n  📄 {file_name} ({len(config)} 个字段):")
            
            # 分类字段
            known_fields = []
            unknown_fields = []
            metadata_fields = []
            
            for key in config.keys():
                if key.startswith('_'):
                    metadata_fields.append(key)
                elif key in self.known_fields:
                    known_fields.append(key)
                else:
                    unknown_fields.append(key)
            
            print(f"    ✅ 已知字段 ({len(known_fields)}): {', '.join(known_fields[:5])}{'...' if len(known_fields) > 5 else ''}")
            print(f"    📋 元数据字段 ({len(metadata_fields)}): {', '.join(metadata_fields)}")
            
            if unknown_fields:
                print(f"    ⚠️ 未知字段 ({len(unknown_fields)}): {', '.join(unknown_fields)}")
                
                # 详细显示未知字段
                for field in unknown_fields:
                    value = config[field]
                    value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    print(f"      • {field}: {value_str}")
            
        except Exception as e:
            print(f"  ❌ {file_name}: 分析失败 - {e}")
    
    def identify_unknown_fields(self):
        """识别未知字段的来源"""
        print("\n🔍 未知字段来源分析...")
        
        # 常见的"未知字段"及其来源
        common_unknown_fields = {
            'ENABLE_LOGGING': '日志配置字段',
            'LOG_TO_CONSOLE': '日志配置字段',
            'LOG_TO_FILE': '日志配置字段',
            'ENABLE_RSI': '策略配置字段',
            'RSI_PERIOD': '技术指标参数',
            'RSI_OVERSOLD': '技术指标参数',
            'RSI_OVERBOUGHT': '技术指标参数',
            'KDJ_PERIOD': '技术指标参数',
            'MACD_FAST': '技术指标参数',
            'MACD_SLOW': '技术指标参数',
            'MACD_SIGNAL': '技术指标参数',
            'STOP_LOSS': '风险管理字段',
            'TAKE_PROFIT': '风险管理字段',
            'MAX_POSITIONS': '交易限制字段',
            'AUTO_TRADE': '交易开关字段',
            'ENABLE_WECHAT_PUSH': '推送配置字段',
            'WECHAT_SENDKEY': '推送配置字段',
            'deepseek_api_key': 'AI配置字段',
            'daily_cost_limit': 'AI配置字段',
            'auto_refresh_news': '界面配置字段',
            'news_refresh_interval': '界面配置字段'
        }
        
        print("常见未知字段及其来源:")
        for field, description in common_unknown_fields.items():
            print(f"  • {field}: {description}")
        
        print("\n💡 这些字段来源于:")
        print("  1. 统一配置文件系统的扩展字段")
        print("  2. 之前版本的配置字段")
        print("  3. 用户自定义的配置扩展")
        print("  4. 第三方插件或模块的配置")
    
    def provide_solutions(self):
        """提供解决方案"""
        print("\n🔧 解决方案...")
        
        print("📋 配置警告解决方案:")
        print("  1. ✅ 这些警告是信息性的，不影响系统运行")
        print("  2. ✅ 系统会忽略未知字段，只使用已知字段")
        print("  3. ✅ 可以通过扩展CONFIG_SCHEMA来消除警告")
        
        print("\n🎯 推荐操作:")
        print("  方案1: 忽略警告（推荐）")
        print("    • 这些警告不影响系统功能")
        print("    • 系统会正常加载已知字段")
        print("    • 未知字段会被安全忽略")
        
        print("\n  方案2: 清理未知字段")
        print("    • 从配置文件中删除未知字段")
        print("    • 保留核心配置字段")
        print("    • 使用标准配置模板")
        
        print("\n  方案3: 扩展配置模式")
        print("    • 在CONFIG_SCHEMA中添加新字段定义")
        print("    • 支持更多配置选项")
        print("    • 保持向后兼容性")
        
        print("\n📁 配置文件加载优先级:")
        print("  1. 用户目录: ~/.wmzc_trading/")
        print("  2. 当前目录: ./")
        print("  3. 默认配置: 内置默认值")
        
        print("\n💡 为什么从用户目录加载:")
        print("  • 用户配置持久化")
        print("  • 多用户环境支持")
        print("  • 程序升级时配置保留")
        print("  • 权限安全考虑")

def create_config_cleaner():
    """创建配置清理工具"""
    cleaner_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 WMZC配置文件清理工具
清理未知字段，消除配置警告
"""

import os
import json
import shutil
from datetime import datetime

def clean_unknown_fields():
    """清理未知字段"""
    print("🧹 清理WMZC配置文件中的未知字段")
    print("=" * 50)
    
    # 已知字段列表（从CONFIG_SCHEMA提取）
    known_fields = {
        'API_KEY', 'API_SECRET', 'PASSPHRASE',
        'OKX_API_KEY', 'OKX_SECRET_KEY', 'OKX_PASSPHRASE',
        'GATE_API_KEY', 'GATE_SECRET_KEY',
        'SYMBOL', 'TIMEFRAME', 'EXCHANGE', 'CURRENT_EXCHANGE',
        'AMOUNT', 'ORDER_AMOUNT', 'ORDER_USDT_AMOUNT', 'LEVERAGE', 'RISK_PERCENT',
        'ENABLE_TRADING', 'AUTO_TRADING', 'TEST_MODE',
        'ENABLE_KDJ', 'ENABLE_MACD', 'ENABLE_PINBAR', 'ENABLE_STOP_LOSS',
        'ENABLE_TAKE_PROFIT', 'ENABLE_ADVANCED_MACD',
        'STOP_LOSS_PCT', 'TAKE_PROFIT_PCT', 'PROFIT_TARGET_PCT',
        'MAX_POSITION_SIZE', 'RISK_PER_TRADE', 'MAX_DAILY_TRADES',
        'KDJ', 'MACD', 'RSI', 'PINBAR', 'TECHNICAL_INDICATORS',
        'THEME', 'window_geometry', 'current_tab', 'font_size', 'update_interval',
        'LOG_LEVEL', 'AUTO_SAVE', 'SANDBOX', 'DEFAULT_SYMBOL', 'DEFAULT_TIMEFRAME',
        'INDICATOR_SYNC',
        'exchange_selection', 'okx_api_key', 'okx_secret_key', 'okx_passphrase',
        'default_symbol', 'default_timeframe'
    }
    
    config_dirs = [
        os.getcwd(),
        os.path.expanduser("~/.wmzc_trading")
    ]
    
    config_files = [
        'trading_config.json',
        'wmzc_config.json',
        'user_settings.json',
        'misc_optimization_config.json',
        'ai_config.json'
    ]
    
    cleaned_count = 0
    
    for config_dir in config_dirs:
        if not os.path.exists(config_dir):
            continue
            
        print(f"\\n📁 清理目录: {config_dir}")
        
        for config_file in config_files:
            file_path = os.path.join(config_dir, config_file)
            
            if not os.path.exists(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                if not content or content == '{}':
                    continue
                
                config = json.loads(content)
                
                # 找出未知字段
                unknown_fields = []
                for key in config.keys():
                    if not key.startswith('_') and key not in known_fields:
                        unknown_fields.append(key)
                
                if unknown_fields:
                    # 备份原文件
                    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.copy2(file_path, backup_path)
                    
                    # 创建清理后的配置
                    cleaned_config = {}
                    for key, value in config.items():
                        if key.startswith('_') or key in known_fields:
                            cleaned_config[key] = value
                    
                    # 保存清理后的配置
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(cleaned_config, f, indent=2, ensure_ascii=False)
                    
                    print(f"  ✅ {config_file}: 清理了 {len(unknown_fields)} 个未知字段")
                    print(f"    已备份: {os.path.basename(backup_path)}")
                    cleaned_count += 1
                else:
                    print(f"  ✅ {config_file}: 无需清理")
                    
            except Exception as e:
                print(f"  ❌ {config_file}: 清理失败 - {e}")
    
    print(f"\\n🎉 清理完成！共清理了 {cleaned_count} 个配置文件")
    print("💡 重新启动WMZC系统，配置警告应该消失")

if __name__ == "__main__":
    clean_unknown_fields()
'''
    
    with open('clean_config_warnings.py', 'w', encoding='utf-8') as f:
        f.write(cleaner_code)
    
    print("✅ 配置清理工具已创建: clean_config_warnings.py")

def main():
    """主函数"""
    diagnoser = ConfigWarningDiagnoser()
    diagnoser.run_diagnosis()
    
    print("\n" + "=" * 60)
    print("🛠️ 创建配置清理工具...")
    create_config_cleaner()
    
    print("\n🎯 总结:")
    print("  1. 配置警告是信息性的，不影响系统运行")
    print("  2. 系统从用户目录加载配置是正常行为")
    print("  3. 未知字段会被安全忽略")
    print("  4. 可以使用清理工具消除警告")
    
    print("\n🚀 建议操作:")
    print("  • 如果不介意警告：无需操作，系统正常运行")
    print("  • 如果要消除警告：运行 python clean_config_warnings.py")
    print("  • 如果要统一配置：继续使用 wmzc_unified_config.json")

if __name__ == "__main__":
    main()
