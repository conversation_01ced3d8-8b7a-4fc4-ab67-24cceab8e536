#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC配置持久化失效问题诊断和修复脚本
解决配置保存后在系统重启时被重置的问题
"""

import os
import json
import shutil
from datetime import datetime

class ConfigPersistenceFailureDiagnoser:
    """配置持久化失效问题诊断器"""
    
    def __init__(self):
        self.config_files = {
            'trading_config.json': 'trading_config.json',
            'wmzc_config.json': 'wmzc_config.json'
        }
        self.backup_dir = 'config_diagnosis_backups'
        self.issues_found = []
        
    def diagnose_persistence_failure(self):
        """诊断配置持久化失效问题"""
        print("🔍 开始诊断配置持久化失效问题...")
        print("=" * 60)
        
        # 1. 检查配置文件当前状态
        self.check_current_config_state()
        
        # 2. 模拟保存和重启过程
        self.simulate_save_restart_cycle()
        
        # 3. 检查配置覆盖问题
        self.check_config_override_issues()
        
        # 4. 分析根本原因
        self.analyze_root_causes()
        
        # 5. 提供修复方案
        self.provide_fix_solutions()
        
        return self.issues_found
    
    def check_current_config_state(self):
        """检查配置文件当前状态"""
        print("📋 检查配置文件当前状态...")
        
        for name, path in self.config_files.items():
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    print(f"\n📄 {name}:")
                    
                    # 检查API配置
                    if name == 'trading_config.json':
                        api_key = config.get('API_KEY', '')
                        api_secret = config.get('API_SECRET', '')
                        passphrase = config.get('PASSPHRASE', '')
                    else:
                        api_key = config.get('okx_api_key', '')
                        api_secret = config.get('okx_secret_key', '')
                        passphrase = config.get('okx_passphrase', '')
                    
                    if api_key and api_secret and passphrase:
                        print(f"  ✅ API配置存在: API_KEY={api_key[:10]}...")
                    else:
                        print(f"  ❌ API配置缺失或为空")
                        self.issues_found.append(f"{name} API配置缺失")
                    
                    # 检查其他重要配置
                    important_fields = ['SYMBOL', 'TIMEFRAME', 'ORDER_USDT_AMOUNT', 'LEVERAGE']
                    missing_fields = []
                    
                    for field in important_fields:
                        if field not in config or not config[field]:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"  ⚠️ 缺失字段: {missing_fields}")
                        self.issues_found.append(f"{name} 缺失字段: {missing_fields}")
                    else:
                        print(f"  ✅ 重要字段完整")
                    
                    print(f"  📊 总配置项: {len(config)}")
                    
                except Exception as e:
                    print(f"  ❌ 读取失败: {e}")
                    self.issues_found.append(f"{name} 读取失败: {e}")
            else:
                print(f"\n📄 {name}: ❌ 文件不存在")
                self.issues_found.append(f"{name} 文件不存在")
    
    def simulate_save_restart_cycle(self):
        """模拟保存和重启过程"""
        print("\n🔄 模拟保存和重启过程...")
        
        # 创建备份
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 备份当前配置
        for name, path in self.config_files.items():
            if os.path.exists(path):
                backup_path = os.path.join(self.backup_dir, f"{name}.before_test_{timestamp}")
                shutil.copy2(path, backup_path)
                print(f"  📋 已备份 {name} -> {backup_path}")
        
        # 模拟用户保存配置
        test_config = {
            "API_KEY": "test_api_key_12345",
            "API_SECRET": "test_api_secret_67890",
            "PASSPHRASE": "test_passphrase_abc",
            "SYMBOL": "BTC-USDT-SWAP",
            "TIMEFRAME": "1m",
            "ORDER_USDT_AMOUNT": 15,
            "LEVERAGE": 5,
            "TEST_MODE": True
        }
        
        print("  💾 模拟用户保存配置...")
        
        # 保存到trading_config.json
        try:
            existing_config = {}
            if os.path.exists('trading_config.json'):
                with open('trading_config.json', 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
            
            existing_config.update(test_config)
            
            with open('trading_config.json', 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, indent=2, ensure_ascii=False)
            
            print("    ✅ trading_config.json 保存成功")
            
        except Exception as e:
            print(f"    ❌ trading_config.json 保存失败: {e}")
            self.issues_found.append(f"保存失败: {e}")
        
        # 保存到wmzc_config.json
        try:
            existing_config = {}
            if os.path.exists('wmzc_config.json'):
                with open('wmzc_config.json', 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
            
            existing_config['okx_api_key'] = test_config['API_KEY']
            existing_config['okx_secret_key'] = test_config['API_SECRET']
            existing_config['okx_passphrase'] = test_config['PASSPHRASE']
            
            with open('wmzc_config.json', 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, indent=2, ensure_ascii=False)
            
            print("    ✅ wmzc_config.json 保存成功")
            
        except Exception as e:
            print(f"    ❌ wmzc_config.json 保存失败: {e}")
            self.issues_found.append(f"保存失败: {e}")
        
        # 验证保存结果
        print("  🔍 验证保存结果...")
        
        for name, path in self.config_files.items():
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                
                if name == 'trading_config.json':
                    saved_api_key = saved_config.get('API_KEY', '')
                else:
                    saved_api_key = saved_config.get('okx_api_key', '')
                
                if saved_api_key == test_config['API_KEY']:
                    print(f"    ✅ {name}: 配置保存验证成功")
                else:
                    print(f"    ❌ {name}: 配置保存验证失败")
                    self.issues_found.append(f"{name} 保存验证失败")
                    
            except Exception as e:
                print(f"    ❌ {name}: 验证失败 {e}")
                self.issues_found.append(f"{name} 验证失败: {e}")
    
    def check_config_override_issues(self):
        """检查配置覆盖问题"""
        print("\n🔧 检查配置覆盖问题...")
        
        # 检查是否存在配置覆盖脚本
        override_scripts = [
            'wmzc_config_warning_silencer.py',
            'wmzc_final_config_optimizer.py',
            'wmzc_config_fixer.py'
        ]
        
        for script in override_scripts:
            if os.path.exists(script):
                print(f"  ⚠️ 发现配置覆盖脚本: {script}")
                self.issues_found.append(f"存在配置覆盖脚本: {script}")
                
                # 检查脚本内容
                try:
                    with open(script, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if 'API_KEY' in content and 'da636867-490f-4e3e-81b2-870841afb860' in content:
                        print(f"    ❌ {script} 包含硬编码的API密钥，会覆盖用户配置！")
                        self.issues_found.append(f"{script} 包含硬编码API密钥")
                    
                    if 'json.dump' in content:
                        print(f"    ⚠️ {script} 会写入配置文件")
                        
                except Exception as e:
                    print(f"    ❌ 无法检查 {script}: {e}")
            else:
                print(f"  ✅ 未发现配置覆盖脚本: {script}")
    
    def analyze_root_causes(self):
        """分析根本原因"""
        print("\n🎯 分析根本原因...")
        
        root_causes = []
        
        # 原因1: 配置覆盖脚本
        if any('配置覆盖脚本' in issue for issue in self.issues_found):
            root_causes.append("配置覆盖脚本在系统启动时重写配置文件")
        
        # 原因2: 默认配置覆盖
        if any('API配置缺失' in issue for issue in self.issues_found):
            root_causes.append("_ensure_config_defaults函数用默认值覆盖用户配置")
        
        # 原因3: 配置加载顺序问题
        root_causes.append("配置加载顺序问题：默认配置覆盖了文件配置")
        
        # 原因4: 配置管理器冲突
        root_causes.append("多个配置管理器冲突，导致配置被重复初始化")
        
        for i, cause in enumerate(root_causes, 1):
            print(f"  {i}. {cause}")
        
        return root_causes
    
    def provide_fix_solutions(self):
        """提供修复方案"""
        print("\n🔧 修复方案...")
        
        solutions = [
            "删除或禁用配置覆盖脚本",
            "修复_ensure_config_defaults函数，保留用户配置",
            "调整配置加载顺序，优先使用文件配置",
            "统一配置管理器，避免重复初始化",
            "添加配置保护机制，防止意外覆盖"
        ]
        
        for i, solution in enumerate(solutions, 1):
            print(f"  {i}. {solution}")
        
        return solutions
    
    def restore_backups(self):
        """恢复备份"""
        print("\n🔄 恢复配置备份...")
        
        if os.path.exists(self.backup_dir):
            backup_files = [f for f in os.listdir(self.backup_dir) if f.endswith('.before_test')]
            
            for backup_file in backup_files:
                original_name = backup_file.split('.before_test')[0]
                backup_path = os.path.join(self.backup_dir, backup_file)
                
                try:
                    shutil.copy2(backup_path, original_name)
                    print(f"  ✅ 已恢复 {original_name}")
                except Exception as e:
                    print(f"  ❌ 恢复 {original_name} 失败: {e}")
        else:
            print("  ⚠️ 未找到备份目录")

def main():
    """主函数"""
    print("🔍 WMZC配置持久化失效问题诊断器")
    print("=" * 60)
    
    diagnoser = ConfigPersistenceFailureDiagnoser()
    
    try:
        # 诊断问题
        issues = diagnoser.diagnose_persistence_failure()
        
        print("\n" + "=" * 60)
        print("📊 诊断结果总结:")
        
        if issues:
            print(f"❌ 发现 {len(issues)} 个问题:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
            
            print("\n💡 主要问题:")
            print("  1. 系统启动时配置被默认值覆盖")
            print("  2. 存在配置覆盖脚本重写配置文件")
            print("  3. _ensure_config_defaults函数覆盖用户配置")
            print("  4. 配置加载顺序不正确")
            
            print("\n🔧 建议修复步骤:")
            print("  1. 运行配置修复脚本")
            print("  2. 删除配置覆盖脚本")
            print("  3. 修复WMZC.py中的配置加载逻辑")
            print("  4. 重新保存API配置")
            
        else:
            print("✅ 未发现明显的配置持久化问题")
            print("💡 如果问题仍然存在，可能是其他原因导致")
        
        # 恢复备份
        diagnoser.restore_backups()
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ 诊断过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
