# WMZC配置相关代码备份
# 备份时间: 20250730_164047

def _ensure_config_defaults(config):
    """🔧 修复P0-9：确保配置字典包含所有必要的默认值 - 保护用户配置"""
    default_config = {
        'ORDER_USDT_AMOUNT': 10.0,
        'LEVERAGE': 3,
        'RISK_PERCENT': 1.0,
        'SYMBOL': 'BTC-USDT-SWAP',
        'TIMEFRAME': '1m',
        'API_KEY': '',
        'API_SECRET': '',
        'PASSPHRASE': '',
        'ENABLE_TRADING': False,
        'TEST_MODE': True,
        'MAX_POSITIONS': 5,
        'STOP_LOSS_PERCENT': 2.0,
        'TAKE_PROFIT_PERCENT': 3.0,
        'ENABLE_MACD': True,
        'ENABLE_KDJ': True,
        'ENABLE_RSI': True,
        'ENABLE_PINBAR': False,
        'LOG_LEVEL': 'INFO'
    }

    # 🔧 修复：只添加缺失的配置项，保护用户已有配置
    for key, default_value in default_config.items():
        if key not in config:
            config[key] = default_value
            log(f"🔧 添加默认配置: {key} = {default_value}", "DEBUG")
        elif key in ['API_KEY', 'API_SECRET', 'PASSPHRASE'] and config[key]:
            # 🔧 修复：保护用户的API配置，不被默认值覆盖
            log(f"🔒 保护用户配置: {key} = {config[key][:10]}...", "DEBUG")

    return config


def load_config_from_file():
    """🔧 修复P0-10：从配置文件加载配置，优先保护用户配置"""
    try:
        config_file = "trading_config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
            print(f"✅ 配置已从 {config_file} 加载")

            # 🔧 修复：检查是否有用户API配置
            has_user_api = (loaded_config.get('API_KEY') and
                           loaded_config.get('API_SECRET') and
                           loaded_config.get('PASSPHRASE'))

            if has_user_api:
                print(f"🔒 检测到用户API配置，将优先保护")

            # 🔧 修复：确保配置包含所有默认值，但保护用户配置
            loaded_config = _ensure_config_defaults(loaded_config)
            return loaded_config
        else:
            print(f"⚠️ 配置文件 {config_file} 不存在，创建默认配置")
            default_config = get_default_config()
            # 保存默认配置到文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            return _ensure_config_defaults(default_config)
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}，使用默认配置")
        default_config = get_default_config()
        return _ensure_config_defaults(default_config)


def _auto_init_config():
    """自动初始化配置系统 - 🔧 修复：不覆盖已加载的用户配置"""
    global config  # 在函数开始就声明global
    try:
        # 🔧 修复：检查是否已有用户配置，如果有则保护
        has_user_config = (isinstance(config, dict) and
                          config.get('API_KEY') and
                          config.get('API_SECRET') and
                          config.get('PASSPHRASE'))

        if has_user_config:
            print("🔒 检测到用户配置，跳过自动初始化以保护用户配置")
            log("🔒 保护用户配置，跳过自动初始化", "INFO")
            return True

        # 触发配置管理器的自动初始化
        cm = get_config_manager()

        # 确保全局config变量可用，但不覆盖用户配置
        if hasattr(cm, 'config') and isinstance(cm.config, dict):
            # 🔧 修复：合并配置而不是覆盖
            if isinstance(config, dict):
                # 保护现有的用户配置
                user_api_config = {}
                for key in ['API_KEY', 'API_SECRET', 'PASSPHRASE']:
                    if config.get(key):
                        user_api_config[key] = config[key]

                # 使用配置管理器的配置
                config = cm.config

                # 恢复用户API配置
                config.update(user_api_config)
            else:
                config = cm.config
        else:
            # 只有在没有任何配置时才使用默认配置
            if not isinstance(config, dict) or not config:
                config = get_default_config()

        log("✅ 配置系统自动初始化成功", "DEBUG")
        return True
    except Exception as e:
        log(f"❌ 配置系统自动初始化失败: {e}", "ERROR")
        # 🔧 修复：只有在config为空时才使用默认配置
        if not isinstance(config, dict) or not config:
            config = get_default_config()
        return False
