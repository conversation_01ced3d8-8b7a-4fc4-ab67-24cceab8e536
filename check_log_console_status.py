#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC日志控制台状态检查工具
快速检查日志控制台的状态和配置
"""

import os
import json
import re

def check_log_console_status():
    """检查日志控制台状态"""
    print("🔍 WMZC日志控制台状态检查")
    print("=" * 50)
    
    # 1. 检查WMZC.py中的日志控制台实现
    print("\n📜 检查日志控制台实现...")
    
    if os.path.exists('WMZC.py'):
        with open('WMZC.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键组件
        components = {
            '日志控制台标签页': '"📜 日志控制台"',
            '日志文本框': 'self.log_text',
            '日志添加方法': 'def add_log_message',
            '日志清空方法': 'def clear_log',
            '日志保存方法': 'def save_log',
            'GUI回调设置': 'set_gui_callback',
            '统一日志接口': 'unified_log_interface'
        }
        
        for name, pattern in components.items():
            if pattern in content:
                print(f"  ✅ {name}: 已实现")
            else:
                print(f"  ❌ {name}: 未找到")
        
        # 检查日志初始化
        if 'def setup_log_tab(self):' in content:
            print("  ✅ 日志控制台初始化函数: 已实现")
            
            # 提取setup_log_tab函数内容
            pattern = r'def setup_log_tab\(self\):(.*?)(?=def |\Z)'
            match = re.search(pattern, content, re.DOTALL)
            if match:
                setup_content = match.group(1)
                
                # 检查关键初始化步骤
                init_steps = {
                    '创建日志文本框': 'self.log_text = tk.Text',
                    '设置GUI回调': 'set_gui_callback(self.add_log_message)',
                    '添加初始日志': 'add_log_message.*日志控制台已初始化',
                    '测试系统日志': 'log.*系统日志连接测试'
                }
                
                print("    日志控制台初始化步骤:")
                for step_name, step_pattern in init_steps.items():
                    if re.search(step_pattern, setup_content):
                        print(f"      ✅ {step_name}")
                    else:
                        print(f"      ❌ {step_name}")
        else:
            print("  ❌ 日志控制台初始化函数: 未找到")
    else:
        print("  ❌ WMZC.py文件不存在")
    
    # 2. 检查配置文件
    print("\n⚙️ 检查日志配置...")
    
    config_files = ['trading_config.json', 'wmzc_config.json']
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                print(f"  📄 {config_file}:")
                
                # 检查日志相关配置
                log_level = config.get('LOG_LEVEL', '未设置')
                enable_logging = config.get('ENABLE_LOGGING', '未设置')
                log_to_console = config.get('LOG_TO_CONSOLE', '未设置')
                
                print(f"    LOG_LEVEL: {log_level}")
                print(f"    ENABLE_LOGGING: {enable_logging}")
                print(f"    LOG_TO_CONSOLE: {log_to_console}")
                
                # 评估配置
                if log_level == 'INFO':
                    print("    ✅ 日志级别设置合适")
                elif log_level in ['ERROR', 'CRITICAL']:
                    print("    ⚠️ 日志级别过高，可能过滤重要信息")
                elif log_level == '未设置':
                    print("    ⚠️ 日志级别未设置")
                
                if enable_logging in [True, '未设置']:
                    print("    ✅ 日志功能已启用")
                else:
                    print("    ❌ 日志功能被禁用")
                
                if log_to_console in [True, '未设置']:
                    print("    ✅ 控制台日志已启用")
                else:
                    print("    ❌ 控制台日志被禁用")
                
            except Exception as e:
                print(f"  ❌ 读取 {config_file} 失败: {e}")
        else:
            print(f"  ❌ {config_file}: 文件不存在")
    
    # 3. 检查日志文件
    print("\n📁 检查日志文件...")
    
    log_files = []
    for file in os.listdir('.'):
        if file.endswith('.log') or 'log' in file.lower():
            log_files.append(file)
    
    if log_files:
        print(f"  📄 发现日志文件: {log_files}")
        print("  💡 日志可能被重定向到文件")
    else:
        print("  ✅ 未发现日志文件，日志应该输出到控制台")
    
    # 4. 生成诊断建议
    print("\n💡 诊断建议:")
    
    suggestions = []
    
    # 基于检查结果生成建议
    if os.path.exists('WMZC.py'):
        suggestions.append("✅ WMZC.py存在，日志控制台应该可以正常工作")
    else:
        suggestions.append("❌ WMZC.py缺失，需要恢复文件")
    
    # 检查配置
    config_ok = True
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                if config.get('LOG_LEVEL') in ['ERROR', 'CRITICAL']:
                    suggestions.append(f"⚠️ {config_file}中日志级别过高，建议设置为INFO")
                    config_ok = False
                
                if config.get('ENABLE_LOGGING') == False:
                    suggestions.append(f"❌ {config_file}中日志功能被禁用，建议启用")
                    config_ok = False
                    
            except:
                pass
    
    if config_ok:
        suggestions.append("✅ 日志配置正常")
    
    # 输出建议
    for suggestion in suggestions:
        print(f"  {suggestion}")
    
    # 5. 提供解决方案
    print("\n🔧 解决方案:")
    print("  1. 重新启动WMZC系统")
    print("  2. 检查'📜 日志控制台'标签页是否存在")
    print("  3. 在系统运行时观察是否有日志输出")
    print("  4. 如果仍无输出，运行: python test_log_console.py")
    print("  5. 检查系统启动日志中是否有错误信息")
    
    return True

def main():
    """主函数"""
    try:
        check_log_console_status()
        print("\n✅ 日志控制台状态检查完成")
    except Exception as e:
        print(f"\n❌ 检查过程中发生异常: {e}")

if __name__ == "__main__":
    main()
