#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 清理测试API配置脚本
清理所有配置文件中的测试API密钥，解决配置警告问题
"""

import os
import json
import shutil
from datetime import datetime

class TestAPIConfigCleaner:
    """测试API配置清理器"""
    
    def __init__(self):
        self.config_directories = [
            '.',  # 当前目录
            'C:\\Users\\<USER>\\.wmzc_trading'  # WMZC配置目录
        ]
        self.test_api_patterns = [
            'da636867-490f-4e3e-81b2-870841afb860',  # 测试API_KEY
            'C15B6EE0CF3FFDEE5834865D3839325E',       # 测试API_SECRET
            'Mx123456@',                              # 测试PASSPHRASE
            'test_final_api_key',                     # 其他测试模式
            'test_api_key',
            'test_secret',
            'test_passphrase'
        ]
        self.cleaned_files = []
        
    def run_cleanup(self):
        """运行清理"""
        print("🧹 清理测试API配置")
        print("=" * 60)
        
        # 1. 扫描所有配置目录
        self.scan_and_clean_configs()
        
        # 2. 生成清理报告
        self.generate_cleanup_report()
        
        # 3. 创建干净的配置模板
        self.create_clean_config_templates()
        
        return len(self.cleaned_files) > 0
    
    def scan_and_clean_configs(self):
        """扫描并清理配置文件"""
        print("\n🔍 扫描配置文件...")
        
        for config_dir in self.config_directories:
            if os.path.exists(config_dir):
                print(f"\n📁 扫描目录: {config_dir}")
                self.clean_directory(config_dir)
            else:
                print(f"\n📁 目录不存在: {config_dir}")
    
    def clean_directory(self, directory):
        """清理指定目录的配置文件"""
        config_files = []
        
        # 查找所有JSON配置文件
        for file in os.listdir(directory):
            if file.endswith('.json') and any(keyword in file.lower() for keyword in 
                ['config', 'setting', 'wmzc', 'trading', 'ai']):
                config_files.append(os.path.join(directory, file))
        
        for config_file in config_files:
            self.clean_config_file(config_file)
    
    def clean_config_file(self, config_file):
        """清理单个配置文件"""
        try:
            print(f"\n📄 检查文件: {os.path.basename(config_file)}")
            
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查是否包含测试API
            has_test_api = False
            cleaned_fields = []
            
            # 检查各种API字段
            api_fields = [
                'API_KEY', 'API_SECRET', 'PASSPHRASE',
                'OKX_API_KEY', 'OKX_SECRET_KEY', 'OKX_PASSPHRASE',
                'okx_api_key', 'okx_secret_key', 'okx_passphrase',
                'GATE_API_KEY', 'GATE_SECRET_KEY',
                'gate_api_key', 'gate_secret_key'
            ]
            
            for field in api_fields:
                if field in config:
                    field_value = str(config[field])
                    
                    # 检查是否包含测试值
                    for test_pattern in self.test_api_patterns:
                        if test_pattern in field_value:
                            print(f"  ⚠️ 发现测试API: {field} = {field_value[:20]}...")
                            
                            # 备份原始文件
                            if not has_test_api:
                                backup_file = f"{config_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                                shutil.copy2(config_file, backup_file)
                                print(f"  💾 已备份: {os.path.basename(backup_file)}")
                            
                            # 清空测试值
                            config[field] = ""
                            cleaned_fields.append(field)
                            has_test_api = True
                            break
            
            if has_test_api:
                # 添加清理标记
                config['_TEST_API_CLEANED'] = datetime.now().isoformat()
                config['_CLEANED_FIELDS'] = cleaned_fields
                
                # 保存清理后的配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                print(f"  ✅ 已清理 {len(cleaned_fields)} 个测试API字段")
                self.cleaned_files.append({
                    'file': config_file,
                    'cleaned_fields': cleaned_fields
                })
            else:
                print(f"  ✅ 未发现测试API配置")
                
        except Exception as e:
            print(f"  ❌ 清理失败: {e}")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        print(f"\n📊 清理报告...")
        
        if self.cleaned_files:
            print(f"✅ 成功清理了 {len(self.cleaned_files)} 个配置文件:")
            
            for item in self.cleaned_files:
                file_name = os.path.basename(item['file'])
                fields = ', '.join(item['cleaned_fields'])
                print(f"  📄 {file_name}: {fields}")
            
            print(f"\n💡 清理内容:")
            print(f"  • 移除了所有测试API密钥")
            print(f"  • 创建了备份文件")
            print(f"  • 添加了清理标记")
            
        else:
            print(f"ℹ️ 未发现需要清理的测试API配置")
    
    def create_clean_config_templates(self):
        """创建干净的配置模板"""
        print(f"\n🔧 创建干净的配置模板...")
        
        # 主配置模板
        clean_trading_config = {
            # API配置 - 用户需要填写真实值
            "API_KEY": "",
            "API_SECRET": "",
            "PASSPHRASE": "",
            "OKX_API_KEY": "",
            "OKX_SECRET_KEY": "",
            "OKX_PASSPHRASE": "",
            
            # 基本交易配置
            "EXCHANGE": "OKX",
            "SYMBOL": "BTC-USDT-SWAP",
            "TIMEFRAME": "1m",
            "ORDER_USDT_AMOUNT": 10,
            "LEVERAGE": 3,
            "RISK_PERCENT": 1.0,
            
            # 策略配置
            "ENABLE_KDJ": True,
            "ENABLE_MACD": True,
            "ENABLE_PINBAR": True,
            "ENABLE_RSI": True,
            
            # 系统配置
            "LOG_LEVEL": "INFO",
            "TEST_MODE": True,
            "ENABLE_TRADING": False,
            
            # 清理标记
            "_CONFIG_CLEANED": datetime.now().isoformat(),
            "_CONFIG_PROTECTED": True,
            "_READY_FOR_REAL_API": True
        }
        
        # WMZC配置模板
        clean_wmzc_config = {
            "exchange_selection": "OKX",
            "okx_api_key": "",
            "okx_secret_key": "",
            "okx_passphrase": "",
            "default_symbol": "BTC-USDT-SWAP",
            "default_timeframe": "1m",
            
            # 清理标记
            "_CONFIG_CLEANED": datetime.now().isoformat(),
            "_CONFIG_PROTECTED": True,
            "_READY_FOR_REAL_API": True
        }
        
        # 保存干净的配置模板
        try:
            with open('trading_config.json', 'w', encoding='utf-8') as f:
                json.dump(clean_trading_config, f, indent=2, ensure_ascii=False)
            print("  ✅ 干净的 trading_config.json 已创建")
        except Exception as e:
            print(f"  ❌ 创建 trading_config.json 失败: {e}")
        
        try:
            with open('wmzc_config.json', 'w', encoding='utf-8') as f:
                json.dump(clean_wmzc_config, f, indent=2, ensure_ascii=False)
            print("  ✅ 干净的 wmzc_config.json 已创建")
        except Exception as e:
            print(f"  ❌ 创建 wmzc_config.json 失败: {e}")
        
        # 清理WMZC配置目录中的文件
        wmzc_config_dir = 'C:\\Users\\<USER>\\.wmzc_trading'
        if os.path.exists(wmzc_config_dir):
            print(f"\n🧹 清理WMZC配置目录...")
            
            wmzc_files = {
                'wmzc_config.json': clean_wmzc_config,
                'trading_config.json': clean_trading_config,
                'user_settings.json': clean_trading_config,
                'misc_optimization_config.json': clean_trading_config,
                'ai_config.json': clean_trading_config
            }
            
            for filename, config_template in wmzc_files.items():
                file_path = os.path.join(wmzc_config_dir, filename)
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(config_template, f, indent=2, ensure_ascii=False)
                    print(f"  ✅ 已清理: {filename}")
                except Exception as e:
                    print(f"  ❌ 清理 {filename} 失败: {e}")

def main():
    """主函数"""
    print("🧹 WMZC测试API配置清理工具")
    print("=" * 60)
    
    cleaner = TestAPIConfigCleaner()
    
    try:
        success = cleaner.run_cleanup()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 测试API配置清理完成！")
            print("\n💡 清理结果:")
            print("  1. ✅ 移除了所有测试API密钥")
            print("  2. ✅ 创建了配置文件备份")
            print("  3. ✅ 生成了干净的配置模板")
            print("  4. ✅ 清理了WMZC配置目录")
            
            print("\n🚀 下一步操作:")
            print("  1. 重新启动WMZC系统")
            print("  2. 在主配置页面填写您的真实API密钥")
            print("  3. 保存配置")
            print("  4. 系统将不再显示配置警告")
            
            print("\n⚠️ 重要提醒:")
            print("  • 请使用您的真实OKX API密钥")
            print("  • 确保API密钥有足够的权限")
            print("  • 确认IP地址在OKX白名单中")
            
        else:
            print("ℹ️ 未发现需要清理的测试API配置")
            print("💡 配置文件可能已经是干净的")
        
        return success
        
    except Exception as e:
        print(f"❌ 清理过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
