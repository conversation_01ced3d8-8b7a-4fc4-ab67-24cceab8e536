#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC交易系统依赖安装脚本
自动安装所需的Python包
"""

import subprocess
import sys
import os

def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        # 尝试导入包
        __import__(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"📦 正在安装 {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {package_name} 安装失败: {e}")
            return False

def main():
    """主安装函数"""
    print("=" * 60)
    print("🚀 WMZC交易系统依赖安装程序")
    print("=" * 60)
    
    # 核心依赖包列表
    core_packages = [
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("matplotlib", "matplotlib"),
        ("requests", "requests"),
        ("websocket-client", "websocket"),
        ("python-dateutil", "dateutil"),
        ("pytz", "pytz")
    ]
    
    # 可选依赖包列表
    optional_packages = [
        ("TA-Lib", "talib"),
        ("scikit-learn", "sklearn"),
        ("torch", "torch"),
        ("ttkbootstrap", "ttkbootstrap")
    ]
    
    print("\n📦 安装核心依赖包...")
    core_success = 0
    for package, import_name in core_packages:
        if install_package(package, import_name):
            core_success += 1
    
    print(f"\n📊 核心依赖安装结果: {core_success}/{len(core_packages)} 成功")
    
    print("\n📦 安装可选依赖包...")
    optional_success = 0
    for package, import_name in optional_packages:
        if install_package(package, import_name):
            optional_success += 1
    
    print(f"\n📊 可选依赖安装结果: {optional_success}/{len(optional_packages)} 成功")
    
    # 特殊处理TA-Lib
    if optional_success < len(optional_packages):
        print("\n⚠️ 部分可选依赖安装失败，这不会影响系统核心功能")
        print("💡 TA-Lib安装提示:")
        print("   Windows: 下载预编译包 https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib")
        print("   Linux: sudo apt-get install ta-lib")
        print("   macOS: brew install ta-lib")
    
    print("\n" + "=" * 60)
    if core_success == len(core_packages):
        print("🎉 依赖安装完成！系统可以正常运行")
    else:
        print("⚠️ 部分核心依赖安装失败，系统将使用模拟对象")
    print("=" * 60)
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
