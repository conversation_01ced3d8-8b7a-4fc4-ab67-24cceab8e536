#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Bug修复验证脚本
测试已修复的P0级别Bug
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dataframe_ambiguity_fix():
    """测试DataFrame歧义性错误修复"""
    print("🧪 测试DataFrame歧义性错误修复...")
    
    try:
        # 导入WMZC模块
        import WMZC
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110],
            'high': [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111],
            'low': [99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109]
        })
        
        # 测试MACD计算
        print("   测试MACD计算...")
        macd_result = WMZC.calculate_macd(test_data)
        if isinstance(macd_result, pd.DataFrame) and not macd_result.empty:
            print("   ✅ MACD计算成功")
        else:
            print("   ❌ MACD计算失败")
            
        # 测试RSI计算
        print("   测试RSI计算...")
        rsi_result = WMZC.calculate_rsi(test_data)
        if isinstance(rsi_result, pd.DataFrame) and not rsi_result.empty:
            print("   ✅ RSI计算成功")
        else:
            print("   ❌ RSI计算失败")
            
        # 测试KDJ计算
        print("   测试KDJ计算...")
        kdj_result = WMZC.calculate_kdj(test_data)
        if isinstance(kdj_result, pd.DataFrame) and not kdj_result.empty:
            print("   ✅ KDJ计算成功")
        else:
            print("   ❌ KDJ计算失败")
            
        return True
        
    except Exception as e:
        print(f"   ❌ DataFrame歧义性测试失败: {e}")
        return False

def test_list_input_handling():
    """测试list输入处理修复"""
    print("🧪 测试list输入处理修复...")
    
    try:
        import WMZC
        
        # 测试list输入到MACD
        print("   测试list输入到MACD...")
        test_list = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110]
        macd_result = WMZC.calculate_macd(test_list)
        if isinstance(macd_result, pd.DataFrame):
            print("   ✅ list输入MACD处理成功")
        else:
            print("   ❌ list输入MACD处理失败")
            
        # 测试list输入到RSI
        print("   测试list输入到RSI...")
        rsi_result = WMZC.calculate_rsi(test_list)
        if isinstance(rsi_result, pd.DataFrame):
            print("   ✅ list输入RSI处理成功")
        else:
            print("   ❌ list输入RSI处理失败")
            
        return True
        
    except Exception as e:
        print(f"   ❌ list输入处理测试失败: {e}")
        return False

def test_safe_dataframe_check():
    """测试安全DataFrame检查函数"""
    print("🧪 测试安全DataFrame检查函数...")
    
    try:
        import WMZC
        
        # 测试空DataFrame
        empty_df = pd.DataFrame()
        result = WMZC._safe_dataframe_check(empty_df)
        if not result:
            print("   ✅ 空DataFrame检查正确")
        else:
            print("   ❌ 空DataFrame检查错误")
            
        # 测试有效DataFrame
        valid_df = pd.DataFrame({'close': [100, 101, 102]})
        result = WMZC._safe_dataframe_check(valid_df)
        if result:
            print("   ✅ 有效DataFrame检查正确")
        else:
            print("   ❌ 有效DataFrame检查错误")
            
        # 测试None输入
        result = WMZC._safe_dataframe_check(None)
        if not result:
            print("   ✅ None输入检查正确")
        else:
            print("   ❌ None输入检查错误")
            
        return True
        
    except Exception as e:
        print(f"   ❌ 安全DataFrame检查测试失败: {e}")
        return False

def test_config_defaults():
    """测试配置默认值修复"""
    print("🧪 测试配置默认值修复...")
    
    try:
        import WMZC
        
        # 测试配置默认值函数
        if hasattr(WMZC, '_ensure_config_defaults'):
            test_config = {}
            WMZC._ensure_config_defaults(test_config)
            
            # 检查关键配置项
            required_keys = ['ORDER_USDT_AMOUNT', 'LEVERAGE', 'RISK_PERCENT']
            missing_keys = [key for key in required_keys if key not in test_config]
            
            if not missing_keys:
                print("   ✅ 配置默认值设置成功")
                return True
            else:
                print(f"   ❌ 配置默认值缺失: {missing_keys}")
                return False
        else:
            print("   ⚠️ _ensure_config_defaults函数不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ 配置默认值测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🔧 WMZC Bug修复验证测试")
    print("=" * 60)
    
    tests = [
        ("DataFrame歧义性错误修复", test_dataframe_ambiguity_fix),
        ("list输入处理修复", test_list_input_handling),
        ("安全DataFrame检查", test_safe_dataframe_check),
        ("配置默认值修复", test_config_defaults)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有Bug修复验证通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
