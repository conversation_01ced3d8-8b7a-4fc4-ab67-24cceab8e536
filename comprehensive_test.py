#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统 - 全面代码质量验证测试
"""

def comprehensive_quality_test():
    """全面的代码质量验证测试"""
    print("🔍 开始WMZC量化交易系统全面代码质量验证...")
    
    test_results = {
        'syntax_check': False,
        'import_check': False,
        'tkinter_fix': False,
        'kdj_fix': False,
        'exception_handling': False,
        'duplicate_removal': False
    }
    
    try:
        # 测试1：语法检查
        print("📋 测试1：Python语法检查...")
        import ast
        with open('WMZC.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        try:
            ast.parse(code)
            print("✅ Python语法检查通过")
            test_results['syntax_check'] = True
        except SyntaxError as e:
            print(f"❌ 语法错误: {e}")
            return test_results
        
        # 测试2：导入检查
        print("📋 测试2：导入语句检查...")
        try:
            import tkinter as tk
            from tkinter import messagebox, ttk, simpledialog
            print("✅ tkinter导入检查通过")
            test_results['import_check'] = True
        except ImportError as e:
            print(f"❌ 导入错误: {e}")
        
        # 测试3：tkinter修复验证
        print("📋 测试3：tkinter修复验证...")
        try:
            # 检查tkinter导入是否存在
            import_lines = []
            for line_num, line in enumerate(code.split('\n'), 1):
                if 'import tkinter as tk' in line:
                    import_lines.append(line_num)
            
            if import_lines:
                print(f"✅ tkinter导入修复验证通过 (第{import_lines[0]}行)")
                test_results['tkinter_fix'] = True
            else:
                print("❌ 未找到tkinter导入语句")
        except Exception as e:
            print(f"❌ tkinter修复验证失败: {e}")
        
        # 测试4：KDJ配置修复验证
        print("📋 测试4：KDJ配置修复验证...")
        try:
            # 检查是否使用了安全的config.get()方法
            safe_kdj_count = code.count("config.get('KDJ', {})")
            unsafe_kdj_count = code.count("config['KDJ']")
            
            print(f"✅ KDJ安全访问: {safe_kdj_count}处, 不安全访问: {unsafe_kdj_count}处")
            if safe_kdj_count > 0:
                test_results['kdj_fix'] = True
        except Exception as e:
            print(f"❌ KDJ配置修复验证失败: {e}")
        
        # 测试5：异常处理检查
        print("📋 测试5：异常处理质量检查...")
        try:
            bare_except_count = code.count('except:')
            proper_except_count = code.count('except Exception')
            
            print(f"✅ 异常处理: 裸露except: {bare_except_count}处, 规范except: {proper_except_count}处")
            if bare_except_count < 5:  # 允许少量裸露except
                test_results['exception_handling'] = True
        except Exception as e:
            print(f"❌ 异常处理检查失败: {e}")
        
        # 测试6：重复代码检查
        print("📋 测试6：重复代码清理验证...")
        try:
            # 检查重复函数定义
            duplicate_functions = [
                'load_pinbar_config',
                'load_system_config', 
                'setup_auto_save_triggers',
                'unified_config_save'
            ]
            
            duplicate_count = 0
            for func_name in duplicate_functions:
                func_pattern = f'def {func_name}('
                count = code.count(func_pattern)
                if count > 1:
                    duplicate_count += 1
                    print(f"⚠️ 发现重复函数: {func_name} ({count}次)")
            
            if duplicate_count == 0:
                print("✅ 重复代码清理验证通过")
                test_results['duplicate_removal'] = True
            else:
                print(f"❌ 仍有{duplicate_count}个重复函数")
        except Exception as e:
            print(f"❌ 重复代码检查失败: {e}")
        
        # 测试7：WMZC模块导入测试
        print("📋 测试7：WMZC模块导入测试...")
        try:
            import WMZC
            print("✅ WMZC模块导入成功")
            
            # 测试TradingApp类创建
            if hasattr(WMZC, 'TradingApp'):
                print("✅ TradingApp类存在")
            else:
                print("❌ TradingApp类不存在")
                
        except Exception as e:
            print(f"❌ WMZC模块导入失败: {e}")
        
        return test_results
        
    except Exception as e:
        print(f"❌ 全面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return test_results

def generate_quality_report(test_results):
    """生成代码质量报告"""
    print("\n" + "="*80)
    print("🎯 WMZC量化交易系统代码质量报告")
    print("="*80)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    print(f"📊 测试总数: {total_tests}")
    print(f"✅ 通过测试: {passed_tests}")
    print(f"❌ 失败测试: {total_tests - passed_tests}")
    print(f"🎯 通过率: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📋 详细结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 恭喜！所有代码质量测试通过！")
        print("✅ WMZC量化交易系统代码质量达到医疗级标准")
    else:
        print(f"\n⚠️ 还有{total_tests - passed_tests}个问题需要修复")
    
    print("="*80)

if __name__ == "__main__":
    results = comprehensive_quality_test()
    generate_quality_report(results)
