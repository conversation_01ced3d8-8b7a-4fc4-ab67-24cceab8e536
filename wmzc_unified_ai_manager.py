#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 WMZC统一AI管理器 - 全阶段集成完成
整合基础AI、高级功能、DeFi应用的统一管理系统
"""

import asyncio
import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

# 导入所有AI模块
try:
    from wmzc_ai_integration_production import (
        WMZCAIIntegrator, AIConfig, WMZCCompatibleAIClient
    )
    from wmzc_advanced_ai_features import (
        MultiStrategyAIEnhancer, AdvancedSentimentAnalyzer, 
        AIRiskManager, PerformanceMonitor, AdvancedAIConfig
    )
    from wmzc_defi_advanced_features import (
        DeFiProtocolIntegrator, CrossExchangeArbitrageAI,
        ReinforcementLearningAgent, AutoParameterOptimizer, DeFiConfig
    )
    ALL_MODULES_AVAILABLE = True
except ImportError as e:
    ALL_MODULES_AVAILABLE = False
    print(f"AI模块导入失败: {e}")

# 兼容WMZC日志系统
def log(message, level="INFO", context=None, error_details=None):
    """兼容WMZC.py的智能日志函数"""
    try:
        if 'log_manager' in globals() and log_manager is not None:
            return log_manager.log(message, level, context, error_details)
    except:
        pass
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [UnifiedAI-{level}] {message}")

@dataclass
class UnifiedAIConfig:
    """统一AI配置"""
    # 基础AI配置
    deepseek_api_key: str = ""
    daily_cost_limit: float = 20.0
    timeout: float = 5.0
    
    # 阶段功能开关
    enable_basic_ai: bool = True
    enable_advanced_features: bool = True
    enable_defi_features: bool = True
    
    # 高级功能配置
    enable_multi_strategy: bool = True
    enable_advanced_sentiment: bool = True
    enable_risk_management: bool = True
    enable_performance_monitoring: bool = True
    
    # DeFi功能配置
    enable_defi_integration: bool = True
    enable_cross_exchange_arbitrage: bool = True
    enable_reinforcement_learning: bool = True
    enable_auto_optimization: bool = True
    
    @classmethod
    def from_wmzc_config(cls, wmzc_config_manager):
        """从WMZC配置管理器创建统一配置"""
        try:
            api_key = wmzc_config_manager.get_config_value('ai', 'deepseek_api_key', '')
            daily_limit = float(wmzc_config_manager.get_config_value('ai', 'daily_cost_limit', '20.0'))
            
            return cls(
                deepseek_api_key=api_key,
                daily_cost_limit=daily_limit
            )
        except Exception as e:
            log(f"从WMZC配置创建统一AI配置失败: {e}", "WARNING")
            return cls()

class WMZCUnifiedAIManager:
    """WMZC统一AI管理器"""
    
    def __init__(self, wmzc_app, config: UnifiedAIConfig):
        self.wmzc = wmzc_app
        self.config = config
        
        # AI组件
        self.basic_ai = None
        self.multi_strategy_enhancer = None
        self.advanced_sentiment = None
        self.risk_manager = None
        self.performance_monitor = None
        self.defi_integrator = None
        self.arbitrage_ai = None
        self.rl_agent = None
        self.parameter_optimizer = None
        
        # AI客户端
        self.ai_client = None
        
        # 状态管理
        self.initialization_status = {
            'basic_ai': False,
            'advanced_features': False,
            'defi_features': False,
            'all_systems': False
        }
        
        # 性能指标
        self.unified_metrics = {
            'total_ai_calls': 0,
            'successful_enhancements': 0,
            'total_cost': 0.0,
            'system_uptime': 0,
            'last_update': time.time()
        }
        
        log("🎯 WMZC统一AI管理器初始化开始", "INFO")
    
    async def initialize_all_systems(self) -> bool:
        """初始化所有AI系统"""
        
        if not ALL_MODULES_AVAILABLE:
            log("AI模块不完整，无法初始化统一系统", "ERROR")
            return False
        
        try:
            # 阶段1：基础AI初始化
            if self.config.enable_basic_ai:
                await self.initialize_basic_ai()
            
            # 阶段2：高级功能初始化
            if self.config.enable_advanced_features:
                await self.initialize_advanced_features()
            
            # 阶段3：DeFi功能初始化
            if self.config.enable_defi_features:
                await self.initialize_defi_features()
            
            # 启动统一监控
            await self.start_unified_monitoring()
            
            self.initialization_status['all_systems'] = True
            log("🎉 所有AI系统初始化完成", "INFO")
            return True
            
        except Exception as e:
            log(f"AI系统初始化失败: {e}", "ERROR")
            return False
    
    async def initialize_basic_ai(self) -> bool:
        """初始化基础AI功能"""
        
        try:
            # 创建AI配置
            ai_config = AIConfig(
                api_key=self.config.deepseek_api_key,
                daily_cost_limit=self.config.daily_cost_limit,
                timeout=self.config.timeout
            )
            
            # 创建AI客户端
            self.ai_client = WMZCCompatibleAIClient(ai_config)
            
            # 创建基础AI集成器
            self.basic_ai = WMZCAIIntegrator(self.wmzc, ai_config)
            
            # 初始化基础AI
            success = await self.basic_ai.initialize()
            
            if success:
                self.initialization_status['basic_ai'] = True
                log("✅ 基础AI功能初始化成功", "INFO")
                return True
            else:
                log("❌ 基础AI功能初始化失败", "ERROR")
                return False
                
        except Exception as e:
            log(f"基础AI初始化异常: {e}", "ERROR")
            return False
    
    async def initialize_advanced_features(self) -> bool:
        """初始化高级AI功能"""
        
        try:
            # 创建高级AI配置
            advanced_config = AdvancedAIConfig(
                enable_multi_strategy=self.config.enable_multi_strategy,
                enable_advanced_sentiment=self.config.enable_advanced_sentiment,
                enable_risk_management=self.config.enable_risk_management,
                enable_performance_monitoring=self.config.enable_performance_monitoring
            )
            
            # 初始化高级组件
            if self.config.enable_multi_strategy:
                self.multi_strategy_enhancer = MultiStrategyAIEnhancer(self.ai_client, advanced_config)
            
            if self.config.enable_advanced_sentiment:
                self.advanced_sentiment = AdvancedSentimentAnalyzer(self.ai_client, advanced_config)
            
            if self.config.enable_risk_management:
                self.risk_manager = AIRiskManager(self.ai_client, advanced_config)
            
            if self.config.enable_performance_monitoring:
                self.performance_monitor = PerformanceMonitor(advanced_config)
                self.performance_monitor.start_monitoring()
            
            self.initialization_status['advanced_features'] = True
            log("✅ 高级AI功能初始化成功", "INFO")
            return True
            
        except Exception as e:
            log(f"高级AI功能初始化失败: {e}", "ERROR")
            return False
    
    async def initialize_defi_features(self) -> bool:
        """初始化DeFi功能"""
        
        try:
            # 创建DeFi配置
            defi_config = DeFiConfig(
                enable_defi_integration=self.config.enable_defi_integration,
                enable_cross_exchange_arbitrage=self.config.enable_cross_exchange_arbitrage,
                enable_reinforcement_learning=self.config.enable_reinforcement_learning,
                enable_auto_optimization=self.config.enable_auto_optimization
            )
            
            # 初始化DeFi组件
            if self.config.enable_defi_integration:
                self.defi_integrator = DeFiProtocolIntegrator(self.ai_client, defi_config)
            
            if self.config.enable_cross_exchange_arbitrage:
                self.arbitrage_ai = CrossExchangeArbitrageAI(self.ai_client, defi_config)
            
            if self.config.enable_reinforcement_learning:
                self.rl_agent = ReinforcementLearningAgent(self.ai_client, defi_config)
            
            if self.config.enable_auto_optimization:
                self.parameter_optimizer = AutoParameterOptimizer(self.ai_client, defi_config)
            
            self.initialization_status['defi_features'] = True
            log("✅ DeFi功能初始化成功", "INFO")
            return True
            
        except Exception as e:
            log(f"DeFi功能初始化失败: {e}", "ERROR")
            return False
    
    async def start_unified_monitoring(self):
        """启动统一监控"""
        
        # 启动后台监控任务
        asyncio.create_task(self.unified_monitoring_loop())
        log("📊 统一监控系统已启动", "INFO")
    
    async def unified_monitoring_loop(self):
        """统一监控循环"""
        
        while True:
            try:
                # 更新统一指标
                await self.update_unified_metrics()
                
                # 检查系统健康状态
                await self.check_system_health()
                
                # 等待下一个监控周期
                await asyncio.sleep(60)  # 每分钟监控一次
                
            except Exception as e:
                log(f"统一监控循环错误: {e}", "ERROR")
                await asyncio.sleep(60)
    
    async def update_unified_metrics(self):
        """更新统一指标"""
        
        try:
            # 收集各组件指标
            metrics = {
                'timestamp': time.time(),
                'basic_ai': {},
                'advanced_features': {},
                'defi_features': {},
                'system_status': self.initialization_status
            }
            
            # 基础AI指标
            if self.basic_ai:
                metrics['basic_ai'] = self.basic_ai.get_performance_metrics()
            
            # 高级功能指标
            if self.performance_monitor:
                metrics['advanced_features'] = self.performance_monitor.get_dashboard_data()
            
            # DeFi功能指标
            if self.rl_agent:
                metrics['defi_features']['rl_performance'] = self.rl_agent.get_performance_metrics()
            
            if self.parameter_optimizer:
                metrics['defi_features']['optimization_summary'] = self.parameter_optimizer.get_optimization_summary()
            
            # 更新统一指标
            self.unified_metrics.update({
                'last_update': time.time(),
                'detailed_metrics': metrics
            })
            
        except Exception as e:
            log(f"更新统一指标失败: {e}", "ERROR")
    
    async def check_system_health(self):
        """检查系统健康状态"""
        
        health_issues = []
        
        # 检查基础AI
        if self.config.enable_basic_ai and not self.initialization_status['basic_ai']:
            health_issues.append("基础AI系统未初始化")
        
        # 检查高级功能
        if self.config.enable_advanced_features and not self.initialization_status['advanced_features']:
            health_issues.append("高级AI功能未初始化")
        
        # 检查DeFi功能
        if self.config.enable_defi_features and not self.initialization_status['defi_features']:
            health_issues.append("DeFi功能未初始化")
        
        # 检查成本使用
        if self.unified_metrics['total_cost'] > self.config.daily_cost_limit:
            health_issues.append(f"日成本超限: ${self.unified_metrics['total_cost']:.2f}")
        
        # 发送健康警告
        if health_issues:
            for issue in health_issues:
                log(f"🚨 系统健康警告: {issue}", "WARNING")
    
    async def enhanced_trading_decision(self, symbol: str, strategy: str, market_data: Dict) -> Dict:
        """增强交易决策 - 整合所有AI功能"""
        
        decision_data = {
            'symbol': symbol,
            'strategy': strategy,
            'market_data': market_data,
            'timestamp': time.time(),
            'ai_enhancements': {}
        }
        
        try:
            # 阶段1：基础AI增强
            if self.basic_ai:
                sentiment = await self.basic_ai.analyze_market_sentiment(symbol, market_data)
                decision_data['ai_enhancements']['basic_sentiment'] = sentiment
            
            # 阶段2：高级功能增强
            if self.multi_strategy_enhancer:
                multi_strategy_result = await self.multi_strategy_enhancer.enhance_all_strategies(symbol, market_data)
                decision_data['ai_enhancements']['multi_strategy'] = multi_strategy_result
            
            if self.advanced_sentiment:
                advanced_sentiment = await self.advanced_sentiment.analyze_comprehensive_sentiment(symbol, market_data)
                decision_data['ai_enhancements']['advanced_sentiment'] = advanced_sentiment
            
            if self.risk_manager:
                # 创建临时信号用于风险评估
                temp_signal = {'action': 'buy', 'confidence': 0.7, 'position_size': 0.1}
                risk_assessment = await self.risk_manager.assess_trade_risk(temp_signal, symbol, market_data)
                decision_data['ai_enhancements']['risk_assessment'] = risk_assessment
            
            # 阶段3：DeFi和高级应用
            if self.rl_agent:
                state_key = self.rl_agent.get_state_key(market_data)
                rl_action = self.rl_agent.choose_action(state_key)
                decision_data['ai_enhancements']['rl_recommendation'] = {
                    'action': rl_action,
                    'state': state_key
                }
            
            # 综合决策
            final_decision = await self.synthesize_final_decision(decision_data)
            
            # 更新统计
            self.unified_metrics['total_ai_calls'] += 1
            if final_decision.get('enhanced', False):
                self.unified_metrics['successful_enhancements'] += 1
            
            return final_decision
            
        except Exception as e:
            log(f"增强交易决策失败: {e}", "ERROR")
            return {
                'action': 'hold',
                'confidence': 0.5,
                'reason': 'AI增强失败',
                'fallback': True
            }
    
    async def synthesize_final_decision(self, decision_data: Dict) -> Dict:
        """综合最终决策"""
        
        enhancements = decision_data.get('ai_enhancements', {})
        
        # 收集所有AI建议
        ai_recommendations = []
        
        # 基础情绪分析
        basic_sentiment = enhancements.get('basic_sentiment', {})
        if basic_sentiment.get('score', 0) != 0:
            ai_recommendations.append({
                'source': 'basic_sentiment',
                'action': 'buy' if basic_sentiment['score'] > 0.3 else 'sell' if basic_sentiment['score'] < -0.3 else 'hold',
                'confidence': basic_sentiment.get('confidence', 0.5),
                'weight': 0.2
            })
        
        # 多策略集成
        multi_strategy = enhancements.get('multi_strategy', {})
        ensemble_signal = multi_strategy.get('ensemble_signal', {})
        if ensemble_signal:
            ai_recommendations.append({
                'source': 'multi_strategy',
                'action': ensemble_signal.get('final_action', 'hold'),
                'confidence': ensemble_signal.get('final_confidence', 0.5),
                'weight': 0.4
            })
        
        # 高级情绪分析
        advanced_sentiment = enhancements.get('advanced_sentiment', {})
        if advanced_sentiment.get('overall_score', 0) != 0:
            ai_recommendations.append({
                'source': 'advanced_sentiment',
                'action': 'buy' if advanced_sentiment['overall_score'] > 0.3 else 'sell' if advanced_sentiment['overall_score'] < -0.3 else 'hold',
                'confidence': advanced_sentiment.get('overall_confidence', 0.5),
                'weight': 0.2
            })
        
        # 强化学习建议
        rl_recommendation = enhancements.get('rl_recommendation', {})
        if rl_recommendation:
            ai_recommendations.append({
                'source': 'reinforcement_learning',
                'action': rl_recommendation.get('action', 'hold'),
                'confidence': 0.6,  # RL置信度
                'weight': 0.2
            })
        
        # 风险管理检查
        risk_assessment = enhancements.get('risk_assessment', {})
        risk_approved = risk_assessment.get('approved', True)
        
        # 加权投票决策
        if ai_recommendations:
            action_scores = {'buy': 0, 'sell': 0, 'hold': 0}
            total_weight = 0
            total_confidence = 0
            
            for rec in ai_recommendations:
                action = rec['action']
                confidence = rec['confidence']
                weight = rec['weight']
                
                action_scores[action] += confidence * weight
                total_weight += weight
                total_confidence += confidence * weight
            
            # 确定最终行动
            final_action = max(action_scores, key=action_scores.get)
            final_confidence = total_confidence / max(total_weight, 1)
            
            # 风险管理覆盖
            if not risk_approved:
                final_action = 'hold'
                final_confidence *= 0.5
            
            return {
                'action': final_action,
                'confidence': final_confidence,
                'ai_recommendations': ai_recommendations,
                'risk_approved': risk_approved,
                'enhanced': True,
                'synthesis_method': 'weighted_voting',
                'decision_timestamp': time.time()
            }
        else:
            return {
                'action': 'hold',
                'confidence': 0.5,
                'reason': '无AI建议可用',
                'enhanced': False
            }
    
    def get_unified_status(self) -> Dict:
        """获取统一状态"""
        
        return {
            'initialization_status': self.initialization_status,
            'unified_metrics': self.unified_metrics,
            'config': {
                'basic_ai_enabled': self.config.enable_basic_ai,
                'advanced_features_enabled': self.config.enable_advanced_features,
                'defi_features_enabled': self.config.enable_defi_features
            },
            'components': {
                'basic_ai': self.basic_ai is not None,
                'multi_strategy_enhancer': self.multi_strategy_enhancer is not None,
                'advanced_sentiment': self.advanced_sentiment is not None,
                'risk_manager': self.risk_manager is not None,
                'performance_monitor': self.performance_monitor is not None,
                'defi_integrator': self.defi_integrator is not None,
                'arbitrage_ai': self.arbitrage_ai is not None,
                'rl_agent': self.rl_agent is not None,
                'parameter_optimizer': self.parameter_optimizer is not None
            },
            'status_timestamp': time.time()
        }
    
    async def shutdown_all_systems(self):
        """关闭所有AI系统"""
        
        try:
            # 停止性能监控
            if self.performance_monitor:
                self.performance_monitor.stop_monitoring()
            
            # 关闭AI客户端
            if self.ai_client:
                await self.ai_client.__aexit__(None, None, None)
            
            log("🔄 所有AI系统已安全关闭", "INFO")
            
        except Exception as e:
            log(f"关闭AI系统时发生错误: {e}", "ERROR")

# 全局统一AI管理器实例
unified_ai_manager = None

def initialize_unified_ai_for_wmzc(wmzc_app, deepseek_api_key: str = None) -> Optional[WMZCUnifiedAIManager]:
    """为WMZC初始化统一AI系统"""
    
    global unified_ai_manager
    
    try:
        # 创建统一配置
        if deepseek_api_key:
            config = UnifiedAIConfig(deepseek_api_key=deepseek_api_key)
        elif hasattr(wmzc_app, 'config_manager'):
            config = UnifiedAIConfig.from_wmzc_config(wmzc_app.config_manager)
        else:
            log("无法获取API密钥配置", "ERROR")
            return None
        
        # 创建统一AI管理器
        unified_ai_manager = WMZCUnifiedAIManager(wmzc_app, config)
        
        # 异步初始化所有系统
        async def init_all():
            success = await unified_ai_manager.initialize_all_systems()
            if success:
                log("🎉 WMZC统一AI系统初始化完成", "INFO")
            else:
                log("❌ WMZC统一AI系统初始化失败", "ERROR")
            return success
        
        # 运行初始化
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            success = loop.run_until_complete(init_all())
            loop.close()
            
            if success:
                return unified_ai_manager
            else:
                return None
                
        except Exception as e:
            log(f"统一AI初始化异常: {e}", "ERROR")
            return None
            
    except Exception as e:
        log(f"统一AI管理器创建失败: {e}", "ERROR")
        return None

if __name__ == "__main__":
    print("🎯 WMZC统一AI管理器")
    print("请在WMZC.py中调用 initialize_unified_ai_for_wmzc() 函数进行集成")
