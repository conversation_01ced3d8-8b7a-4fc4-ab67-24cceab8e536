#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC交易循环模块化重构
将超长的trading_loop函数拆分为可维护的模块
"""

import asyncio
import time
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

# 🔧 Bug修复: 添加pandas导入并处理导入错误
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    # 🔧 P1-5修复：改进pandas兼容性处理，提供更完整的Mock实现
    class MockPandas:
        class DataFrame:
            def __init__(self, data=None, columns=None, *args, **kwargs):
                self.data = data or []
                self.columns = columns or []
                self._empty = len(self.data) == 0

            def copy(self):
                return MockPandas.DataFrame(self.data, self.columns)

            @property
            def empty(self):
                return self._empty

            def __len__(self):
                return len(self.data) if self.data else 0

            def __getitem__(self, key):
                # 简单的列访问支持
                return []

            def iloc(self, *args):
                # 简单的iloc支持
                return self

            def to_dict(self, *args, **kwargs):
                return {}

            def head(self, n=5):
                return self

            def tail(self, n=5):
                return self

            def dropna(self, *args, **kwargs):
                return self

            def fillna(self, *args, **kwargs):
                return self

        @staticmethod
        def concat(*args, **kwargs):
            return MockPandas.DataFrame()

        @staticmethod
        def read_csv(*args, **kwargs):
            return MockPandas.DataFrame()

    pd = MockPandas()

    # 🔧 P1-5修复：添加pandas不可用时的警告
    import warnings
    warnings.warn("pandas不可用，使用Mock实现。某些功能可能受限。", UserWarning)

# 🔧 Bug修复: 避免循环导入，使用延迟导入
def get_wmzc_components():
    """延迟导入WMZC组件以避免循环导入"""
    try:
        import WMZC
        return WMZC.log, WMZC.global_state, WMZC.config, getattr(WMZC, 'memory_monitor', None)
    except (ImportError, AttributeError):
        # 如果无法导入，定义占位符
        def log(msg, level="INFO"):
            print(f"[{level}] {msg}")

        class GlobalState:
            trading_active = True
            trading_paused = False
            _enhanced_kline_cache = {}
            _cache_metadata = {}

            def cleanup_expired_cache(self):
                return 0

            def get_cache_statistics(self):
                return {'hit_rate': 0, 'memory_usage_mb': 0}

        return log, GlobalState(), {}, None

# 获取组件
log, global_state, config, memory_monitor = get_wmzc_components()

@dataclass
class TradingLoopState:
    """交易循环状态管理"""
    last_heartbeat: float = 0
    error_count: int = 0
    cleanup_counter: int = 0
    cached_exchange: str = ""
    loop_count: int = 0
    
    def __post_init__(self):
        self.last_heartbeat = time.time()

class TradingLoopManager:
    """交易循环管理器 - 模块化重构版"""
    
    def __init__(self):
        self.state = TradingLoopState()
        self.performance_metrics = {
            'loop_duration': [],
            'kline_fetch_time': [],
            'strategy_execution_time': [],
            'memory_usage': []
        }
    
    async def run_trading_loop(self):
        """主交易循环 - 重构版"""
        log("🚀 模块化交易循环启动", "INFO")
        
        # 初始化
        await self._initialize_trading_loop()
        
        while global_state.trading_active:
            loop_start_time = time.time()
            
            try:
                # 检查交易状态
                if not await self._check_trading_status():
                    break
                
                # 执行循环维护任务
                await self._perform_loop_maintenance()
                
                # 获取市场数据
                market_data = await self._fetch_market_data()
                if market_data is None:
                    await asyncio.sleep(10)
                    continue
                
                # 执行交易策略
                await self._execute_trading_strategies(market_data)
                
                # 更新性能指标
                self._update_performance_metrics(loop_start_time)
                
                # 控制循环频率
                await self._control_loop_frequency(loop_start_time)
                
            except asyncio.CancelledError:
                log("🛑 交易循环被取消", "INFO")
                break
            except Exception as e:
                await self._handle_loop_error(e)
        
        log("🛑 交易循环已停止", "INFO")
    
    async def _initialize_trading_loop(self):
        """初始化交易循环"""
        try:
            # 获取交易所信息
            self.state.cached_exchange = await self._get_current_exchange()
            log(f"🔧 交易所初始化: {self.state.cached_exchange}", "INFO")
            
            # 初始化缓存系统
            await self._initialize_cache_system()
            
            # 初始化性能监控
            self._initialize_performance_monitoring()
            
        except Exception as e:
            log(f"❌ 交易循环初始化失败: {e}", "ERROR")
            raise
    
    async def _check_trading_status(self) -> bool:
        """检查交易状态"""
        if not global_state.trading_active:
            log("🛑 检测到交易停止信号", "INFO")
            return False
        
        if global_state.trading_paused:
            log("⏸️ 交易已暂停，等待恢复", "DEBUG")
            await asyncio.sleep(2)
            return False
        
        return True
    
    async def _perform_loop_maintenance(self):
        """执行循环维护任务"""
        self.state.loop_count += 1
        current_time = time.time()
        
        # 定期清理缓存
        if self.state.loop_count % 10 == 0:
            await self._cleanup_expired_cache()
        
        # 定期输出统计信息
        if self.state.loop_count % 50 == 0:
            await self._log_cache_statistics()
        
        # 定期更新活动状态
        if int(current_time) % 30 == 0:
            await self._update_activity_status()
        
        # 内存监控
        if memory_monitor and hasattr(memory_monitor, 'should_cleanup'):
            if memory_monitor.should_cleanup():
                memory_monitor.cleanup_memory()
    
    async def _fetch_market_data(self) -> Optional[object]:  # 🔧 P1-5修复：使用object类型避免pandas依赖
        """获取市场数据"""
        try:
            # 获取配置
            symbol = config.get('SYMBOL', 'BTC-USDT-SWAP')
            timeframe = config.get('TIMEFRAME', '1m')
            
            log(f"📊 获取市场数据: {symbol} {timeframe}", "DEBUG")
            
            # 检查缓存
            cached_data = await self._get_cached_kline_data(symbol, timeframe)
            if cached_data is not None:
                return cached_data
            
            # 获取新数据
            kline_start_time = time.time()
            df = await self._fetch_kline_data(symbol, timeframe)
            kline_duration = time.time() - kline_start_time
            
            # 更新性能指标
            self.performance_metrics['kline_fetch_time'].append(kline_duration)
            
            # 缓存数据
            if df is not None and not df.empty:
                await self._cache_kline_data(symbol, timeframe, df)
                log(f"✅ 市场数据获取成功: {len(df)}条 ({kline_duration:.2f}s)", "INFO")
            
            return df
            
        except Exception as e:
            log(f"❌ 市场数据获取失败: {e}", "ERROR")
            return None
    
    async def _execute_trading_strategies(self, market_data: object):  # 🔧 P1-5修复：使用object类型
        """执行交易策略"""
        try:
            strategy_start_time = time.time()
            
            # AI分析（如果启用）
            ai_analysis = await self._perform_ai_analysis(market_data)
            
            # 技术指标计算
            indicators = await self._calculate_technical_indicators(market_data)
            
            # 策略执行
            signals = await self._execute_strategies(market_data, indicators, ai_analysis)
            
            # 订单管理
            if signals:
                await self._manage_orders(signals)
            
            # 更新性能指标
            strategy_duration = time.time() - strategy_start_time
            self.performance_metrics['strategy_execution_time'].append(strategy_duration)
            
        except Exception as e:
            log(f"❌ 策略执行失败: {e}", "ERROR")
    
    async def _get_current_exchange(self) -> str:
        """获取当前交易所"""
        # 这里应该调用实际的交易所获取函数
        return config.get('exchange', 'gate')
    
    async def _initialize_cache_system(self):
        """初始化缓存系统"""
        if not hasattr(global_state, '_enhanced_kline_cache'):
            global_state._enhanced_kline_cache = {}
        if not hasattr(global_state, '_cache_metadata'):
            global_state._cache_metadata = {}
        log("🔧 缓存系统初始化完成", "DEBUG")
    
    def _initialize_performance_monitoring(self):
        """初始化性能监控"""
        self.performance_metrics = {
            'loop_duration': [],
            'kline_fetch_time': [],
            'strategy_execution_time': [],
            'memory_usage': []
        }
        log("📊 性能监控初始化完成", "DEBUG")
    
    async def _cleanup_expired_cache(self):
        """清理过期缓存"""
        if hasattr(global_state, 'cleanup_expired_cache'):
            cleaned = global_state.cleanup_expired_cache()
            if cleaned > 0:
                log(f"🧹 清理了{cleaned}个过期缓存项", "DEBUG")
    
    async def _log_cache_statistics(self):
        """输出缓存统计信息"""
        if hasattr(global_state, 'get_cache_statistics'):
            cache_stats = global_state.get_cache_statistics()
            log(f"📊 缓存统计: 命中率{cache_stats.get('hit_rate', 0):.1f}%, "
                f"内存{cache_stats.get('memory_usage_mb', 0):.1f}MB", "INFO")
    
    async def _update_activity_status(self):
        """更新活动状态"""
        # 这里应该调用实际的活动更新函数
        self.state.last_heartbeat = time.time()
        log("💓 活动状态已更新", "DEBUG")
    
    async def _get_cached_kline_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """获取缓存的K线数据"""
        cache_key = f"trading_loop_kline_{symbol}_{timeframe}"
        
        if not hasattr(global_state, '_enhanced_kline_cache'):
            return None
        
        cached_df = global_state._enhanced_kline_cache.get(cache_key)
        if cached_df is None:
            return None
        
        # 检查缓存是否过期
        if hasattr(global_state, '_cache_metadata'):
            metadata = global_state._cache_metadata.get(cache_key)
            if metadata:
                current_time = time.time()
                if current_time - metadata['timestamp'] < metadata['ttl']:
                    metadata['hit_count'] += 1
                    log(f"📊 缓存命中: {symbol} {timeframe}", "DEBUG")
                    return cached_df
        
        return None
    
    async def _fetch_kline_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """获取K线数据 - 🔧 Bug修复: 改进DataFrame处理"""
        try:
            # 这里应该调用实际的K线获取函数
            log(f"📊 从API获取K线数据: {symbol} {timeframe}", "DEBUG")

            # 🔧 Bug修复: 检查pandas是否可用
            if PANDAS_AVAILABLE:
                # 返回空DataFrame但包含正确的列结构
                return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            else:
                # 如果pandas不可用，返回None
                log("⚠️ pandas不可用，无法创建DataFrame", "WARNING")
                return None
        except Exception as e:
            log(f"❌ K线数据获取失败: {e}", "ERROR")
            return None
    
    async def _cache_kline_data(self, symbol: str, timeframe: str, df: pd.DataFrame):
        """缓存K线数据"""
        cache_key = f"trading_loop_kline_{symbol}_{timeframe}"
        current_time = time.time()
        
        # TTL策略
        ttl_map = {
            '1m': 30, '3m': 60, '5m': 120, '15m': 300,
            '30m': 600, '1h': 900, '4h': 1800, '1d': 3600
        }
        cache_ttl = ttl_map.get(timeframe, 30)
        
        # 更新缓存
        global_state._enhanced_kline_cache[cache_key] = df.copy()
        global_state._cache_metadata[cache_key] = {
            'timestamp': current_time,
            'ttl': cache_ttl,
            'hit_count': 0,
            'data_length': len(df),
            'timeframe': timeframe
        }
        
        log(f"💾 数据已缓存: {symbol} {timeframe} TTL={cache_ttl}s", "DEBUG")
    
    async def _perform_ai_analysis(self, market_data: pd.DataFrame) -> Optional[Dict]:
        """执行AI分析"""
        try:
            # 这里应该调用实际的AI分析函数
            log("🤖 执行AI分析", "DEBUG")
            return None
        except Exception as e:
            log(f"❌ AI分析失败: {e}", "WARNING")
            return None
    
    async def _calculate_technical_indicators(self, market_data: pd.DataFrame) -> Dict:
        """计算技术指标"""
        try:
            # 这里应该调用实际的技术指标计算函数
            log("📈 计算技术指标", "DEBUG")
            return {}
        except Exception as e:
            log(f"❌ 技术指标计算失败: {e}", "ERROR")
            return {}
    
    async def _execute_strategies(self, market_data: pd.DataFrame, indicators: Dict, ai_analysis: Optional[Dict]) -> Optional[Dict]:
        """执行交易策略"""
        try:
            # 这里应该调用实际的策略执行函数
            log("🎯 执行交易策略", "DEBUG")
            return None
        except Exception as e:
            log(f"❌ 策略执行失败: {e}", "ERROR")
            return None
    
    async def _manage_orders(self, signals: Dict):
        """管理订单"""
        try:
            # 这里应该调用实际的订单管理函数
            log("📋 管理交易订单", "DEBUG")
        except Exception as e:
            log(f"❌ 订单管理失败: {e}", "ERROR")
    
    def _update_performance_metrics(self, loop_start_time: float):
        """更新性能指标"""
        loop_duration = time.time() - loop_start_time
        self.performance_metrics['loop_duration'].append(loop_duration)
        
        # 保持最近100次的记录
        for metric_list in self.performance_metrics.values():
            if len(metric_list) > 100:
                metric_list.pop(0)
    
    async def _control_loop_frequency(self, loop_start_time: float):
        """控制循环频率"""
        loop_duration = time.time() - loop_start_time
        target_interval = config.get('loop_interval', 15)
        sleep_time = max(target_interval - loop_duration, 1)
        
        if loop_duration > target_interval:
            log(f"⚠️ 循环执行时间过长: {loop_duration:.2f}s", "WARNING")
        
        await asyncio.sleep(sleep_time)
    
    async def _handle_loop_error(self, error: Exception):
        """处理循环错误"""
        self.state.error_count += 1
        log(f"❌ 交易循环错误 (第{self.state.error_count}次): {error}", "ERROR")
        
        if self.state.error_count > 10:
            log("🚨 错误次数过多，停止交易循环", "CRITICAL")
            global_state.trading_active = False
        else:
            await asyncio.sleep(5)  # 错误后等待5秒
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        summary = {}
        for metric_name, values in self.performance_metrics.items():
            if values:
                summary[metric_name] = {
                    'avg': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'count': len(values)
                }
            else:
                summary[metric_name] = {'avg': 0, 'min': 0, 'max': 0, 'count': 0}
        
        return summary

# 全局交易循环管理器实例
trading_loop_manager = TradingLoopManager()

# 兼容性函数
async def trading_loop():
    """兼容性包装函数"""
    await trading_loop_manager.run_trading_loop()
