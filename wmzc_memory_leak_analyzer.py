#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC内存泄漏专业分析器
基于336MB内存增长的深度诊断和安全修复
"""

import os
import gc
import sys
import time
import psutil
import threading
import traceback
from datetime import datetime
from collections import defaultdict, deque

class WMZCMemoryLeakAnalyzer:
    """WMZC内存泄漏专业分析器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.baseline_memory = self.process.memory_info().rss
        self.memory_snapshots = deque(maxlen=100)
        self.leak_sources = defaultdict(list)
        self.analysis_results = {}
        
        # 336MB泄漏阈值分析
        self.critical_threshold = 336 * 1024 * 1024  # 336MB
        self.warning_threshold = 200 * 1024 * 1024   # 200MB
        
        print(f"🔍 WMZC内存泄漏分析器初始化")
        print(f"📊 基线内存: {self.baseline_memory / 1024 / 1024:.1f} MB")
        print(f"⚠️ 警告阈值: {self.warning_threshold / 1024 / 1024:.1f} MB")
        print(f"🚨 危险阈值: {self.critical_threshold / 1024 / 1024:.1f} MB")
    
    def analyze_wmzc_memory_patterns(self):
        """分析WMZC内存泄漏模式"""
        print("\n🔍 开始WMZC内存泄漏模式分析...")
        
        # 1. 分析已知的内存泄漏源
        self._analyze_known_leak_sources()
        
        # 2. 检测循环引用
        self._detect_circular_references()
        
        # 3. 分析缓存系统
        self._analyze_cache_systems()
        
        # 4. 检测线程泄漏
        self._detect_thread_leaks()
        
        # 5. 分析DataFrame内存使用
        self._analyze_dataframe_memory()
        
        # 6. 检测WebSocket连接泄漏
        self._detect_websocket_leaks()
        
        return self.analysis_results
    
    def _analyze_known_leak_sources(self):
        """分析已知的内存泄漏源"""
        print("📋 分析已知内存泄漏源...")
        
        known_sources = {
            'signal_log': '交易信号日志累积',
            'trading_records': '交易记录累积', 
            'audit_trail': '审计日志累积',
            'alert_history': '警报历史累积',
            'connection_errors': 'WebSocket连接错误累积',
            'memory_snapshots': '内存快照累积',
            'calculation_times': '计算时间记录累积',
            'message_counts': '消息计数累积'
        }
        
        leak_risks = []
        
        try:
            import WMZC
            
            for source_name, description in known_sources.items():
                risk_level = self._assess_source_risk(source_name, description)
                if risk_level > 0:
                    leak_risks.append({
                        'source': source_name,
                        'description': description,
                        'risk_level': risk_level,
                        'estimated_memory_mb': risk_level * 10  # 估算内存使用
                    })
                    
        except ImportError:
            print("⚠️ 无法导入WMZC模块进行分析")
        
        self.analysis_results['known_sources'] = leak_risks
        
        total_estimated = sum(item['estimated_memory_mb'] for item in leak_risks)
        print(f"📊 已知源估算内存使用: {total_estimated:.1f} MB")
        
        if total_estimated > 300:
            print(f"🚨 警告: 已知源可能导致 {total_estimated:.1f} MB 内存泄漏")
    
    def _assess_source_risk(self, source_name, description):
        """评估内存泄漏源的风险级别"""
        try:
            import WMZC
            
            # 检查全局变量
            if hasattr(WMZC, source_name):
                obj = getattr(WMZC, source_name)
                if isinstance(obj, (list, deque)):
                    size = len(obj)
                    if size > 1000:
                        return 5  # 高风险
                    elif size > 500:
                        return 3  # 中风险
                    elif size > 100:
                        return 1  # 低风险
                elif isinstance(obj, dict):
                    size = len(obj)
                    if size > 10000:
                        return 5  # 高风险
                    elif size > 5000:
                        return 3  # 中风险
                    elif size > 1000:
                        return 1  # 低风险
            
            return 0  # 无风险
            
        except Exception as e:
            print(f"⚠️ 评估 {source_name} 风险失败: {e}")
            return 0
    
    def _detect_circular_references(self):
        """检测循环引用"""
        print("🔄 检测循环引用...")
        
        # 强制垃圾回收并统计
        before_gc = len(gc.get_objects())
        collected = gc.collect()
        after_gc = len(gc.get_objects())
        
        circular_refs = {
            'objects_before_gc': before_gc,
            'objects_collected': collected,
            'objects_after_gc': after_gc,
            'potential_circular_refs': collected
        }
        
        self.analysis_results['circular_references'] = circular_refs
        
        if collected > 100:
            print(f"🚨 发现大量循环引用: {collected} 个对象被回收")
        else:
            print(f"✅ 循环引用检测正常: {collected} 个对象被回收")
    
    def _analyze_cache_systems(self):
        """分析缓存系统内存使用"""
        print("💾 分析缓存系统...")
        
        cache_analysis = {}
        
        try:
            import WMZC
            
            # 检查各种缓存
            cache_objects = [
                'smart_cache_manager',
                'indicator_cache', 
                'kline_cache',
                'kdj_cache',
                'macd_cache',
                'rsi_cache'
            ]
            
            total_cache_memory = 0
            
            for cache_name in cache_objects:
                if hasattr(WMZC, cache_name):
                    cache_obj = getattr(WMZC, cache_name)
                    cache_info = self._analyze_cache_object(cache_obj, cache_name)
                    cache_analysis[cache_name] = cache_info
                    total_cache_memory += cache_info.get('estimated_memory_mb', 0)
            
            cache_analysis['total_cache_memory_mb'] = total_cache_memory
            
            if total_cache_memory > 200:
                print(f"🚨 缓存内存使用过高: {total_cache_memory:.1f} MB")
            else:
                print(f"✅ 缓存内存使用正常: {total_cache_memory:.1f} MB")
                
        except ImportError:
            print("⚠️ 无法导入WMZC模块分析缓存")
        
        self.analysis_results['cache_systems'] = cache_analysis
    
    def _analyze_cache_object(self, cache_obj, cache_name):
        """分析单个缓存对象"""
        try:
            cache_info = {
                'type': type(cache_obj).__name__,
                'size': 0,
                'estimated_memory_mb': 0
            }
            
            if hasattr(cache_obj, '__len__'):
                cache_info['size'] = len(cache_obj)
                # 估算内存使用 (每个条目约1KB)
                cache_info['estimated_memory_mb'] = cache_info['size'] * 0.001
            
            if hasattr(cache_obj, 'get_stats'):
                stats = cache_obj.get_stats()
                cache_info.update(stats)
            
            return cache_info
            
        except Exception as e:
            return {'error': str(e), 'estimated_memory_mb': 0}
    
    def _detect_thread_leaks(self):
        """检测线程泄漏"""
        print("🧵 检测线程泄漏...")
        
        active_threads = threading.active_count()
        thread_info = {
            'active_threads': active_threads,
            'thread_names': [t.name for t in threading.enumerate()],
            'daemon_threads': len([t for t in threading.enumerate() if t.daemon])
        }
        
        self.analysis_results['thread_analysis'] = thread_info
        
        if active_threads > 20:
            print(f"🚨 线程数量过多: {active_threads} 个活跃线程")
        else:
            print(f"✅ 线程数量正常: {active_threads} 个活跃线程")
    
    def _analyze_dataframe_memory(self):
        """分析DataFrame内存使用"""
        print("📊 分析DataFrame内存使用...")
        
        dataframe_analysis = {
            'total_dataframes': 0,
            'estimated_memory_mb': 0,
            'large_dataframes': []
        }
        
        # 统计所有DataFrame对象
        for obj in gc.get_objects():
            try:
                if hasattr(obj, 'memory_usage') and hasattr(obj, 'shape'):
                    # 这是一个pandas DataFrame
                    dataframe_analysis['total_dataframes'] += 1
                    
                    # 估算内存使用
                    try:
                        memory_usage = obj.memory_usage(deep=True).sum()
                        memory_mb = memory_usage / 1024 / 1024
                        dataframe_analysis['estimated_memory_mb'] += memory_mb
                        
                        if memory_mb > 10:  # 大于10MB的DataFrame
                            dataframe_analysis['large_dataframes'].append({
                                'shape': obj.shape,
                                'memory_mb': memory_mb,
                                'columns': len(obj.columns) if hasattr(obj, 'columns') else 0
                            })
                    except Exception:
                        pass
                        
            except Exception:
                continue
        
        self.analysis_results['dataframe_analysis'] = dataframe_analysis
        
        total_df_memory = dataframe_analysis['estimated_memory_mb']
        if total_df_memory > 100:
            print(f"🚨 DataFrame内存使用过高: {total_df_memory:.1f} MB")
        else:
            print(f"✅ DataFrame内存使用正常: {total_df_memory:.1f} MB")
    
    def _detect_websocket_leaks(self):
        """检测WebSocket连接泄漏"""
        print("🌐 检测WebSocket连接泄漏...")
        
        websocket_analysis = {
            'websocket_objects': 0,
            'connection_objects': 0,
            'potential_leaks': []
        }
        
        # 搜索WebSocket相关对象
        for obj in gc.get_objects():
            try:
                obj_type = type(obj).__name__.lower()
                if 'websocket' in obj_type or 'connection' in obj_type:
                    websocket_analysis['websocket_objects'] += 1
                    
                    # 检查是否有未关闭的连接
                    if hasattr(obj, 'closed') and not obj.closed:
                        websocket_analysis['potential_leaks'].append({
                            'type': type(obj).__name__,
                            'status': 'open'
                        })
                        
            except Exception:
                continue
        
        self.analysis_results['websocket_analysis'] = websocket_analysis
        
        leak_count = len(websocket_analysis['potential_leaks'])
        if leak_count > 0:
            print(f"🚨 发现 {leak_count} 个潜在WebSocket泄漏")
        else:
            print(f"✅ WebSocket连接检测正常")
    
    def generate_memory_report(self):
        """生成内存分析报告"""
        current_memory = self.process.memory_info().rss
        memory_growth = current_memory - self.baseline_memory
        
        print(f"\n📋 WMZC内存泄漏分析报告")
        print(f"=" * 60)
        print(f"📊 当前内存使用: {current_memory / 1024 / 1024:.1f} MB")
        print(f"📈 内存增长: {memory_growth / 1024 / 1024:.1f} MB")
        print(f"🎯 336MB阈值状态: {'🚨 已超过' if memory_growth > self.critical_threshold else '✅ 正常'}")
        
        # 分析主要泄漏源
        print(f"\n🔍 主要内存泄漏源分析:")
        
        if 'known_sources' in self.analysis_results:
            high_risk_sources = [s for s in self.analysis_results['known_sources'] if s['risk_level'] >= 3]
            if high_risk_sources:
                print(f"🚨 高风险源 ({len(high_risk_sources)}个):")
                for source in high_risk_sources:
                    print(f"  • {source['source']}: {source['description']} ({source['estimated_memory_mb']:.1f}MB)")
        
        if 'cache_systems' in self.analysis_results:
            cache_memory = self.analysis_results['cache_systems'].get('total_cache_memory_mb', 0)
            if cache_memory > 50:
                print(f"💾 缓存系统: {cache_memory:.1f} MB")
        
        if 'dataframe_analysis' in self.analysis_results:
            df_memory = self.analysis_results['dataframe_analysis']['estimated_memory_mb']
            if df_memory > 50:
                print(f"📊 DataFrame: {df_memory:.1f} MB")
        
        # 提供修复建议
        print(f"\n🔧 修复建议:")
        self._generate_fix_recommendations(memory_growth)
        
        return {
            'current_memory_mb': current_memory / 1024 / 1024,
            'memory_growth_mb': memory_growth / 1024 / 1024,
            'exceeds_336mb_threshold': memory_growth > self.critical_threshold,
            'analysis_results': self.analysis_results
        }
    
    def _generate_fix_recommendations(self, memory_growth):
        """生成修复建议"""
        if memory_growth > self.critical_threshold:
            print(f"🚨 紧急修复 (内存增长 {memory_growth / 1024 / 1024:.1f} MB > 336 MB):")
            print(f"  1. 立即执行内存清理")
            print(f"  2. 重启WMZC系统")
            print(f"  3. 启用自动内存监控")
        elif memory_growth > self.warning_threshold:
            print(f"⚠️ 预防性修复 (内存增长 {memory_growth / 1024 / 1024:.1f} MB > 200 MB):")
            print(f"  1. 清理缓存和日志")
            print(f"  2. 优化数据结构")
            print(f"  3. 增强垃圾回收")
        else:
            print(f"✅ 内存使用正常，建议定期监控")

def main():
    """主函数"""
    print("🚀 WMZC内存泄漏专业分析器")
    print("=" * 60)
    
    analyzer = WMZCMemoryLeakAnalyzer()
    
    try:
        # 执行内存分析
        analyzer.analyze_wmzc_memory_patterns()
        
        # 生成报告
        report = analyzer.generate_memory_report()
        
        # 保存分析结果
        import json
        with open('wmzc_memory_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 分析报告已保存: wmzc_memory_analysis_report.json")
        
        return report
        
    except Exception as e:
        print(f"❌ 内存分析失败: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
