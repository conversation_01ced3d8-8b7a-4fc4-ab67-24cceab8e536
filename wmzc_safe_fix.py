#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC安全Bug修复脚本
只修复最安全的问题：重复导入和裸露except
"""

import re
import shutil
from datetime import datetime

class SafeBugFixer:
    """安全Bug修复器"""
    
    def __init__(self, file_path: str = "WMZC.py"):
        self.file_path = file_path
        self.backup_path = f"WMZC_safe_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        self.lines = []
        self.fixes_applied = 0
        
    def create_backup(self):
        """创建备份"""
        try:
            shutil.copy2(self.file_path, self.backup_path)
            print(f"✅ 备份已创建: {self.backup_path}")
            return True
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    
    def load_code(self):
        """加载代码"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.lines = f.readlines()
            print(f"✅ 加载了 {len(self.lines)} 行代码")
            return True
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False
    
    def save_code(self):
        """保存代码"""
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                f.writelines(self.lines)
            print(f"✅ 保存完成，应用了 {self.fixes_applied} 个修复")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def fix_duplicate_imports_safe(self):
        """安全修复重复导入 - 只删除完全相同的连续重复"""
        print("🔧 安全修复重复导入...")
        
        lines_to_remove = []
        prev_import = None
        
        for i, line in enumerate(self.lines):
            line_stripped = line.strip()
            
            # 只处理简单的单模块导入
            if (line_stripped.startswith('import ') and 
                ' as ' not in line_stripped and 
                ',' not in line_stripped and
                len(line_stripped.split()) == 2):
                
                # 检查是否与前一行完全相同
                if prev_import == line_stripped:
                    lines_to_remove.append(i)
                    print(f"   删除连续重复导入: {line_stripped}")
                else:
                    prev_import = line_stripped
            else:
                prev_import = None
        
        # 删除重复行
        for i in reversed(lines_to_remove):
            del self.lines[i]
            self.fixes_applied += 1
        
        print(f"✅ 安全修复了 {len(lines_to_remove)} 个重复导入")
    
    def fix_bare_except_safe(self):
        """安全修复裸露的except语句"""
        print("🔧 安全修复裸露的except语句...")
        
        fixes = 0
        for i, line in enumerate(self.lines):
            line_stripped = line.strip()
            
            # 只修复最简单的情况
            if line_stripped == 'except:':
                # 获取缩进
                indent = len(line) - len(line.lstrip())
                # 替换为具体异常
                self.lines[i] = ' ' * indent + 'except Exception as e:\n'
                fixes += 1
                self.fixes_applied += 1
                print(f"   修复第{i+1}行: except: -> except Exception as e:")
        
        print(f"✅ 安全修复了 {fixes} 个裸露的except语句")
    
    def validate_syntax(self):
        """验证语法"""
        print("🔍 验证修复后的语法...")
        
        try:
            import ast
            content = ''.join(self.lines)
            ast.parse(content)
            print("✅ 语法验证通过")
            return True
        except SyntaxError as e:
            print(f"❌ 语法错误: 第{e.lineno}行 - {e.msg}")
            return False
        except Exception as e:
            print(f"❌ 验证异常: {e}")
            return False
    
    def run_safe_fix(self):
        """运行安全修复"""
        print("🚀 开始安全Bug修复...")
        
        # 1. 备份
        if not self.create_backup():
            return False
        
        # 2. 加载
        if not self.load_code():
            return False
        
        # 3. 安全修复
        self.fix_duplicate_imports_safe()
        self.fix_bare_except_safe()
        
        # 4. 验证语法
        if not self.validate_syntax():
            print("❌ 语法验证失败，恢复备份")
            shutil.copy2(self.backup_path, self.file_path)
            return False
        
        # 5. 保存
        if not self.save_code():
            return False
        
        print(f"🎉 安全修复完成！共修复 {self.fixes_applied} 个问题")
        return True

def main():
    """主函数"""
    print("=" * 80)
    print("🔧 WMZC安全Bug修复工具")
    print("只修复最安全的问题：连续重复导入、裸露except")
    print("=" * 80)
    
    fixer = SafeBugFixer()
    
    try:
        success = fixer.run_safe_fix()
        
        if success:
            print("\n✅ 安全修复成功！")
            print("📋 修复内容:")
            print("   ✅ 删除了连续重复的导入语句")
            print("   ✅ 修复了裸露的except语句")
            print("\n💡 建议运行测试验证功能正常")
            return True
        else:
            print("\n❌ 修复失败")
            return False
            
    except Exception as e:
        print(f"\n💥 修复异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
