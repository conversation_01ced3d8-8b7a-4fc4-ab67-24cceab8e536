#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC精确Bug修复脚本
只修复真正的问题：重复导入、裸露except、过长代码行
"""

import re
import shutil
from datetime import datetime

class PreciseBugFixer:
    """精确Bug修复器"""
    
    def __init__(self, file_path: str = "WMZC.py"):
        self.file_path = file_path
        self.backup_path = f"WMZC_precise_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        self.lines = []
        self.fixes_applied = 0
        
    def create_backup(self):
        """创建备份"""
        try:
            shutil.copy2(self.file_path, self.backup_path)
            print(f"✅ 备份已创建: {self.backup_path}")
            return True
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    
    def load_code(self):
        """加载代码"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.lines = f.readlines()
            print(f"✅ 加载了 {len(self.lines)} 行代码")
            return True
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False
    
    def save_code(self):
        """保存代码"""
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                f.writelines(self.lines)
            print(f"✅ 保存完成，应用了 {self.fixes_applied} 个修复")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def fix_duplicate_imports(self):
        """修复重复导入 - 只删除真正的重复"""
        print("🔧 修复重复导入...")
        
        # 记录已见过的导入
        seen_imports = {}
        lines_to_remove = []
        
        for i, line in enumerate(self.lines):
            line_stripped = line.strip()
            
            # 只处理简单的import语句
            if line_stripped.startswith('import ') and ' as ' not in line_stripped:
                module = line_stripped.replace('import ', '').strip()
                
                # 跳过复杂的导入（如多个模块）
                if ',' in module:
                    continue
                    
                # 检查是否真正重复
                if module in seen_imports:
                    # 确认这是完全相同的导入
                    prev_line = self.lines[seen_imports[module]].strip()
                    if prev_line == line_stripped:
                        lines_to_remove.append(i)
                        print(f"   删除重复导入: {line_stripped}")
                else:
                    seen_imports[module] = i
        
        # 删除重复行
        for i in reversed(lines_to_remove):
            del self.lines[i]
            self.fixes_applied += 1
        
        print(f"✅ 修复了 {len(lines_to_remove)} 个重复导入")
    
    def fix_bare_except(self):
        """修复裸露的except语句"""
        print("🔧 修复裸露的except语句...")
        
        fixes = 0
        for i, line in enumerate(self.lines):
            line_stripped = line.strip()
            
            # 查找裸露的except
            if line_stripped == 'except:':
                # 获取缩进
                indent = len(line) - len(line.lstrip())
                # 替换为具体异常
                self.lines[i] = ' ' * indent + 'except Exception as e:\n'
                fixes += 1
                self.fixes_applied += 1
                print(f"   修复第{i+1}行: except: -> except Exception as e:")
        
        print(f"✅ 修复了 {fixes} 个裸露的except语句")
    
    def fix_long_lines(self):
        """修复过长的代码行 - 只处理最严重的"""
        print("🔧 修复过长的代码行...")
        
        fixes = 0
        for i, line in enumerate(self.lines):
            # 只处理超过150字符且包含逗号的行
            if len(line) > 150 and ',' in line and not line.strip().startswith('#'):
                # 简单的逗号分割
                if line.count(',') >= 3:  # 至少3个逗号才值得分割
                    # 获取缩进
                    indent = len(line) - len(line.lstrip())
                    
                    # 分割参数
                    parts = line.strip().split(',')
                    if len(parts) > 3:
                        # 重新组织为多行
                        new_line = parts[0] + ',\n'
                        for j, part in enumerate(parts[1:-1], 1):
                            new_line += ' ' * (indent + 4) + part.strip() + ',\n'
                        new_line += ' ' * (indent + 4) + parts[-1].strip() + '\n'
                        
                        self.lines[i] = new_line
                        fixes += 1
                        self.fixes_applied += 1
                        print(f"   修复第{i+1}行: 分割长行 ({len(line)} -> 多行)")
        
        print(f"✅ 修复了 {fixes} 个过长的代码行")
    
    def fix_empty_except_blocks(self):
        """修复空的except块"""
        print("🔧 修复空的except块...")
        
        fixes = 0
        i = 0
        while i < len(self.lines) - 1:
            line = self.lines[i].strip()
            next_line = self.lines[i + 1].strip() if i + 1 < len(self.lines) else ""
            
            # 查找except后面跟pass的情况
            if line.startswith('except') and next_line == 'pass':
                # 获取pass行的缩进
                indent = len(self.lines[i + 1]) - len(self.lines[i + 1].lstrip())
                # 替换pass为有意义的处理
                self.lines[i + 1] = ' ' * indent + 'log(f"异常被忽略: {e}", "WARNING")\n'
                fixes += 1
                self.fixes_applied += 1
                print(f"   修复第{i+2}行: pass -> log异常")
            
            i += 1
        
        print(f"✅ 修复了 {fixes} 个空的except块")
    
    def validate_syntax(self):
        """验证语法"""
        print("🔍 验证修复后的语法...")
        
        try:
            import ast
            content = ''.join(self.lines)
            ast.parse(content)
            print("✅ 语法验证通过")
            return True
        except SyntaxError as e:
            print(f"❌ 语法错误: 第{e.lineno}行 - {e.msg}")
            return False
        except Exception as e:
            print(f"❌ 验证异常: {e}")
            return False
    
    def run_precise_fix(self):
        """运行精确修复"""
        print("🚀 开始精确Bug修复...")
        
        # 1. 备份
        if not self.create_backup():
            return False
        
        # 2. 加载
        if not self.load_code():
            return False
        
        # 3. 精确修复
        self.fix_duplicate_imports()
        self.fix_bare_except()
        self.fix_empty_except_blocks()
        self.fix_long_lines()
        
        # 4. 验证语法
        if not self.validate_syntax():
            print("❌ 语法验证失败，恢复备份")
            shutil.copy2(self.backup_path, self.file_path)
            return False
        
        # 5. 保存
        if not self.save_code():
            return False
        
        print(f"🎉 精确修复完成！共修复 {self.fixes_applied} 个真实问题")
        return True

def main():
    """主函数"""
    print("=" * 80)
    print("🔧 WMZC精确Bug修复工具")
    print("只修复真正的问题：重复导入、裸露except、过长代码行")
    print("=" * 80)
    
    fixer = PreciseBugFixer()
    
    try:
        success = fixer.run_precise_fix()
        
        if success:
            print("\n✅ 精确修复成功！")
            print("📋 修复内容:")
            print("   ✅ 删除了真正重复的导入语句")
            print("   ✅ 修复了裸露的except语句")
            print("   ✅ 改进了空的except块")
            print("   ✅ 分割了过长的代码行")
            print("\n💡 建议运行测试验证功能正常")
            return True
        else:
            print("\n❌ 修复失败")
            return False
            
    except Exception as e:
        print(f"\n💥 修复异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
