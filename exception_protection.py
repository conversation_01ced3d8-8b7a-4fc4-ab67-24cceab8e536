#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ 系统异常保护脚本
为关键函数添加异常处理保护
"""

import traceback
import logging
from functools import wraps

def safe_execution(func):
    """安全执行装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logging.error(f"❌ 函数 {func.__name__} 执行失败: {e}")
            logging.error(f"📍 错误详情: {traceback.format_exc()}")
            return None
    return wrapper

def safe_api_call(func):
    """安全API调用装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if "403" in str(e) or "Forbidden" in str(e):
                logging.warning(f"⚠️ API认证失败: {e}")
                logging.info("💡 请检查API密钥配置和权限设置")
            else:
                logging.error(f"❌ API调用失败: {e}")
            return None
    return wrapper

def safe_indicator_calculation(func):
    """安全技术指标计算装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            # 确保返回值不是DataFrame，避免布尔值判断错误
            if hasattr(result, 'iloc'):
                return result.iloc[-1] if not result.empty else 0
            return result
        except Exception as e:
            logging.error(f"❌ 技术指标计算失败: {e}")
            return 0
    return wrapper

# 导出保护装饰器
__all__ = [
    'safe_execution',
    'safe_api_call', 
    'safe_indicator_calculation'
]
