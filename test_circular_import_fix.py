#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
循环导入和初始化顺序修复验证脚本
"""

def test_circular_import_fix():
    """测试循环导入修复"""
    print("🔍 开始验证循环导入修复...")
    
    try:
        # 测试1: 导入WMZC模块
        print("📦 测试1: 导入WMZC模块...")
        import WMZC
        print("✅ WMZC模块导入成功")
        
        # 测试2: 检查config_manager是否可用
        print("🔍 测试2: 检查config_manager...")
        if hasattr(WMZC, 'config_manager'):
            cm = WMZC.config_manager
            if cm is not None:
                print("✅ config_manager可用")
                
                # 测试配置管理器的方法
                if hasattr(cm, 'config'):
                    print("✅ config_manager有config属性")
                else:
                    print("⚠️ config_manager缺少config属性")
            else:
                print("❌ config_manager为None")
        else:
            print("❌ config_manager不存在")
            
        # 测试3: 检查unified_exchange是否可用
        print("🔍 测试3: 检查unified_exchange...")
        if hasattr(WMZC, 'unified_exchange'):
            ue = WMZC.unified_exchange
            if ue is not None:
                print("✅ unified_exchange可用")
                
                # 测试统一交易所管理器的方法
                if hasattr(ue, 'get_current_exchange_name'):
                    try:
                        exchange_name = ue.get_current_exchange_name()
                        print(f"✅ 当前交易所: {exchange_name}")
                    except Exception as e:
                        print(f"⚠️ 获取交易所名称失败: {e}")
                else:
                    print("⚠️ unified_exchange缺少get_current_exchange_name方法")
            else:
                print("❌ unified_exchange为None")
        else:
            print("❌ unified_exchange不存在")
            
        # 测试4: 检查banking_risk_monitor是否可用
        print("🔍 测试4: 检查banking_risk_monitor...")
        if hasattr(WMZC, 'banking_risk_monitor'):
            brm = WMZC.banking_risk_monitor
            if brm is not None:
                print("✅ banking_risk_monitor可用")
            else:
                print("⚠️ banking_risk_monitor为None")
        else:
            print("❌ banking_risk_monitor不存在")
            
        # 测试5: 检查config变量是否可用
        print("🔍 测试5: 检查config变量...")
        if hasattr(WMZC, 'config'):
            config = WMZC.config
            if isinstance(config, dict):
                print(f"✅ config可用，包含 {len(config)} 个配置项")
                
                # 检查一些关键配置
                key_configs = ['SYMBOL', 'EXCHANGE', 'TIMEFRAME']
                for key in key_configs:
                    if key in config:
                        print(f"  ✅ {key}: {config[key]}")
                    else:
                        print(f"  ⚠️ 缺少配置: {key}")
            else:
                print(f"❌ config类型错误: {type(config)}")
        else:
            print("❌ config变量不存在")
            
        # 测试6: 测试智能获取器函数
        print("🔍 测试6: 测试智能获取器函数...")
        if hasattr(WMZC, 'get_config_manager'):
            try:
                cm = WMZC.get_config_manager()
                print("✅ get_config_manager()函数工作正常")
            except Exception as e:
                print(f"❌ get_config_manager()失败: {e}")
        else:
            print("❌ get_config_manager函数不存在")
            
        if hasattr(WMZC, 'get_unified_exchange'):
            try:
                ue = WMZC.get_unified_exchange()
                print("✅ get_unified_exchange()函数工作正常")
            except Exception as e:
                print(f"❌ get_unified_exchange()失败: {e}")
        else:
            print("❌ get_unified_exchange函数不存在")
            
        print("🎉 循环导入修复验证完成！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_initialization_order():
    """测试初始化顺序"""
    print("\n🔍 测试初始化顺序...")
    
    try:
        import WMZC
        
        # 测试多次访问是否一致
        print("🔍 测试多次访问一致性...")
        
        cm1 = WMZC.get_config_manager()
        cm2 = WMZC.get_config_manager()
        
        if cm1 is cm2:
            print("✅ 配置管理器单例模式工作正常")
        else:
            print("⚠️ 配置管理器可能存在多实例问题")
            
        ue1 = WMZC.get_unified_exchange()
        ue2 = WMZC.get_unified_exchange()
        
        if ue1 is ue2:
            print("✅ 统一交易所管理器单例模式工作正常")
        else:
            print("⚠️ 统一交易所管理器可能存在多实例问题")
            
        return True
        
    except Exception as e:
        print(f"❌ 初始化顺序测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 循环导入和初始化顺序修复验证")
    print("=" * 60)
    
    # 测试循环导入修复
    success1 = test_circular_import_fix()
    
    # 测试初始化顺序
    success2 = test_initialization_order()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过！循环导入问题修复成功")
        print("✅ 修复效果:")
        print("   - 消除了循环导入问题")
        print("   - 实现了延迟初始化")
        print("   - 保持了向后兼容性")
        print("   - 提供了智能获取器")
        print("   - 确保了单例模式")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    print("=" * 60)

if __name__ == "__main__":
    main()
