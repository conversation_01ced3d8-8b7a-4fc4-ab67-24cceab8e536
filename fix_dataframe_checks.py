#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复WMZC中的DataFrame布尔值判断问题
"""

import re

def fix_dataframe_checks():
    """修复DataFrame检查问题"""
    print("🔧 修复WMZC中的DataFrame布尔值判断问题")
    
    # 读取文件
    with open('WMZC.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 记录修复次数
    fixes = 0
    
    # 修复模式1: not df.empty -> _safe_dataframe_check(df)
    pattern1 = r'not\s+(\w+)\.empty'
    def replace1(match):
        var_name = match.group(1)
        return f'_safe_dataframe_check({var_name})'
    
    new_content, count1 = re.subn(pattern1, replace1, content)
    fixes += count1
    print(f"✅ 修复 'not df.empty' 模式: {count1} 处")
    
    # 修复模式2: df.empty -> not _safe_dataframe_check(df)
    pattern2 = r'(\w+)\.empty(?!\s*and|\s*or)'
    def replace2(match):
        var_name = match.group(1)
        return f'not _safe_dataframe_check({var_name})'
    
    new_content, count2 = re.subn(pattern2, replace2, new_content)
    fixes += count2
    print(f"✅ 修复 'df.empty' 模式: {count2} 处")
    
    # 修复模式3: df is not None and not df.empty -> _safe_dataframe_check(df)
    pattern3 = r'(\w+)\s+is\s+not\s+None\s+and\s+not\s+\1\.empty'
    def replace3(match):
        var_name = match.group(1)
        return f'_safe_dataframe_check({var_name})'
    
    new_content, count3 = re.subn(pattern3, replace3, new_content)
    fixes += count3
    print(f"✅ 修复 'df is not None and not df.empty' 模式: {count3} 处")
    
    # 修复模式4: if df and not df.empty -> if _safe_dataframe_check(df)
    pattern4 = r'if\s+(\w+)\s+and\s+not\s+\1\.empty'
    def replace4(match):
        var_name = match.group(1)
        return f'if _safe_dataframe_check({var_name})'
    
    new_content, count4 = re.subn(pattern4, replace4, new_content)
    fixes += count4
    print(f"✅ 修复 'if df and not df.empty' 模式: {count4} 处")
    
    # 写回文件
    if fixes > 0:
        with open('WMZC.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"🎉 总共修复了 {fixes} 处DataFrame检查问题")
        return True
    else:
        print("✅ 没有发现需要修复的DataFrame检查问题")
        return False

def test_fixes():
    """测试修复效果"""
    print("\n🧪 测试修复效果...")
    
    try:
        import WMZC
        
        # 测试全局log函数
        if hasattr(WMZC, 'log'):
            WMZC.log("🧪 测试DataFrame修复后的日志", "INFO")
            print("✅ 全局log函数正常")
        
        # 测试_safe_dataframe_check函数
        if hasattr(WMZC, '_safe_dataframe_check'):
            import pandas as pd
            
            # 测试空DataFrame
            empty_df = pd.DataFrame()
            result1 = WMZC._safe_dataframe_check(empty_df)
            print(f"✅ 空DataFrame检查: {result1} (应该是False)")
            
            # 测试有数据的DataFrame
            data_df = pd.DataFrame({'test': [1, 2, 3]})
            result2 = WMZC._safe_dataframe_check(data_df)
            print(f"✅ 有数据DataFrame检查: {result2} (应该是True)")
            
            # 测试None
            result3 = WMZC._safe_dataframe_check(None)
            print(f"✅ None检查: {result3} (应该是False)")
            
        else:
            print("❌ 未找到_safe_dataframe_check函数")
        
        print("🎉 修复测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 WMZC DataFrame检查修复工具")
    print("=" * 50)
    
    try:
        # 1. 修复DataFrame检查
        success = fix_dataframe_checks()
        
        if success:
            print("\n✅ DataFrame检查修复完成")
            
            # 2. 测试修复效果
            test_success = test_fixes()
            
            if test_success:
                print("\n🎉 所有修复验证通过！")
                print("💡 现在WMZC系统应该不再出现DataFrame布尔值歧义错误")
                return True
            else:
                print("\n⚠️ 修复验证失败，可能需要手动检查")
                return False
        else:
            print("\n✅ 没有发现需要修复的问题")
            return True
            
    except Exception as e:
        print(f"\n❌ 修复过程异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
