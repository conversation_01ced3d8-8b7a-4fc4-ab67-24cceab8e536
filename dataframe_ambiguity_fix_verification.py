#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 DataFrame歧义性错误修复验证脚本
验证我们对WMZC系统中DataFrame布尔值判断错误的修复效果
"""

import pandas as pd
import numpy as np
import time
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

def test_dataframe_return_consistency():
    """测试指标计算函数返回值一致性"""
    print("🔍 测试指标计算函数返回值一致性...")
    
    try:
        # 导入修复后的函数
        from WMZC import calculate_macd, calculate_kdj, calculate_rsi
        
        # 测试1: 空输入
        print("  测试1: 空输入处理...")
        macd_result = calculate_macd(None)
        rsi_result = calculate_rsi(None)
        kdj_result = calculate_kdj(None)
        
        if isinstance(macd_result, pd.DataFrame) and macd_result.empty:
            print("    ✅ MACD空输入返回空DataFrame")
        else:
            print(f"    ❌ MACD空输入返回类型错误: {type(macd_result)}")
            
        if isinstance(rsi_result, pd.DataFrame) and rsi_result.empty:
            print("    ✅ RSI空输入返回空DataFrame")
        else:
            print(f"    ❌ RSI空输入返回类型错误: {type(rsi_result)}")
            
        if isinstance(kdj_result, pd.DataFrame) and kdj_result.empty:
            print("    ✅ KDJ空输入返回空DataFrame")
        else:
            print(f"    ❌ KDJ空输入返回类型错误: {type(kdj_result)}")
        
        # 测试2: 错误类型输入
        print("  测试2: 错误类型输入处理...")
        wrong_input = "not_a_dataframe"
        
        macd_result = calculate_macd(wrong_input)
        rsi_result = calculate_rsi(wrong_input)
        kdj_result = calculate_kdj(wrong_input)
        
        if isinstance(macd_result, pd.DataFrame) and macd_result.empty:
            print("    ✅ MACD错误类型输入返回空DataFrame")
        else:
            print(f"    ❌ MACD错误类型输入返回类型错误: {type(macd_result)}")
            
        if isinstance(rsi_result, pd.DataFrame) and rsi_result.empty:
            print("    ✅ RSI错误类型输入返回空DataFrame")
        else:
            print(f"    ❌ RSI错误类型输入返回类型错误: {type(rsi_result)}")
            
        if isinstance(kdj_result, pd.DataFrame) and kdj_result.empty:
            print("    ✅ KDJ错误类型输入返回空DataFrame")
        else:
            print(f"    ❌ KDJ错误类型输入返回类型错误: {type(kdj_result)}")
        
        # 测试3: 数据不足输入
        print("  测试3: 数据不足输入处理...")
        insufficient_data = pd.DataFrame({
            'open': [100, 101],
            'high': [105, 106],
            'low': [95, 96],
            'close': [103, 104],
            'volume': [1000, 1100]
        })
        
        macd_result = calculate_macd(insufficient_data)
        rsi_result = calculate_rsi(insufficient_data)
        kdj_result = calculate_kdj(insufficient_data)
        
        if isinstance(macd_result, pd.DataFrame) and macd_result.empty:
            print("    ✅ MACD数据不足返回空DataFrame")
        else:
            print(f"    ❌ MACD数据不足返回类型错误: {type(macd_result)}")
            
        if isinstance(rsi_result, pd.DataFrame) and rsi_result.empty:
            print("    ✅ RSI数据不足返回空DataFrame")
        else:
            print(f"    ❌ RSI数据不足返回类型错误: {type(rsi_result)}")
            
        if isinstance(kdj_result, pd.DataFrame) and kdj_result.empty:
            print("    ✅ KDJ数据不足返回空DataFrame")
        else:
            print(f"    ❌ KDJ数据不足返回类型错误: {type(kdj_result)}")
        
        print("  ✅ 返回值一致性测试完成")
        
    except Exception as e:
        print(f"  ❌ 返回值一致性测试失败: {e}")

def test_signal_functions():
    """测试信号获取函数的DataFrame处理"""
    print("🔍 测试信号获取函数...")
    
    try:
        # 导入修复后的函数
        from WMZC import UnifiedExchangeManager
        
        # 创建测试实例
        manager = UnifiedExchangeManager()
        
        # 创建测试K线数据
        test_kline = []
        for i in range(50):
            test_kline.append([
                int(time.time() * 1000) + i * 60000,  # timestamp
                100 + i * 0.1,  # open
                105 + i * 0.1,  # high
                95 + i * 0.1,   # low
                103 + i * 0.1,  # close
                1000 + i * 10   # volume
            ])
        
        print("  测试MACD信号获取...")
        macd_signal = manager._get_macd_signal(test_kline)
        if macd_signal is None or isinstance(macd_signal, dict):
            print("    ✅ MACD信号获取正常")
        else:
            print(f"    ❌ MACD信号获取异常: {type(macd_signal)}")
        
        print("  测试RSI信号获取...")
        rsi_signal = manager._get_rsi_signal(test_kline)
        if rsi_signal is None or isinstance(rsi_signal, dict):
            print("    ✅ RSI信号获取正常")
        else:
            print(f"    ❌ RSI信号获取异常: {type(rsi_signal)}")
        
        print("  测试KDJ信号获取...")
        kdj_signal = manager._get_kdj_signal(test_kline)
        if kdj_signal is None or isinstance(kdj_signal, dict):
            print("    ✅ KDJ信号获取正常")
        else:
            print(f"    ❌ KDJ信号获取异常: {type(kdj_signal)}")
        
        print("  ✅ 信号获取函数测试完成")
        
    except Exception as e:
        print(f"  ❌ 信号获取函数测试失败: {e}")

def test_performance_improvement():
    """测试性能改进效果"""
    print("🔍 测试性能改进效果...")
    
    try:
        from WMZC import calculate_macd, calculate_rsi, calculate_kdj
        
        # 创建大量测试数据
        large_data = pd.DataFrame({
            'open': np.random.uniform(100, 110, 200),
            'high': np.random.uniform(110, 120, 200),
            'low': np.random.uniform(90, 100, 200),
            'close': np.random.uniform(100, 110, 200),
            'volume': np.random.uniform(1000, 2000, 200)
        })
        
        # 测试MACD性能
        start_time = time.time()
        for i in range(5):
            macd_result = calculate_macd(large_data)
        macd_avg_time = (time.time() - start_time) / 5
        
        print(f"  MACD平均执行时间: {macd_avg_time*1000:.1f}ms")
        
        # 测试RSI性能
        start_time = time.time()
        for i in range(5):
            rsi_result = calculate_rsi(large_data)
        rsi_avg_time = (time.time() - start_time) / 5
        
        print(f"  RSI平均执行时间: {rsi_avg_time*1000:.1f}ms")
        
        # 测试KDJ性能
        start_time = time.time()
        for i in range(5):
            kdj_result = calculate_kdj(large_data)
        kdj_avg_time = (time.time() - start_time) / 5
        
        print(f"  KDJ平均执行时间: {kdj_avg_time*1000:.1f}ms")
        
        # 性能评估
        if macd_avg_time < 0.1 and rsi_avg_time < 0.1 and kdj_avg_time < 0.1:
            print("  ✅ 性能表现良好 (所有指标 < 100ms)")
        else:
            print("  ⚠️ 性能需要进一步优化")
        
        print("  ✅ 性能测试完成")
        
    except Exception as e:
        print(f"  ❌ 性能测试失败: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("🔧 DataFrame歧义性错误修复验证")
    print("=" * 60)
    
    # 运行所有测试
    test_dataframe_return_consistency()
    print()
    test_signal_functions()
    print()
    test_performance_improvement()
    
    print("=" * 60)
    print("✅ 验证完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
