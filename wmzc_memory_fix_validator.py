#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
✅ WMZC内存修复验证工具
验证所有内存泄漏修复的有效性和安全性
"""

import os
import gc
import sys
import time
import psutil
import threading
import traceback
from datetime import datetime

class WMZCMemoryFixValidator:
    """WMZC内存修复验证器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.baseline_memory = self.process.memory_info().rss
        self.validation_results = {}
        
        print(f"✅ WMZC内存修复验证器初始化")
        print(f"📊 基线内存: {self.baseline_memory / 1024 / 1024:.1f} MB")
    
    def run_comprehensive_validation(self):
        """运行全面验证"""
        print(f"\n🔍 开始全面内存修复验证...")
        
        # 1. 验证内存泄漏检测器修复
        self._validate_memory_leak_detector()
        
        # 2. 验证信号日志修复
        self._validate_signal_log_fix()
        
        # 3. 验证交易记录修复
        self._validate_trading_records_fix()
        
        # 4. 验证缓存系统修复
        self._validate_cache_system_fix()
        
        # 5. 验证垃圾回收优化
        self._validate_garbage_collection()
        
        # 6. 进行内存压力测试
        self._perform_memory_stress_test()
        
        # 7. 生成验证报告
        return self._generate_validation_report()
    
    def _validate_memory_leak_detector(self):
        """验证内存泄漏检测器修复"""
        print(f"\n🔍 验证内存泄漏检测器修复...")
        
        try:
            import WMZC
            
            if hasattr(WMZC, 'memory_leak_detector'):
                detector = WMZC.memory_leak_detector
                
                # 检查修复项目
                checks = {
                    'deque_snapshots': hasattr(detector, 'memory_snapshots') and 
                                     hasattr(detector.memory_snapshots, 'maxlen'),
                    'cleanup_counter': hasattr(detector, '_cleanup_counter'),
                    'preventive_cleanup': hasattr(detector, '_perform_preventive_cleanup'),
                    'warning_threshold': hasattr(detector, 'warning_threshold'),
                    'critical_threshold': detector.leak_threshold == 336 * 1024 * 1024
                }
                
                passed_checks = sum(checks.values())
                total_checks = len(checks)
                
                self.validation_results['memory_leak_detector'] = {
                    'status': 'PASS' if passed_checks == total_checks else 'PARTIAL',
                    'passed_checks': passed_checks,
                    'total_checks': total_checks,
                    'details': checks
                }
                
                print(f"  ✅ 内存泄漏检测器: {passed_checks}/{total_checks} 项修复验证通过")
                
                # 测试预防性清理
                if hasattr(detector, '_perform_preventive_cleanup'):
                    try:
                        detector._perform_preventive_cleanup()
                        print(f"  ✅ 预防性清理功能正常")
                    except Exception as e:
                        print(f"  ⚠️ 预防性清理测试失败: {e}")
                
            else:
                self.validation_results['memory_leak_detector'] = {
                    'status': 'FAIL',
                    'reason': 'memory_leak_detector not found'
                }
                print(f"  ❌ 未找到内存泄漏检测器")
                
        except ImportError:
            self.validation_results['memory_leak_detector'] = {
                'status': 'FAIL',
                'reason': 'Cannot import WMZC'
            }
            print(f"  ❌ 无法导入WMZC模块")
    
    def _validate_signal_log_fix(self):
        """验证信号日志修复"""
        print(f"\n📝 验证信号日志修复...")
        
        try:
            import WMZC
            
            # 检查全局signal_log
            if 'signal_log' in globals():
                signal_log = globals()['signal_log']
                
                # 测试大量添加
                initial_size = len(signal_log)
                
                # 模拟添加300条记录
                for i in range(300):
                    test_entry = {
                        'timestamp': time.time(),
                        'signal_type': 'test',
                        'value': i,
                        'source': 'validation_test'
                    }
                    signal_log.append(test_entry)
                    
                    # 检查是否触发清理
                    if len(signal_log) > 250:  # 新的限制
                        break
                
                final_size = len(signal_log)
                
                # 验证大小限制
                size_limited = final_size <= 250
                
                self.validation_results['signal_log'] = {
                    'status': 'PASS' if size_limited else 'FAIL',
                    'initial_size': initial_size,
                    'final_size': final_size,
                    'size_limited': size_limited,
                    'max_allowed': 250
                }
                
                print(f"  ✅ signal_log大小限制: {final_size} <= 250")
                
            else:
                self.validation_results['signal_log'] = {
                    'status': 'FAIL',
                    'reason': 'signal_log not found in globals'
                }
                print(f"  ❌ 未找到全局signal_log")
                
        except Exception as e:
            self.validation_results['signal_log'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"  ❌ signal_log验证失败: {e}")
    
    def _validate_trading_records_fix(self):
        """验证交易记录修复"""
        print(f"\n📊 验证交易记录修复...")
        
        try:
            import WMZC
            
            # 检查全局trading_records
            if 'trading_records' in globals():
                trading_records = globals()['trading_records']
                
                # 测试大量添加
                initial_size = len(trading_records)
                
                # 模拟添加250条记录
                for i in range(250):
                    test_record = {
                        'timestamp': time.time(),
                        'type': 'test_trade',
                        'amount': i,
                        'price': 100000 + i
                    }
                    trading_records.append(test_record)
                    
                    # 检查是否触发清理
                    if len(trading_records) > 200:  # 新的限制
                        break
                
                final_size = len(trading_records)
                
                # 验证大小限制
                size_limited = final_size <= 200
                
                self.validation_results['trading_records'] = {
                    'status': 'PASS' if size_limited else 'FAIL',
                    'initial_size': initial_size,
                    'final_size': final_size,
                    'size_limited': size_limited,
                    'max_allowed': 200
                }
                
                print(f"  ✅ trading_records大小限制: {final_size} <= 200")
                
            else:
                self.validation_results['trading_records'] = {
                    'status': 'FAIL',
                    'reason': 'trading_records not found in globals'
                }
                print(f"  ❌ 未找到全局trading_records")
                
        except Exception as e:
            self.validation_results['trading_records'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"  ❌ trading_records验证失败: {e}")
    
    def _validate_cache_system_fix(self):
        """验证缓存系统修复"""
        print(f"\n💾 验证缓存系统修复...")
        
        try:
            import WMZC
            
            cache_objects = [
                'smart_cache_manager',
                'indicator_cache',
                'kline_cache',
                'kdj_cache',
                'macd_cache',
                'rsi_cache'
            ]
            
            cache_status = {}
            
            for cache_name in cache_objects:
                if hasattr(WMZC, cache_name):
                    cache_obj = getattr(WMZC, cache_name)
                    
                    # 检查清理功能
                    has_clear = hasattr(cache_obj, 'clear')
                    has_len = hasattr(cache_obj, '__len__')
                    
                    cache_status[cache_name] = {
                        'exists': True,
                        'has_clear': has_clear,
                        'has_len': has_len,
                        'current_size': len(cache_obj) if has_len else 'unknown'
                    }
                    
                    print(f"  ✅ {cache_name}: 存在, 清理功能: {has_clear}")
                else:
                    cache_status[cache_name] = {
                        'exists': False
                    }
                    print(f"  ⚠️ {cache_name}: 不存在")
            
            self.validation_results['cache_system'] = {
                'status': 'PASS',
                'cache_status': cache_status
            }
            
        except Exception as e:
            self.validation_results['cache_system'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"  ❌ 缓存系统验证失败: {e}")
    
    def _validate_garbage_collection(self):
        """验证垃圾回收优化"""
        print(f"\n🗑️ 验证垃圾回收优化...")
        
        try:
            # 测试垃圾回收效果
            before_objects = len(gc.get_objects())
            
            # 创建一些临时对象
            temp_objects = []
            for i in range(1000):
                temp_objects.append({'data': f'test_{i}', 'value': i * 100})
            
            # 删除引用
            del temp_objects
            
            # 执行垃圾回收
            collected = gc.collect()
            after_objects = len(gc.get_objects())
            
            self.validation_results['garbage_collection'] = {
                'status': 'PASS',
                'before_objects': before_objects,
                'after_objects': after_objects,
                'collected_objects': collected,
                'gc_working': collected > 0
            }
            
            print(f"  ✅ 垃圾回收: {collected} 个对象被回收")
            print(f"  📊 对象数量: {before_objects} -> {after_objects}")
            
        except Exception as e:
            self.validation_results['garbage_collection'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"  ❌ 垃圾回收验证失败: {e}")
    
    def _perform_memory_stress_test(self):
        """执行内存压力测试"""
        print(f"\n🔥 执行内存压力测试...")
        
        try:
            start_memory = self.process.memory_info().rss
            max_memory = start_memory
            
            # 模拟内存密集操作
            test_data = []
            
            for i in range(100):
                # 创建一些数据
                data_chunk = [{'id': j, 'data': f'test_data_{j}' * 100} for j in range(100)]
                test_data.append(data_chunk)
                
                # 检查内存使用
                current_memory = self.process.memory_info().rss
                if current_memory > max_memory:
                    max_memory = current_memory
                
                # 模拟清理
                if i % 10 == 0:
                    test_data = test_data[-5:]  # 只保留最近5个块
                    gc.collect()
            
            # 最终清理
            del test_data
            gc.collect()
            
            end_memory = self.process.memory_info().rss
            
            memory_growth = max_memory - start_memory
            memory_recovered = max_memory - end_memory
            
            self.validation_results['stress_test'] = {
                'status': 'PASS',
                'start_memory_mb': start_memory / 1024 / 1024,
                'max_memory_mb': max_memory / 1024 / 1024,
                'end_memory_mb': end_memory / 1024 / 1024,
                'max_growth_mb': memory_growth / 1024 / 1024,
                'recovered_mb': memory_recovered / 1024 / 1024,
                'recovery_rate': memory_recovered / memory_growth if memory_growth > 0 else 1.0
            }
            
            print(f"  ✅ 压力测试完成")
            print(f"  📊 最大内存增长: {memory_growth / 1024 / 1024:.1f} MB")
            print(f"  🔄 内存回收: {memory_recovered / 1024 / 1024:.1f} MB")
            print(f"  📈 回收率: {memory_recovered / memory_growth * 100 if memory_growth > 0 else 100:.1f}%")
            
        except Exception as e:
            self.validation_results['stress_test'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"  ❌ 内存压力测试失败: {e}")
    
    def _generate_validation_report(self):
        """生成验证报告"""
        current_memory = self.process.memory_info().rss
        memory_growth = current_memory - self.baseline_memory
        
        print(f"\n📋 WMZC内存修复验证报告")
        print(f"=" * 60)
        
        # 总体状态
        total_tests = len(self.validation_results)
        passed_tests = sum(1 for result in self.validation_results.values() 
                          if result.get('status') == 'PASS')
        
        print(f"📊 验证总结:")
        print(f"  总测试项: {total_tests}")
        print(f"  通过测试: {passed_tests}")
        print(f"  通过率: {passed_tests / total_tests * 100:.1f}%")
        
        print(f"\n📈 内存状态:")
        print(f"  基线内存: {self.baseline_memory / 1024 / 1024:.1f} MB")
        print(f"  当前内存: {current_memory / 1024 / 1024:.1f} MB")
        print(f"  内存增长: {memory_growth / 1024 / 1024:.1f} MB")
        
        # 336MB阈值检查
        threshold_336mb = 336 * 1024 * 1024
        if memory_growth < threshold_336mb:
            print(f"  ✅ 内存增长低于336MB阈值 ({(threshold_336mb - memory_growth) / 1024 / 1024:.1f} MB余量)")
        else:
            print(f"  🚨 内存增长超过336MB阈值 (超出 {(memory_growth - threshold_336mb) / 1024 / 1024:.1f} MB)")
        
        print(f"\n🔍 详细结果:")
        for test_name, result in self.validation_results.items():
            status_icon = "✅" if result['status'] == 'PASS' else "⚠️" if result['status'] == 'PARTIAL' else "❌"
            print(f"  {status_icon} {test_name}: {result['status']}")
        
        # 生成建议
        print(f"\n💡 建议:")
        if passed_tests == total_tests:
            print(f"  🎉 所有内存修复验证通过，系统内存管理已优化")
        elif passed_tests >= total_tests * 0.8:
            print(f"  ✅ 大部分修复验证通过，系统内存管理显著改善")
        else:
            print(f"  ⚠️ 部分修复需要进一步优化")
        
        if memory_growth < 200 * 1024 * 1024:
            print(f"  ✅ 内存使用控制良好，可以长期稳定运行")
        elif memory_growth < threshold_336mb:
            print(f"  ⚠️ 内存使用需要监控，建议启用内存守护者")
        else:
            print(f"  🚨 内存使用过高，建议立即重启系统")
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'pass_rate': passed_tests / total_tests,
            'memory_growth_mb': memory_growth / 1024 / 1024,
            'under_336mb_threshold': memory_growth < threshold_336mb,
            'validation_results': self.validation_results
        }

def main():
    """主函数"""
    print(f"✅ WMZC内存修复验证工具")
    print(f"=" * 60)
    
    validator = WMZCMemoryFixValidator()
    
    try:
        # 运行全面验证
        report = validator.run_comprehensive_validation()
        
        # 保存验证报告
        import json
        with open('wmzc_memory_fix_validation_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 验证报告已保存: wmzc_memory_fix_validation_report.json")
        
        return report['pass_rate'] >= 0.8  # 80%通过率认为成功
        
    except Exception as e:
        print(f"❌ 验证过程异常: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
