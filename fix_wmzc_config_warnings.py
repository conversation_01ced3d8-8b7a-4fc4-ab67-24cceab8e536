#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 修复WMZC配置警告问题
清理C:\Users\<USER>\.wmzc_trading目录中的测试API配置
"""

import os
import json
import shutil
from datetime import datetime

def fix_wmzc_config_warnings():
    """修复WMZC配置警告"""
    print("🔧 修复WMZC配置警告问题")
    print("=" * 60)
    
    # WMZC配置目录
    wmzc_config_dir = r'C:\Users\<USER>\.wmzc_trading'
    
    # 检查目录是否存在
    if not os.path.exists(wmzc_config_dir):
        print(f"📁 创建WMZC配置目录: {wmzc_config_dir}")
        try:
            os.makedirs(wmzc_config_dir, exist_ok=True)
        except Exception as e:
            print(f"❌ 创建目录失败: {e}")
            return False
    
    # 需要清理的配置文件
    config_files = [
        'wmzc_config.json',
        'trading_config.json', 
        'user_settings.json',
        'misc_optimization_config.json',
        'ai_config.json'
    ]
    
    # 干净的配置模板
    clean_config_template = {
        "API_KEY": "",
        "API_SECRET": "",
        "PASSPHRASE": "",
        "OKX_API_KEY": "",
        "OKX_SECRET_KEY": "",
        "OKX_PASSPHRASE": "",
        "EXCHANGE": "OKX",
        "SYMBOL": "BTC-USDT-SWAP",
        "TIMEFRAME": "1m",
        "ORDER_USDT_AMOUNT": 10,
        "LEVERAGE": 3,
        "RISK_PERCENT": 1.0,
        "ENABLE_KDJ": True,
        "ENABLE_MACD": True,
        "ENABLE_PINBAR": True,
        "ENABLE_RSI": True,
        "LOG_LEVEL": "INFO",
        "TEST_MODE": True,
        "ENABLE_TRADING": False,
        "_CONFIG_CLEANED": datetime.now().isoformat(),
        "_CONFIG_PROTECTED": True,
        "_NO_TEST_API": True
    }
    
    # WMZC特定配置模板
    wmzc_specific_template = {
        "exchange_selection": "OKX",
        "okx_api_key": "",
        "okx_secret_key": "",
        "okx_passphrase": "",
        "default_symbol": "BTC-USDT-SWAP",
        "default_timeframe": "1m",
        "_CONFIG_CLEANED": datetime.now().isoformat(),
        "_CONFIG_PROTECTED": True,
        "_NO_TEST_API": True
    }
    
    cleaned_count = 0
    
    print(f"\n🧹 清理配置文件...")
    
    for config_file in config_files:
        file_path = os.path.join(wmzc_config_dir, config_file)
        
        print(f"\n📄 处理: {config_file}")
        
        # 备份现有文件（如果存在）
        if os.path.exists(file_path):
            backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            try:
                shutil.copy2(file_path, backup_path)
                print(f"  💾 已备份到: {os.path.basename(backup_path)}")
            except Exception as e:
                print(f"  ⚠️ 备份失败: {e}")
        
        # 选择合适的配置模板
        if config_file == 'wmzc_config.json':
            config_template = wmzc_specific_template
        else:
            config_template = clean_config_template
        
        # 创建干净的配置文件
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_template, f, indent=2, ensure_ascii=False)
            print(f"  ✅ 已创建干净的配置文件")
            cleaned_count += 1
        except Exception as e:
            print(f"  ❌ 创建失败: {e}")
    
    print(f"\n📊 清理结果:")
    print(f"  ✅ 成功清理了 {cleaned_count} 个配置文件")
    print(f"  📁 配置目录: {wmzc_config_dir}")
    
    # 验证清理结果
    print(f"\n🔍 验证清理结果...")
    
    for config_file in config_files:
        file_path = os.path.join(wmzc_config_dir, config_file)
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查是否还有测试API
                test_api_found = False
                test_patterns = [
                    'da636867-490f-4e3e-81b2-870841afb860',
                    'C15B6EE0CF3FFDEE5834865D3839325E',
                    'Mx123456@'
                ]
                
                for key, value in config.items():
                    if isinstance(value, str):
                        for pattern in test_patterns:
                            if pattern in value:
                                test_api_found = True
                                break
                
                if test_api_found:
                    print(f"  ⚠️ {config_file}: 仍包含测试API")
                else:
                    print(f"  ✅ {config_file}: 已清理干净")
                    
            except Exception as e:
                print(f"  ❌ {config_file}: 验证失败 {e}")
    
    return cleaned_count > 0

def create_config_verification_script():
    """创建配置验证脚本"""
    print(f"\n🔍 创建配置验证脚本...")
    
    verification_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC配置验证脚本
验证所有配置文件是否干净，无测试API密钥
"""

import os
import json

def verify_all_configs():
    """验证所有配置"""
    print("🔍 验证WMZC配置文件...")
    
    # 配置目录和文件
    config_locations = [
        {
            'dir': '.',
            'files': ['trading_config.json', 'wmzc_config.json']
        },
        {
            'dir': r'C:\\Users\\<USER>\\.wmzc_trading',
            'files': ['wmzc_config.json', 'trading_config.json', 'user_settings.json', 
                     'misc_optimization_config.json', 'ai_config.json']
        }
    ]
    
    test_patterns = [
        'da636867-490f-4e3e-81b2-870841afb860',
        'C15B6EE0CF3FFDEE5834865D3839325E', 
        'Mx123456@',
        'test_final_api_key',
        'test_api_key'
    ]
    
    total_files = 0
    clean_files = 0
    issues_found = []
    
    for location in config_locations:
        config_dir = location['dir']
        if os.path.exists(config_dir):
            print(f"\\n📁 检查目录: {config_dir}")
            
            for config_file in location['files']:
                file_path = os.path.join(config_dir, config_file)
                if os.path.exists(file_path):
                    total_files += 1
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        
                        # 检查测试API
                        has_test_api = False
                        for key, value in config.items():
                            if isinstance(value, str):
                                for pattern in test_patterns:
                                    if pattern in value:
                                        has_test_api = True
                                        issues_found.append(f"{config_file}: {key} 包含测试值")
                                        break
                        
                        if has_test_api:
                            print(f"  ❌ {config_file}: 包含测试API")
                        else:
                            print(f"  ✅ {config_file}: 配置干净")
                            clean_files += 1
                            
                    except Exception as e:
                        print(f"  ❌ {config_file}: 读取失败 {e}")
                        issues_found.append(f"{config_file}: 读取失败")
    
    print(f"\\n📊 验证结果:")
    print(f"  总文件数: {total_files}")
    print(f"  干净文件: {clean_files}")
    print(f"  问题文件: {total_files - clean_files}")
    
    if issues_found:
        print(f"\\n⚠️ 发现的问题:")
        for issue in issues_found:
            print(f"    • {issue}")
        return False
    else:
        print(f"\\n✅ 所有配置文件都是干净的！")
        return True

if __name__ == "__main__":
    verify_all_configs()
'''
    
    try:
        with open('verify_wmzc_configs.py', 'w', encoding='utf-8') as f:
            f.write(verification_script)
        print("  ✅ 配置验证脚本已创建: verify_wmzc_configs.py")
        return True
    except Exception as e:
        print(f"  ❌ 创建验证脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 WMZC配置警告修复工具")
    print("=" * 60)
    
    try:
        # 1. 修复配置警告
        success = fix_wmzc_config_warnings()
        
        # 2. 创建验证脚本
        verification_created = create_config_verification_script()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 WMZC配置警告修复完成！")
            print("\n💡 修复内容:")
            print("  1. ✅ 清理了所有测试API密钥")
            print("  2. ✅ 创建了干净的配置文件")
            print("  3. ✅ 备份了原始配置")
            print("  4. ✅ 创建了验证脚本")
            
            print("\n🚀 下一步操作:")
            print("  1. 重新启动WMZC系统")
            print("  2. 配置警告应该消失")
            print("  3. 在主配置页面填写真实API密钥")
            print("  4. 运行 python verify_wmzc_configs.py 验证配置")
            
            print("\n⚠️ 重要提醒:")
            print("  • 现在所有API字段都是空的")
            print("  • 请填写您的真实OKX API密钥")
            print("  • 系统将不再显示测试API警告")
            
        else:
            print("❌ WMZC配置警告修复失败")
            print("💡 请检查权限和路径设置")
        
        return success
        
    except Exception as e:
        print(f"❌ 修复过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
