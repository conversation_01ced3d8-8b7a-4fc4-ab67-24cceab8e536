#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🆓 WMZC免费API管理器 - 适配现有系统
与现有WMZC AI集成系统完美兼容的免费API源管理
"""

import asyncio
import aiohttp
import json
import time
import random
from datetime import datetime
from typing import Dict, List, Optional, Any

# 兼容WMZC日志系统
def log(message, level="INFO", context=None, error_details=None):
    """兼容WMZC.py的智能日志函数"""
    try:
        if 'log_manager' in globals() and log_manager is not None:
            return log_manager.log(message, level, context, error_details)
    except:
        pass
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [FreeAPI-{level}] {message}")

class WMZCFreeAPIManager:
    """WMZC免费API管理器 - 与现有系统兼容"""
    
    def __init__(self, wmzc_app):
        self.wmzc = wmzc_app
        self.free_sources = []
        self.current_source_index = 0
        self.usage_stats = {}
        
        # 从配置加载免费API源
        self.load_free_sources()
        
        # 初始化使用统计
        self.init_usage_stats()
        
        log(f"🆓 免费API管理器初始化完成，加载了 {len(self.free_sources)} 个免费源", "INFO")
    
    def load_free_sources(self):
        """从WMZC配置加载免费API源"""
        try:
            config = None

            # 尝试多种方式获取配置
            if hasattr(self.wmzc, 'config_manager') and self.wmzc.config_manager:
                if hasattr(self.wmzc.config_manager, 'config'):
                    config = self.wmzc.config_manager.config
                elif hasattr(self.wmzc.config_manager, 'get_config'):
                    config = self.wmzc.config_manager.get_config()

            # 如果从WMZC获取失败，直接从文件加载
            if not config:
                try:
                    with open('wmzc_config.json', 'r', encoding='utf-8') as f:
                        config = json.load(f)
                except FileNotFoundError:
                    log("配置文件不存在，使用默认配置", "WARNING")
                    config = {}

            self.free_sources = config.get('free_api_sources', [])

            # 只保留启用的源
            self.free_sources = [source for source in self.free_sources if source.get('enabled', True)]

            # 按优先级排序
            self.free_sources.sort(key=lambda x: x.get('priority', 999))

            log(f"✅ 成功加载 {len(self.free_sources)} 个免费API源", "INFO")

        except Exception as e:
            log(f"❌ 加载免费API源失败: {e}", "ERROR")
            self.free_sources = []
    
    def init_usage_stats(self):
        """初始化使用统计"""
        for source in self.free_sources:
            source_name = source['name']
            self.usage_stats[source_name] = {
                'calls_today': 0,
                'quota_used': 0.0,
                'last_success': None,
                'consecutive_failures': 0,
                'avg_response_time': 0.0,
                'success_rate': 100.0
            }
    
    async def make_free_api_call(self, prompt: str, **kwargs) -> Optional[Dict]:
        """使用免费API进行调用"""
        
        # 尝试所有可用的免费源
        for attempt in range(len(self.free_sources)):
            source = self.get_next_available_source()
            if not source:
                break
            
            try:
                start_time = time.time()
                response = await self.call_single_source(source, prompt, **kwargs)
                response_time = time.time() - start_time
                
                # 更新成功统计
                self.update_success_stats(source['name'], response_time)
                
                log(f"✅ 免费API调用成功: {source['name']} (响应时间: {response_time:.2f}s)", "DEBUG")
                return response
                
            except Exception as e:
                self.update_failure_stats(source['name'], str(e))
                log(f"❌ 免费API源 {source['name']} 失败: {e}", "WARNING")
                continue
        
        log("❌ 所有免费API源都不可用", "ERROR")
        return None
    
    def get_next_available_source(self) -> Optional[Dict]:
        """获取下一个可用的免费源"""
        
        for _ in range(len(self.free_sources)):
            source = self.free_sources[self.current_source_index]
            self.current_source_index = (self.current_source_index + 1) % len(self.free_sources)
            
            if self.is_source_available(source):
                return source
        
        return None
    
    def is_source_available(self, source: Dict) -> bool:
        """检查源是否可用"""
        
        source_name = source['name']
        stats = self.usage_stats.get(source_name, {})
        
        # 检查连续失败次数
        if stats.get('consecutive_failures', 0) >= 3:
            return False
        
        # 检查日调用限制
        if 'daily_calls_limit' in source:
            if stats.get('calls_today', 0) >= source['daily_calls_limit']:
                return False
        
        # 检查月调用限制（简化版本）
        if 'monthly_calls_limit' in source:
            if stats.get('calls_today', 0) * 30 >= source['monthly_calls_limit']:
                return False
        
        # 检查免费配额
        if 'free_quota' in source:
            if stats.get('quota_used', 0) >= source['free_quota']:
                return False
        
        return True
    
    async def call_single_source(self, source: Dict, prompt: str, **kwargs) -> Dict:
        """调用单个免费API源"""
        
        # 构建请求头
        headers = {
            'Content-Type': 'application/json'
        }
        
        # 根据不同API源设置认证
        if source['name'].startswith('openrouter'):
            headers['Authorization'] = f"Bearer {source['api_key']}"
            if 'headers' in source:
                headers.update(source['headers'])
        elif source['name'].startswith('anthropic'):
            headers['x-api-key'] = source['api_key']
            headers['anthropic-version'] = '2023-06-01'
        elif source['name'].startswith('huggingface'):
            headers['Authorization'] = f"Bearer {source['api_key']}"
        else:
            headers['Authorization'] = f"Bearer {source['api_key']}"
        
        # 构建请求数据
        if source['name'].startswith('anthropic'):
            data = {
                'model': source['model'],
                'max_tokens': kwargs.get('max_tokens', 1000),
                'messages': [{'role': 'user', 'content': prompt}]
            }
            endpoint = f"{source['base_url']}/messages"
        else:
            data = {
                'model': source['model'],
                'messages': [{'role': 'user', 'content': prompt}],
                'temperature': kwargs.get('temperature', 0.3),
                'max_tokens': kwargs.get('max_tokens', 1000)
            }
            endpoint = f"{source['base_url']}/chat/completions"
        
        # 发送请求
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(endpoint, headers=headers, json=data) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"API调用失败: {response.status} - {error_text}")
    
    def update_success_stats(self, source_name: str, response_time: float):
        """更新成功统计"""
        
        stats = self.usage_stats[source_name]
        stats['calls_today'] += 1
        stats['last_success'] = time.time()
        stats['consecutive_failures'] = 0
        
        # 更新平均响应时间
        if stats['avg_response_time'] == 0:
            stats['avg_response_time'] = response_time
        else:
            stats['avg_response_time'] = (stats['avg_response_time'] + response_time) / 2
        
        # 更新成功率
        total_calls = stats['calls_today']
        if total_calls > 1:
            current_successes = (stats['success_rate'] / 100) * (total_calls - 1) + 1
            stats['success_rate'] = (current_successes / total_calls) * 100
    
    def update_failure_stats(self, source_name: str, error: str):
        """更新失败统计"""
        
        stats = self.usage_stats[source_name]
        stats['calls_today'] += 1
        stats['consecutive_failures'] += 1
        
        # 更新成功率
        total_calls = stats['calls_today']
        if total_calls > 1:
            current_successes = (stats['success_rate'] / 100) * (total_calls - 1)
            stats['success_rate'] = (current_successes / total_calls) * 100
    
    def get_free_api_status(self) -> Dict:
        """获取免费API状态"""
        
        total_sources = len(self.free_sources)
        available_sources = sum(1 for source in self.free_sources if self.is_source_available(source))
        
        total_calls = sum(stats['calls_today'] for stats in self.usage_stats.values())
        avg_success_rate = sum(stats['success_rate'] for stats in self.usage_stats.values()) / max(len(self.usage_stats), 1)
        
        return {
            'timestamp': time.time(),
            'total_sources': total_sources,
            'available_sources': available_sources,
            'availability_rate': (available_sources / max(total_sources, 1)) * 100,
            'total_calls_today': total_calls,
            'avg_success_rate': avg_success_rate,
            'source_details': self.usage_stats,
            'current_source': self.free_sources[self.current_source_index]['name'] if self.free_sources else None
        }
    
    def reset_daily_stats(self):
        """重置日统计"""
        
        today = datetime.now().date()
        for stats in self.usage_stats.values():
            stats['calls_today'] = 0
            stats['quota_used'] = 0.0
            stats['consecutive_failures'] = 0
        
        log("🔄 免费API日统计已重置", "INFO")

# 与现有AI集成器的兼容适配器
class WMZCFreeAPIAdapter:
    """WMZC免费API适配器 - 与现有AI集成器兼容"""
    
    def __init__(self, wmzc_app):
        self.wmzc = wmzc_app
        self.free_manager = WMZCFreeAPIManager(wmzc_app)
        
        # 尝试获取现有的AI集成器
        self.ai_integrator = getattr(wmzc_app, 'ai_integrator', None)
        
        log("🔗 免费API适配器初始化完成", "INFO")
    
    async def enhanced_ai_call(self, prompt: str, **kwargs) -> Optional[Dict]:
        """增强的AI调用 - 优先使用免费API"""
        
        # 首先尝试免费API
        try:
            response = await self.free_manager.make_free_api_call(prompt, **kwargs)
            if response:
                log("✅ 使用免费API成功", "DEBUG")
                return response
        except Exception as e:
            log(f"免费API调用失败: {e}", "WARNING")
        
        # 免费API失败，降级到原有AI集成器
        if self.ai_integrator:
            try:
                # 使用原有的AI集成器
                response = await self.ai_integrator.analyze_market_sentiment(prompt, {})
                if response:
                    log("✅ 降级到原有AI集成器成功", "DEBUG")
                    return response
            except Exception as e:
                log(f"原有AI集成器也失败: {e}", "ERROR")
        
        return None
    
    def get_combined_status(self) -> Dict:
        """获取组合状态"""
        
        free_status = self.free_manager.get_free_api_status()
        
        # 如果有原有AI集成器，也获取其状态
        original_status = {}
        if self.ai_integrator:
            try:
                original_status = self.ai_integrator.get_performance_metrics()
            except:
                pass
        
        return {
            'free_api_status': free_status,
            'original_ai_status': original_status,
            'adapter_active': True,
            'timestamp': time.time()
        }

# 全局免费API管理器实例
free_api_adapter = None

def initialize_free_api_for_wmzc(wmzc_app) -> Optional[WMZCFreeAPIAdapter]:
    """为WMZC初始化免费API系统"""
    
    global free_api_adapter
    
    try:
        free_api_adapter = WMZCFreeAPIAdapter(wmzc_app)
        log("🆓 WMZC免费API系统初始化成功", "INFO")
        return free_api_adapter
    except Exception as e:
        log(f"❌ WMZC免费API系统初始化失败: {e}", "ERROR")
        return None

if __name__ == "__main__":
    print("🆓 WMZC免费API管理器")
    print("请在WMZC.py中调用 initialize_free_api_for_wmzc() 函数进行集成")
