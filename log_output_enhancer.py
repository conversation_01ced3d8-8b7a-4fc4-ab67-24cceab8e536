#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 WMZC日志输出增强器
增强日志输出机制，确保日志正确显示在控制台
"""

import threading
import queue
import time
from datetime import datetime

class LogOutputEnhancer:
    """日志输出增强器"""
    
    def __init__(self):
        self.log_queue = queue.Queue()
        self.gui_callback = None
        self.output_thread = None
        self.running = False
        
    def set_gui_callback(self, callback):
        """设置GUI回调"""
        self.gui_callback = callback
        
    def start_log_processor(self):
        """启动日志处理器"""
        if self.running:
            return
        
        self.running = True
        self.output_thread = threading.Thread(target=self._process_logs, daemon=True)
        self.output_thread.start()
        print("✅ 日志处理器已启动")
    
    def stop_log_processor(self):
        """停止日志处理器"""
        self.running = False
        if self.output_thread:
            self.output_thread.join(timeout=1)
        print("✅ 日志处理器已停止")
    
    def add_log(self, message, level="INFO"):
        """添加日志到队列"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_entry = {
                'timestamp': timestamp,
                'message': message,
                'level': level,
                'formatted': f"[{timestamp}] {level} - {message}"
            }
            self.log_queue.put(log_entry, timeout=1)
        except queue.Full:
            print("⚠️ 日志队列已满，丢弃日志消息")
    
    def _process_logs(self):
        """处理日志队列"""
        while self.running:
            try:
                # 从队列获取日志
                log_entry = self.log_queue.get(timeout=0.1)
                
                # 输出到控制台
                print(log_entry['formatted'])
                
                # 输出到GUI（如果可用）
                if self.gui_callback:
                    try:
                        self.gui_callback(log_entry['message'], log_entry['level'])
                    except Exception as e:
                        print(f"GUI日志输出失败: {e}")
                
                self.log_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"日志处理异常: {e}")
                time.sleep(0.1)
    
    def flush_logs(self):
        """刷新所有待处理的日志"""
        try:
            while not self.log_queue.empty():
                log_entry = self.log_queue.get_nowait()
                print(log_entry['formatted'])
                if self.gui_callback:
                    try:
                        self.gui_callback(log_entry['message'], log_entry['level'])
                    except:
                        pass
                self.log_queue.task_done()
        except queue.Empty:
            pass

# 创建全局日志输出增强器
log_output_enhancer = LogOutputEnhancer()

def start_enhanced_logging():
    """启动增强日志系统"""
    log_output_enhancer.start_log_processor()

def stop_enhanced_logging():
    """停止增强日志系统"""
    log_output_enhancer.stop_log_processor()

def enhanced_log(message, level="INFO"):
    """增强日志函数"""
    log_output_enhancer.add_log(message, level)

def set_enhanced_gui_callback(callback):
    """设置增强GUI回调"""
    log_output_enhancer.set_gui_callback(callback)

if __name__ == "__main__":
    # 测试增强日志系统
    start_enhanced_logging()
    
    enhanced_log("测试INFO消息", "INFO")
    enhanced_log("测试WARNING消息", "WARNING")
    enhanced_log("测试ERROR消息", "ERROR")
    
    time.sleep(1)
    stop_enhanced_logging()
