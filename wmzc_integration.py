#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 WMZC系统集成模块
负责现代化GUI与原WMZC系统的完整集成
"""

import asyncio
import threading
import time
import json
import os
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from queue import Queue, Empty
from queue import Queue, Empty
from threading import RLock

class WMZCIntegrationBridge:
    """WMZC集成桥接器 - 连接现代GUI与原系统"""
    
    def __init__(self, modern_gui=None, original_wmzc=None):
        self.modern_gui = modern_gui
        self.original_wmzc = original_wmzc
        
        # 数据同步状态
        self.sync_active = False
        self.sync_interval = 1.0  # 秒
        self.last_sync_time = 0
        
        # 事件监听器
        self.event_listeners = {}

        # GUI更新队列（线程安全）
        self.gui_update_queue = Queue()

        # 数据缓存
        self.data_cache = {
            "ticker_data": {},
            "account_info": {},
            "positions": {},
            "orders": {},
            "trading_records": [],
            "news_data": [],
            "indicator_data": {}
        }
        
        self.setup_integration()
        
    def setup_integration(self):
        """设置集成"""
        if self.modern_gui and self.original_wmzc:
            # 绑定现代GUI到原系统
            self.modern_gui.wmzc_instance = self.original_wmzc
            
            # 设置数据同步
            self.setup_data_sync()

            # 设置GUI更新处理
            self.setup_gui_update_handler()

            # 设置事件监听
            self.setup_event_listeners()

            # 迁移配置
            self.migrate_configuration()
            
    def setup_data_sync(self):
        """设置数据同步"""
        def sync_loop():
            while self.sync_active:
                try:
                    self.sync_all_data()
                    time.sleep(self.sync_interval)
                except Exception as e:
                    print(f"数据同步错误: {e}")
                    time.sleep(5)  # 错误时延长等待时间
                    
        self.sync_active = True
        sync_thread = threading.Thread(target=sync_loop, daemon=True)
        sync_thread.start()

    def setup_gui_update_handler(self):
        """设置GUI更新处理器（线程安全）"""
        def process_gui_updates():
            """处理GUI更新队列"""
            try:
                while True:
                    update_func, args, kwargs = self.gui_update_queue.get(timeout=0.1)
                    if self.modern_gui and self.modern_gui.root:
                        # 在主线程中执行GUI更新
                        self.modern_gui.root.after_idle(update_func, *args, **kwargs)
                    self.gui_update_queue.task_done()
            except Empty:
                pass
            except Exception as e:
                print(f"GUI更新处理错误: {e}")
            finally:
                # 继续处理
                if self.modern_gui and self.modern_gui.root:
                    self.modern_gui.root.after(100, process_gui_updates)

        if self.modern_gui and self.modern_gui.root:
            self.modern_gui.root.after(100, process_gui_updates)
        
    def sync_all_data(self):
        """同步所有数据"""
        current_time = time.time()
        
        # 避免过于频繁的同步
        if current_time - self.last_sync_time < self.sync_interval:
            return
            
        try:
            # 同步行情数据
            self.sync_ticker_data()
            
            # 同步账户信息
            self.sync_account_info()
            
            # 同步持仓信息
            self.sync_positions()
            
            # 同步订单信息
            self.sync_orders()
            
            # 同步交易记录
            self.sync_trading_records()
            
            # 同步新闻数据
            self.sync_news_data()
            
            # 同步指标数据
            self.sync_indicator_data()
            
            # 更新GUI显示
            self.update_gui_displays()
            
            self.last_sync_time = current_time
            
        except Exception as e:
            print(f"数据同步失败: {e}")
            
    def sync_ticker_data(self):
        """同步行情数据"""
        if hasattr(self.original_wmzc, 'get_current_price'):
            try:
                # 获取主要交易对的价格
                symbols = ['BTC-USDT', 'ETH-USDT', 'SOL-USDT']
                for symbol in symbols:
                    price = self.original_wmzc.get_current_price(symbol)
                    if price:
                        self.data_cache["ticker_data"][symbol] = {
                            "price": price,
                            "timestamp": time.time()
                        }
            except Exception as e:
                print(f"同步行情数据失败: {e}")
                
    def sync_account_info(self):
        """同步账户信息"""
        if hasattr(self.original_wmzc, 'get_account_balance'):
            try:
                balance = self.original_wmzc.get_account_balance()
                if balance:
                    self.data_cache["account_info"] = {
                        "balance": balance,
                        "timestamp": time.time()
                    }
            except Exception as e:
                print(f"同步账户信息失败: {e}")
                
    def sync_positions(self):
        """同步持仓信息"""
        if hasattr(self.original_wmzc, 'get_positions'):
            try:
                positions = self.original_wmzc.get_positions()
                if positions:
                    self.data_cache["positions"] = {
                        "data": positions,
                        "timestamp": time.time()
                    }
            except Exception as e:
                print(f"同步持仓信息失败: {e}")
                
    def sync_orders(self):
        """同步订单信息"""
        if hasattr(self.original_wmzc, 'get_open_orders'):
            try:
                orders = self.original_wmzc.get_open_orders()
                if orders:
                    self.data_cache["orders"] = {
                        "data": orders,
                        "timestamp": time.time()
                    }
            except Exception as e:
                print(f"同步订单信息失败: {e}")
                
    def sync_trading_records(self):
        """同步交易记录"""
        if hasattr(self.original_wmzc, 'get_trading_history'):
            try:
                records = self.original_wmzc.get_trading_history(limit=50)
                if records:
                    self.data_cache["trading_records"] = records
            except Exception as e:
                print(f"同步交易记录失败: {e}")
                
    def sync_news_data(self):
        """同步新闻数据"""
        if hasattr(self.original_wmzc, 'get_latest_news'):
            try:
                news = self.original_wmzc.get_latest_news(limit=20)
                if news:
                    self.data_cache["news_data"] = news
            except Exception as e:
                print(f"同步新闻数据失败: {e}")
                
    def sync_indicator_data(self):
        """同步指标数据"""
        if hasattr(self.original_wmzc, 'get_indicators'):
            try:
                indicators = self.original_wmzc.get_indicators()
                if indicators:
                    self.data_cache["indicator_data"] = {
                        "data": indicators,
                        "timestamp": time.time()
                    }
            except Exception as e:
                print(f"同步指标数据失败: {e}")
                
    def update_gui_displays(self):
        """更新GUI显示（线程安全）"""
        if not self.modern_gui:
            return

        # 将GUI更新任务放入队列，由主线程处理
        try:
            # 更新连接状态
            if self.data_cache.get("account_info"):
                self.queue_gui_update(self._update_connection_status, "🟢 已连接")

            # 更新系统状态
            if self.sync_active:
                self.queue_gui_update(self._update_system_status, "🟢 系统运行中")

            # 更新实时数据（如果当前在相关页面）
            if hasattr(self.modern_gui, 'current_tab'):
                if self.modern_gui.current_tab == "trading_records":
                    self.queue_gui_update(self.update_trading_records_display)
                elif self.modern_gui.current_tab == "indicators":
                    self.queue_gui_update(self.update_indicators_display)
                elif self.modern_gui.current_tab == "news":
                    self.queue_gui_update(self.update_news_display)

        except Exception as e:
            print(f"更新GUI显示失败: {e}")

    def queue_gui_update(self, func, *args, **kwargs):
        """将GUI更新任务加入队列（线程安全）"""
        try:
            self.gui_update_queue.put((func, args, kwargs), timeout=1.0)
        except Exception as e:
            print(f"GUI更新队列添加失败: {e}")

    def _update_connection_status(self, status_text):
        """更新连接状态（在主线程中执行）"""
        try:
            if hasattr(self.modern_gui, 'connection_status'):
                self.modern_gui.connection_status.configure(text=status_text)
        except Exception as e:
            print(f"更新连接状态失败: {e}")

    def _update_system_status(self, status_text):
        """更新系统状态（在主线程中执行）"""
        try:
            if hasattr(self.modern_gui, 'system_status'):
                self.modern_gui.system_status.configure(text=status_text)
        except Exception as e:
            print(f"更新系统状态失败: {e}")
            
    def update_trading_records_display(self):
        """更新交易记录显示"""
        if hasattr(self.modern_gui, 'trading_tree') and self.data_cache.get("trading_records"):
            try:
                # 清空现有数据
                for item in self.modern_gui.trading_tree.get_children():
                    self.modern_gui.trading_tree.delete(item)
                    
                # 添加新数据
                for record in self.data_cache["trading_records"][:10]:  # 显示最近10条
                    self.modern_gui.trading_tree.insert("", "end", values=(
                        record.get("time", ""),
                        record.get("symbol", ""),
                        record.get("side", ""),
                        record.get("amount", ""),
                        record.get("price", ""),
                        record.get("pnl", ""),
                        record.get("status", "")
                    ))
            except Exception as e:
                print(f"更新交易记录显示失败: {e}")
                
    def update_indicators_display(self):
        """更新指标显示"""
        if self.data_cache.get("indicator_data"):
            try:
                indicators = self.data_cache["indicator_data"]["data"]
                
                # 更新MACD值
                if "macd" in indicators and hasattr(self.modern_gui, 'macd_value_label'):
                    macd_value = indicators["macd"].get("value", 0)
                    self.modern_gui.macd_value_label.configure(text=f"MACD: {macd_value:.4f}")
                    
                # 更新RSI值
                if "rsi" in indicators and hasattr(self.modern_gui, 'rsi_value_label'):
                    rsi_value = indicators["rsi"].get("value", 0)
                    color = "red" if rsi_value > 70 else "green" if rsi_value < 30 else "orange"
                    self.modern_gui.rsi_value_label.configure(
                        text=f"当前RSI: {rsi_value:.1f}",
                        text_color=color
                    )
                    
            except Exception as e:
                print(f"更新指标显示失败: {e}")
                
    def update_news_display(self):
        """更新新闻显示"""
        # 新闻数据通常不需要实时更新，可以按需实现
        pass
        
    def setup_event_listeners(self):
        """设置事件监听"""
        # 监听原系统的事件
        if hasattr(self.original_wmzc, 'add_event_listener'):
            self.original_wmzc.add_event_listener('order_filled', self.on_order_filled)
            self.original_wmzc.add_event_listener('position_changed', self.on_position_changed)
            self.original_wmzc.add_event_listener('balance_changed', self.on_balance_changed)
            
    def on_order_filled(self, order_data):
        """订单成交事件"""
        if self.modern_gui:
            self.modern_gui.update_status(f"订单成交: {order_data.get('symbol', '')} {order_data.get('side', '')}")
            
    def on_position_changed(self, position_data):
        """持仓变化事件"""
        if self.modern_gui:
            self.modern_gui.update_status(f"持仓变化: {position_data.get('symbol', '')}")
            
    def on_balance_changed(self, balance_data):
        """余额变化事件"""
        if self.modern_gui:
            self.modern_gui.update_status(f"余额更新: {balance_data.get('total', '')}")
            
    def migrate_configuration(self):
        """迁移配置"""
        try:
            # 从原系统读取配置
            if hasattr(self.original_wmzc, 'config'):
                original_config = self.original_wmzc.config
                
                # 迁移到现代GUI
                if self.modern_gui:
                    # API配置
                    if 'api_key' in original_config:
                        self.modern_gui.api_key_var.set(original_config['api_key'])
                    if 'secret_key' in original_config:
                        self.modern_gui.secret_key_var.set(original_config['secret_key'])
                    if 'passphrase' in original_config:
                        self.modern_gui.passphrase_var.set(original_config['passphrase'])
                        
                    # 交易配置
                    if 'symbol' in original_config:
                        self.modern_gui.symbol_var.set(original_config['symbol'])
                    if 'amount' in original_config:
                        self.modern_gui.amount_var.set(str(original_config['amount']))
                        
        except Exception as e:
            print(f"配置迁移失败: {e}")
            
    def execute_strategy_command(self, strategy_name, action, params=None):
        """执行策略命令"""
        if not self.original_wmzc:
            return False
            
        try:
            if hasattr(self.original_wmzc, 'execute_strategy'):
                result = self.original_wmzc.execute_strategy(strategy_name, action, params)
                return result
            else:
                print(f"原系统不支持策略执行: {strategy_name}")
                return False
        except Exception as e:
            print(f"执行策略命令失败: {e}")
            return False
            
    def get_cached_data(self, data_type):
        """获取缓存数据"""
        return self.data_cache.get(data_type, {})
        
    def stop_sync(self):
        """停止数据同步"""
        self.sync_active = False
        
    def restart_sync(self):
        """重启数据同步"""
        if not self.sync_active:
            self.setup_data_sync()

def create_integrated_wmzc_app(original_wmzc_instance=None):
    """创建集成的WMZC应用"""
    from modern_wmzc_gui import ModernWMZCGUI
    
    # 创建现代GUI
    modern_gui = ModernWMZCGUI()
    
    # 创建集成桥接器
    bridge = WMZCIntegrationBridge(modern_gui, original_wmzc_instance)
    
    # 将桥接器绑定到GUI
    modern_gui.integration_bridge = bridge
    
    return modern_gui, bridge

if __name__ == "__main__":
    # 测试集成
    try:
        # 尝试导入原WMZC系统
        import WMZC
        original_app = WMZC.TradingApp()
        
        # 创建集成应用
        modern_gui, bridge = create_integrated_wmzc_app(original_app)
        
        print("🚀 集成WMZC应用启动成功")
        modern_gui.run()
        
    except ImportError:
        print("⚠️ 未找到原WMZC系统，启动独立现代GUI")
        modern_gui, bridge = create_integrated_wmzc_app()
        modern_gui.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
