import json
import os

print("🔧 快速修复WMZC配置...")

# 默认配置
default_config = {
    'ORDER_USDT_AMOUNT': 10.0,
    'LEVERAGE': 3,
    'RISK_PERCENT': 1.0,
    'SYMBOL': 'BTC-USDT-SWAP',
    'TIMEFRAME': '1m',
    'API_KEY': '',
    'API_SECRET': '',
    'PASSPHRASE': '',
    'OKX_API_KEY': '',
    'OKX_SECRET_KEY': '',
    'OKX_PASSPHRASE': '',
    'ENABLE_TRADING': False,
    'TEST_MODE': True,
    'MAX_POSITIONS': 5,
    'STOP_LOSS_PERCENT': 2.0,
    'TAKE_PROFIT_PERCENT': 3.0,
    'ENABLE_MACD': True,
    'ENABLE_KDJ': True,
    'ENABLE_RSI': True,
    'ENABLE_PINBAR': False,
    'LOG_LEVEL': 'INFO'
}

# 修复trading_config.json
config_file = 'trading_config.json'
existing_config = {}

if os.path.exists(config_file):
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            existing_config = json.load(f)
        print(f"✅ 已加载现有配置: {len(existing_config)} 项")
    except Exception as e:
        print(f"❌ 加载失败: {e}")

# 合并配置
merged_config = default_config.copy()
merged_config.update(existing_config)

# 保存配置
try:
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(merged_config, f, indent=2, ensure_ascii=False)
    
    new_items = set(merged_config.keys()) - set(existing_config.keys())
    print(f"✅ 配置已更新: 总计 {len(merged_config)} 项")
    if new_items:
        print(f"🆕 新增 {len(new_items)} 项配置")
    print("🎉 配置修复完成！")
    
except Exception as e:
    print(f"❌ 保存失败: {e}")

print("\n💡 请重启WMZC系统并在主配置页面填写API密钥")
