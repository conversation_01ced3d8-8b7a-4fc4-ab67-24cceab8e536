#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速配置检查 - 验证CONFIG_SCHEMA是否包含所有必要字段
"""

import json
import os

def check_config_schema():
    """检查CONFIG_SCHEMA是否包含所有配置字段"""
    print("🔍 快速配置检查")
    print("=" * 50)
    
    # 读取实际配置文件
    config_file = r'C:\Users\<USER>\.wmzc_trading\trading_config.json'
    if not os.path.exists(config_file):
        print("❌ 配置文件不存在")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        actual_config = json.load(f)
    
    print(f"✅ 配置文件加载成功，包含 {len(actual_config)} 个字段")
    
    # 从WMZC.py中提取CONFIG_SCHEMA字段
    wmzc_file = "WMZC.py"
    if not os.path.exists(wmzc_file):
        print("❌ WMZC.py文件不存在")
        return False
    
    # 读取WMZC.py文件，提取CONFIG_SCHEMA中的字段
    schema_fields = set()
    
    with open(wmzc_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    in_config_schema = False
    for line in lines:
        line = line.strip()
        
        # 检测CONFIG_SCHEMA开始
        if 'CONFIG_SCHEMA = {' in line:
            in_config_schema = True
            continue
        
        # 检测CONFIG_SCHEMA结束
        if in_config_schema and line.startswith('}') and not line.startswith("'}"):
            break
        
        # 提取字段名
        if in_config_schema and line.startswith("'") and ':' in line:
            field_name = line.split("'")[1]
            schema_fields.add(field_name)
    
    print(f"✅ CONFIG_SCHEMA包含 {len(schema_fields)} 个字段定义")
    
    # 检查哪些字段在配置文件中但不在schema中
    config_fields = set(actual_config.keys())
    unknown_fields = config_fields - schema_fields
    
    print(f"\n📊 字段对比分析:")
    print(f"  - 配置文件字段数: {len(config_fields)}")
    print(f"  - CONFIG_SCHEMA字段数: {len(schema_fields)}")
    print(f"  - 未知字段数: {len(unknown_fields)}")
    
    if unknown_fields:
        print(f"\n❌ 仍有未知字段 ({len(unknown_fields)}个):")
        for i, field in enumerate(sorted(unknown_fields), 1):
            print(f"  {i:2d}. {field}")
        
        print(f"\n🔧 需要在CONFIG_SCHEMA中添加这些字段:")
        for field in sorted(unknown_fields):
            print(f"        '{field}': {{'type': str, 'required': False}},")
        
        return False
    else:
        print(f"\n✅ 所有配置字段都已在CONFIG_SCHEMA中定义")
        print(f"🎉 配置警告问题已完全解决！")
        return True

def check_target_fields():
    """检查目标修复字段"""
    print(f"\n🎯 检查目标修复字段:")
    
    # 之前识别的9个缺失字段
    target_fields = [
        'ENABLE_LOGGING', 'LOG_TO_CONSOLE', 'KDJ_PERIOD',
        'MACD_FAST', 'MACD_SLOW', 'MACD_SIGNAL',
        'STOP_LOSS', 'TAKE_PROFIT', 'api_key'
    ]
    
    # 从WMZC.py中检查这些字段是否存在
    wmzc_file = "WMZC.py"
    with open(wmzc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    found_fields = []
    missing_fields = []
    
    for field in target_fields:
        if f"'{field}'" in content and 'CONFIG_SCHEMA' in content:
            found_fields.append(field)
        else:
            missing_fields.append(field)
    
    print(f"  ✅ 已添加字段 ({len(found_fields)}个):")
    for field in found_fields:
        print(f"    - {field}")
    
    if missing_fields:
        print(f"  ❌ 仍缺失字段 ({len(missing_fields)}个):")
        for field in missing_fields:
            print(f"    - {field}")
        return False
    else:
        print(f"  🎉 所有目标字段都已添加！")
        return True

if __name__ == "__main__":
    schema_ok = check_config_schema()
    target_ok = check_target_fields()
    
    print(f"\n" + "=" * 50)
    print(f"📊 最终结果:")
    print(f"  配置架构完整性: {'✅ 通过' if schema_ok else '❌ 失败'}")
    print(f"  目标字段修复: {'✅ 通过' if target_ok else '❌ 失败'}")
    
    if schema_ok and target_ok:
        print(f"\n🎉 配置警告修复完全成功！")
        print(f"💡 启动WMZC系统时应该不再有'发现 9 个未知配置字段'的警告")
    else:
        print(f"\n⚠️ 修复未完成，需要进一步处理")
