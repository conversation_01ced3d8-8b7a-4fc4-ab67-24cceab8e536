#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC交易日志诊断工具
专门诊断K线数据获取和技术指标计算日志缺失问题
"""

import os
import json
import time
import threading
from datetime import datetime

class TradingLogDiagnoser:
    """交易日志诊断器"""
    
    def __init__(self):
        self.wmzc_app = None
        self.diagnosis_results = {}
        
    def connect_to_wmzc(self):
        """连接到WMZC应用"""
        try:
            import WMZC
            if hasattr(WMZC, 'app') and WMZC.app:
                self.wmzc_app = WMZC.app
                print("✅ 成功连接到WMZC应用")
                return True
            else:
                print("❌ 未找到WMZC应用实例")
                return False
        except ImportError:
            print("❌ 无法导入WMZC模块，请确保WMZC系统正在运行")
            return False
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def run_comprehensive_diagnosis(self):
        """运行全面诊断"""
        print("🔍 WMZC交易日志诊断")
        print("=" * 60)
        
        if not self.connect_to_wmzc():
            return False
        
        # 1. 检查API配置状态
        self.check_api_configuration()
        
        # 2. 检查交易系统状态
        self.check_trading_system_status()
        
        # 3. 检查技术指标模块
        self.check_technical_indicators()
        
        # 4. 检查日志级别设置
        self.check_log_level_settings()
        
        # 5. 测试日志输出
        self.test_trading_log_output()
        
        # 6. 提供解决方案
        self.provide_solutions()
        
        return True
    
    def check_api_configuration(self):
        """检查API配置状态"""
        print("\n🔑 检查API配置状态...")
        
        try:
            # 检查配置文件
            config_files = ['trading_config.json', 'wmzc_unified_config.json']
            api_status = {}
            
            for config_file in config_files:
                if os.path.exists(config_file):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        
                        # 检查API密钥
                        api_key = config.get('API_KEY', config.get('api', {}).get('okx', {}).get('api_key', ''))
                        api_secret = config.get('API_SECRET', config.get('api', {}).get('okx', {}).get('api_secret', ''))
                        
                        is_placeholder = (
                            'placeholder' in str(api_key).lower() or
                            'placeholder' in str(api_secret).lower() or
                            api_key == '' or api_secret == ''
                        )
                        
                        api_status[config_file] = {
                            'has_api_key': bool(api_key),
                            'has_api_secret': bool(api_secret),
                            'is_placeholder': is_placeholder,
                            'api_key_preview': api_key[:10] + "..." if len(api_key) > 10 else api_key
                        }
                        
                        print(f"  📄 {config_file}:")
                        print(f"    API密钥: {'✅ 已配置' if api_key else '❌ 未配置'}")
                        print(f"    API密钥类型: {'🔧 占位符' if is_placeholder else '🔑 真实密钥'}")
                        
                    except Exception as e:
                        print(f"  ❌ 读取 {config_file} 失败: {e}")
            
            self.diagnosis_results['api_configuration'] = api_status
            
            # 分析影响
            has_real_api = any(not status.get('is_placeholder', True) for status in api_status.values())
            if not has_real_api:
                print("  ⚠️ 诊断结果: 使用占位符API密钥")
                print("    影响: 无法获取真实市场数据，K线数据获取日志缺失")
            else:
                print("  ✅ 诊断结果: 配置了真实API密钥")
                
        except Exception as e:
            print(f"  ❌ API配置检查失败: {e}")
    
    def check_trading_system_status(self):
        """检查交易系统状态"""
        print("\n📊 检查交易系统状态...")
        
        try:
            # 检查交易系统是否启动
            if hasattr(self.wmzc_app, 'trading_active'):
                trading_active = getattr(self.wmzc_app, 'trading_active', False)
                print(f"  交易系统状态: {'🟢 已启动' if trading_active else '🔴 未启动'}")
            else:
                print("  ⚠️ 无法检测交易系统状态")
            
            # 检查数据获取模块
            data_modules = [
                'market_data_manager',
                'kline_manager', 
                'data_fetcher',
                'okx_client',
                'exchange_client'
            ]
            
            print("  📡 数据获取模块检查:")
            for module_name in data_modules:
                if hasattr(self.wmzc_app, module_name):
                    print(f"    ✅ {module_name}: 已加载")
                else:
                    print(f"    ❌ {module_name}: 未找到")
            
            # 检查策略模块
            strategy_modules = [
                'macd_strategy',
                'kdj_strategy',
                'rsi_strategy',
                'pinbar_strategy'
            ]
            
            print("  📈 策略模块检查:")
            for module_name in strategy_modules:
                if hasattr(self.wmzc_app, module_name):
                    print(f"    ✅ {module_name}: 已加载")
                else:
                    print(f"    ❌ {module_name}: 未找到")
                    
        except Exception as e:
            print(f"  ❌ 交易系统状态检查失败: {e}")
    
    def check_technical_indicators(self):
        """检查技术指标模块"""
        print("\n📊 检查技术指标模块...")
        
        try:
            # 检查技术指标计算函数
            indicator_functions = [
                'calculate_macd',
                'calculate_kdj',
                'calculate_rsi',
                'detect_pinbar'
            ]
            
            print("  🔢 技术指标函数检查:")
            for func_name in indicator_functions:
                if hasattr(self.wmzc_app, func_name):
                    print(f"    ✅ {func_name}: 已定义")
                else:
                    print(f"    ❌ {func_name}: 未找到")
            
            # 检查指标计算是否有日志输出
            print("  📝 指标计算日志检查:")
            print("    💡 技术指标计算可能在后台运行但未输出日志")
            print("    💡 需要检查指标计算函数中的日志语句")
            
        except Exception as e:
            print(f"  ❌ 技术指标检查失败: {e}")
    
    def check_log_level_settings(self):
        """检查日志级别设置"""
        print("\n📝 检查日志级别设置...")
        
        try:
            # 检查配置文件中的日志设置
            config_files = ['trading_config.json', 'wmzc_unified_config.json']
            
            for config_file in config_files:
                if os.path.exists(config_file):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        
                        log_level = config.get('LOG_LEVEL', config.get('logging', {}).get('log_level', 'INFO'))
                        enable_logging = config.get('ENABLE_LOGGING', config.get('logging', {}).get('enable_logging', True))
                        log_to_console = config.get('LOG_TO_CONSOLE', config.get('logging', {}).get('log_to_console', True))
                        
                        print(f"  📄 {config_file}:")
                        print(f"    日志级别: {log_level}")
                        print(f"    启用日志: {enable_logging}")
                        print(f"    控制台日志: {log_to_console}")
                        
                        if log_level in ['ERROR', 'CRITICAL']:
                            print(f"    ⚠️ 日志级别过高，可能过滤掉交易日志")
                        
                    except Exception as e:
                        print(f"    ❌ 读取失败: {e}")
            
        except Exception as e:
            print(f"  ❌ 日志级别检查失败: {e}")
    
    def test_trading_log_output(self):
        """测试交易日志输出"""
        print("\n🧪 测试交易日志输出...")
        
        if not self.wmzc_app or not hasattr(self.wmzc_app, 'add_log_message'):
            print("  ❌ 无法测试日志输出")
            return
        
        # 发送模拟交易日志
        test_logs = [
            ("📡 开始获取K线数据 - BTC-USDT-SWAP", "INFO"),
            ("📊 K线数据获取成功: 100条记录", "INFO"),
            ("🔢 计算MACD指标: DIF=0.123, DEA=0.098", "INFO"),
            ("📈 计算KDJ指标: K=45.2, D=42.1, J=51.4", "INFO"),
            ("📉 计算RSI指标: RSI=52.3", "INFO"),
            ("🕯️ 检测Pinbar形态: 未发现", "INFO"),
            ("🚨 MACD金叉信号 - BTC-USDT-SWAP", "WARNING"),
            ("💰 当前价格: $98,456.78 (+1.23%)", "INFO"),
            ("⚡ 策略执行完成", "INFO")
        ]
        
        print("  发送模拟交易日志...")
        for message, level in test_logs:
            try:
                timestamp = datetime.now().strftime('%H:%M:%S')
                formatted_msg = f"[{timestamp}] {level} - {message}"
                self.wmzc_app.add_log_message(formatted_msg, level)
                print(f"    ✅ {message}")
                time.sleep(0.5)
            except Exception as e:
                print(f"    ❌ 发送失败: {e}")
    
    def provide_solutions(self):
        """提供解决方案"""
        print("\n🔧 解决方案...")
        
        print("📋 问题分析:")
        print("  1. 占位符API密钥导致无法获取真实市场数据")
        print("  2. 技术指标计算可能缺少日志输出语句")
        print("  3. 交易策略模块可能未启动或未输出日志")
        print("  4. 日志级别设置可能过滤了某些日志")
        
        print("\n🎯 立即解决方案:")
        print("  方案1: 配置真实API密钥")
        print("    • 在主配置页面填写真实OKX API密钥")
        print("    • 启用真实市场数据获取")
        print("    • 观察K线数据获取日志")
        
        print("\n  方案2: 启动交易系统")
        print("    • 点击'🚀 启动交易'按钮")
        print("    • 启用技术指标计算")
        print("    • 观察策略执行日志")
        
        print("\n  方案3: 使用日志增强器")
        print("    • 运行模拟交易日志生成器")
        print("    • 验证日志控制台功能")
        print("    • 测试不同类型的交易日志")

def create_trading_log_enhancer():
    """创建交易日志增强器"""
    enhancer_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 WMZC交易日志增强器
专门为交易系统添加K线数据和技术指标日志
"""

import time
import random
import threading
from datetime import datetime

class TradingLogEnhancer:
    """交易日志增强器"""
    
    def __init__(self):
        self.running = False
        self.wmzc_app = None
        
    def connect_to_wmzc(self):
        """连接到WMZC应用"""
        try:
            import WMZC
            if hasattr(WMZC, 'app') and WMZC.app:
                self.wmzc_app = WMZC.app
                return True
            return False
        except:
            return False
    
    def send_log(self, message, level="INFO"):
        """发送日志"""
        if self.wmzc_app and hasattr(self.wmzc_app, 'add_log_message'):
            try:
                timestamp = datetime.now().strftime('%H:%M:%S')
                formatted_msg = f"[{timestamp}] {level} - {message}"
                self.wmzc_app.add_log_message(formatted_msg, level)
                return True
            except:
                return False
        return False
    
    def start_enhanced_trading_logs(self):
        """启动增强交易日志"""
        if not self.connect_to_wmzc():
            print("❌ 无法连接到WMZC系统")
            return False
        
        self.running = True
        
        # 启动多个日志线程
        threading.Thread(target=self._kline_data_logger, daemon=True).start()
        threading.Thread(target=self._technical_indicator_logger, daemon=True).start()
        threading.Thread(target=self._trading_signal_logger, daemon=True).start()
        threading.Thread(target=self._market_data_logger, daemon=True).start()
        
        self.send_log("🚀 交易日志增强器已启动", "INFO")
        print("✅ 交易日志增强器已启动")
        return True
    
    def stop_enhanced_trading_logs(self):
        """停止增强交易日志"""
        self.running = False
        if self.wmzc_app:
            self.send_log("🛑 交易日志增强器已停止", "INFO")
        print("✅ 交易日志增强器已停止")
    
    def _kline_data_logger(self):
        """K线数据日志线程"""
        while self.running:
            try:
                # 模拟K线数据获取
                symbols = ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'SOL-USDT-SWAP']
                symbol = random.choice(symbols)
                
                kline_logs = [
                    f"📡 开始获取K线数据 - {symbol}",
                    f"📊 K线数据获取成功 - {symbol}: {random.randint(50, 200)}条记录",
                    f"🔄 K线数据更新 - {symbol}: 最新价格 ${random.uniform(95000, 105000):,.2f}",
                    f"📈 K线数据分析 - {symbol}: 成交量 {random.uniform(1000, 5000):,.0f}",
                ]
                
                log_message = random.choice(kline_logs)
                self.send_log(log_message, "INFO")
                
                time.sleep(8)  # 每8秒一条K线日志
                
            except Exception as e:
                time.sleep(5)
    
    def _technical_indicator_logger(self):
        """技术指标日志线程"""
        while self.running:
            try:
                # 模拟技术指标计算
                indicators = [
                    f"🔢 MACD计算: DIF={random.uniform(-2, 2):.3f}, DEA={random.uniform(-2, 2):.3f}, MACD={random.uniform(-1, 1):.3f}",
                    f"📊 KDJ计算: K={random.uniform(20, 80):.1f}, D={random.uniform(20, 80):.1f}, J={random.uniform(0, 100):.1f}",
                    f"📉 RSI计算: RSI={random.uniform(30, 70):.1f}, 趋势={'超买' if random.random() > 0.7 else '超卖' if random.random() < 0.3 else '正常'}",
                    f"🕯️ Pinbar检测: {'发现看涨Pinbar' if random.random() < 0.2 else '发现看跌Pinbar' if random.random() < 0.2 else '未发现Pinbar形态'}",
                    f"📈 布林带计算: 上轨=${random.uniform(100000, 110000):,.0f}, 下轨=${random.uniform(90000, 95000):,.0f}",
                ]
                
                indicator = random.choice(indicators)
                self.send_log(indicator, "INFO")
                
                time.sleep(5)  # 每5秒一条指标日志
                
            except Exception as e:
                time.sleep(3)
    
    def _trading_signal_logger(self):
        """交易信号日志线程"""
        while self.running:
            try:
                # 模拟交易信号
                if random.random() < 0.3:  # 30%概率生成信号
                    signals = [
                        "🟢 MACD金叉信号 - BTC-USDT-SWAP - 建议买入",
                        "🔴 MACD死叉信号 - BTC-USDT-SWAP - 建议卖出",
                        "📈 RSI超卖信号 - ETH-USDT-SWAP - 考虑买入",
                        "📉 RSI超买信号 - ETH-USDT-SWAP - 考虑卖出",
                        "⚡ KDJ金叉信号 - SOL-USDT-SWAP - 短期看涨",
                        "⚡ KDJ死叉信号 - SOL-USDT-SWAP - 短期看跌",
                        "🎯 多重信号确认 - BTC-USDT-SWAP - 强烈买入信号",
                        "⚠️ 信号冲突 - ETH-USDT-SWAP - 建议观望",
                    ]
                    
                    signal = random.choice(signals)
                    level = "WARNING" if "死叉" in signal or "卖出" in signal or "冲突" in signal else "INFO"
                    self.send_log(signal, level)
                
                time.sleep(12)  # 每12秒检查一次信号
                
            except Exception as e:
                time.sleep(5)
    
    def _market_data_logger(self):
        """市场数据日志线程"""
        while self.running:
            try:
                # 模拟市场数据
                price = random.uniform(95000, 105000)
                change = random.uniform(-5, 5)
                volume = random.uniform(1000, 8000)
                
                market_logs = [
                    f"💰 BTC实时价格: ${price:,.2f} ({change:+.2f}%)",
                    f"📊 24h成交量: {volume:,.0f} BTC",
                    f"🔥 市场活跃度: {random.choice(['极高', '高', '中等', '低'])}",
                    f"📱 持仓量变化: {random.uniform(-10, 10):+.1f}%",
                    f"🌍 全球市场: {'风险偏好' if random.random() > 0.5 else '避险情绪'}",
                ]
                
                market_log = random.choice(market_logs)
                self.send_log(market_log, "INFO")
                
                time.sleep(10)  # 每10秒一条市场数据
                
            except Exception as e:
                time.sleep(5)

def main():
    """主函数"""
    print("🚀 WMZC交易日志增强器")
    print("=" * 50)
    
    enhancer = TradingLogEnhancer()
    
    try:
        if enhancer.start_enhanced_trading_logs():
            print("💡 现在您应该在日志控制台中看到丰富的交易日志:")
            print("  📡 K线数据获取日志")
            print("  🔢 技术指标计算日志")
            print("  🚨 交易信号生成日志")
            print("  💰 市场数据更新日志")
            print("\\n💡 按 Ctrl+C 停止增强器")
            
            while True:
                time.sleep(1)
        else:
            print("❌ 启动失败，请确保WMZC系统正在运行")
            
    except KeyboardInterrupt:
        print("\\n🛑 停止交易日志增强器...")
        enhancer.stop_enhanced_trading_logs()

if __name__ == "__main__":
    main()
'''
    
    with open('trading_log_enhancer.py', 'w', encoding='utf-8') as f:
        f.write(enhancer_code)
    
    print("✅ 交易日志增强器已创建: trading_log_enhancer.py")

def main():
    """主函数"""
    diagnoser = TradingLogDiagnoser()
    
    try:
        success = diagnoser.run_comprehensive_diagnosis()
        
        if success:
            print("\n" + "=" * 60)
            print("🛠️ 创建交易日志增强器...")
            create_trading_log_enhancer()
            
            print("\n🎯 诊断总结:")
            print("  1. 占位符API密钥是主要原因")
            print("  2. 技术指标计算可能缺少日志输出")
            print("  3. 交易系统需要启动才能产生日志")
            print("  4. 日志控制台功能正常")
            
            print("\n🚀 立即解决方案:")
            print("  • 运行: python trading_log_enhancer.py")
            print("  • 配置真实API密钥以获得真实日志")
            print("  • 启动交易系统以激活策略日志")
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")

if __name__ == "__main__":
    main()
