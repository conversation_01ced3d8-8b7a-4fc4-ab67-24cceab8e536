#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC系统全局验证器
确保BUG修复没有破坏系统功能
"""

import ast
import sys
import time
import importlib.util
from typing import Dict, List, Any

class SystemValidator:
    """系统验证器"""
    
    def __init__(self):
        self.validation_results = {
            'syntax_check': False,
            'import_check': False,
            'async_structure_check': False,
            'critical_functions_check': False,
            'overall_status': 'UNKNOWN'
        }
        self.errors = []
        self.warnings = []
    
    def run_full_validation(self):
        """运行完整的系统验证"""
        print("🚀 开始WMZC系统全局验证...")
        print("=" * 60)
        
        start_time = time.time()
        
        # 验证步骤
        self.validate_syntax()
        self.validate_imports()
        self.validate_async_structure()
        self.validate_critical_functions()
        
        end_time = time.time()
        
        # 生成验证报告
        self.generate_validation_report(end_time - start_time)
    
    def validate_syntax(self):
        """验证语法正确性"""
        print("📋 步骤1: 语法验证")
        
        files_to_check = [
            'WMZC.py',
            'Global_Position_Controller.py',
            'batch_order_manager.py',
            'exchange_rate_limiter.py',
            'smart_retry_handler.py'
        ]
        
        syntax_errors = 0
        
        for file_name in files_to_check:
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 语法检查
                ast.parse(content)
                print(f"  ✅ {file_name} - 语法正确")
                
            except FileNotFoundError:
                print(f"  ⚠️ {file_name} - 文件不存在")
                self.warnings.append(f"文件不存在: {file_name}")
                
            except SyntaxError as e:
                print(f"  ❌ {file_name} - 语法错误: {e.msg} (行{e.lineno})")
                self.errors.append(f"语法错误 {file_name}:{e.lineno} - {e.msg}")
                syntax_errors += 1
                
            except Exception as e:
                print(f"  ❌ {file_name} - 检查失败: {e}")
                self.errors.append(f"检查失败 {file_name}: {e}")
                syntax_errors += 1
        
        self.validation_results['syntax_check'] = syntax_errors == 0
        print(f"语法验证完成: {'✅ 通过' if syntax_errors == 0 else f'❌ 发现{syntax_errors}个错误'}")
    
    def validate_imports(self):
        """验证导入依赖"""
        print("\n📦 步骤2: 导入依赖验证")
        
        try:
            # 检查关键导入
            critical_imports = [
                'asyncio',
                'json',
                'datetime',
                'typing',
                'tkinter'
            ]
            
            import_errors = 0
            
            for module_name in critical_imports:
                try:
                    __import__(module_name)
                    print(f"  ✅ {module_name} - 导入成功")
                except ImportError as e:
                    print(f"  ❌ {module_name} - 导入失败: {e}")
                    self.errors.append(f"导入失败: {module_name}")
                    import_errors += 1
            
            self.validation_results['import_check'] = import_errors == 0
            print(f"导入验证完成: {'✅ 通过' if import_errors == 0 else f'❌ 发现{import_errors}个错误'}")
            
        except Exception as e:
            print(f"  ❌ 导入验证失败: {e}")
            self.errors.append(f"导入验证失败: {e}")
            self.validation_results['import_check'] = False
    
    def validate_async_structure(self):
        """验证异步结构"""
        print("\n⚡ 步骤3: 异步结构验证")
        
        try:
            with open('WMZC.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查异步相关的关键模式
            async_checks = {
                'time.sleep': content.count('time.sleep('),
                'await asyncio.sleep': content.count('await asyncio.sleep('),
                'async def': content.count('async def '),
                'await': content.count('await ')
            }
            
            print(f"  📊 异步结构统计:")
            for check, count in async_checks.items():
                print(f"    {check}: {count}次")
            
            # 验证异步修复效果
            if async_checks['time.sleep'] == 0:
                print(f"  ✅ time.sleep阻塞操作已全部修复")
            else:
                print(f"  ⚠️ 仍有{async_checks['time.sleep']}个time.sleep阻塞操作")
                self.warnings.append(f"仍有{async_checks['time.sleep']}个time.sleep")
            
            if async_checks['await asyncio.sleep'] > 0:
                print(f"  ✅ 已使用{async_checks['await asyncio.sleep']}个await asyncio.sleep")
            
            self.validation_results['async_structure_check'] = async_checks['time.sleep'] == 0
            print(f"异步结构验证: {'✅ 通过' if async_checks['time.sleep'] == 0 else '⚠️ 有警告'}")
            
        except Exception as e:
            print(f"  ❌ 异步结构验证失败: {e}")
            self.errors.append(f"异步结构验证失败: {e}")
            self.validation_results['async_structure_check'] = False
    
    def validate_critical_functions(self):
        """验证关键功能"""
        print("\n🔧 步骤4: 关键功能验证")
        
        try:
            # 检查关键类和函数的存在性
            with open('WMZC.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            critical_patterns = {
                'WMZC类': r'class WMZC\(',
                '异步主函数': r'async def main\(',
                '配置管理': r'def.*config',
                '风险管理': r'def.*risk',
                '交易执行': r'def.*trade',
                '日志系统': r'def log\(',
            }
            
            missing_functions = 0
            
            for func_name, pattern in critical_patterns.items():
                import re
                if re.search(pattern, content, re.IGNORECASE):
                    print(f"  ✅ {func_name} - 存在")
                else:
                    print(f"  ⚠️ {func_name} - 未找到")
                    self.warnings.append(f"未找到关键功能: {func_name}")
                    missing_functions += 1
            
            self.validation_results['critical_functions_check'] = missing_functions == 0
            print(f"关键功能验证: {'✅ 通过' if missing_functions == 0 else f'⚠️ {missing_functions}个功能需确认'}")
            
        except Exception as e:
            print(f"  ❌ 关键功能验证失败: {e}")
            self.errors.append(f"关键功能验证失败: {e}")
            self.validation_results['critical_functions_check'] = False
    
    def generate_validation_report(self, validation_time: float):
        """生成验证报告"""
        print("\n" + "=" * 60)
        print("📊 系统验证报告")
        print("=" * 60)
        
        # 计算总体状态
        passed_checks = sum(1 for result in self.validation_results.values() if result is True)
        total_checks = len([k for k in self.validation_results.keys() if k != 'overall_status'])
        
        if len(self.errors) == 0 and passed_checks == total_checks:
            self.validation_results['overall_status'] = 'PASS'
            status_icon = "🎉"
            status_text = "系统验证通过"
        elif len(self.errors) == 0:
            self.validation_results['overall_status'] = 'PASS_WITH_WARNINGS'
            status_icon = "⚠️"
            status_text = "系统验证通过（有警告）"
        else:
            self.validation_results['overall_status'] = 'FAIL'
            status_icon = "❌"
            status_text = "系统验证失败"
        
        print(f"{status_icon} 总体状态: {status_text}")
        print(f"⏱️ 验证耗时: {validation_time:.2f}秒")
        print(f"✅ 通过检查: {passed_checks}/{total_checks}")
        print(f"❌ 错误数量: {len(self.errors)}")
        print(f"⚠️ 警告数量: {len(self.warnings)}")
        
        # 详细结果
        print(f"\n📋 详细验证结果:")
        for check_name, result in self.validation_results.items():
            if check_name != 'overall_status':
                icon = "✅" if result else "❌"
                print(f"  {icon} {check_name}: {'通过' if result else '失败'}")
        
        # 错误列表
        if self.errors:
            print(f"\n❌ 错误详情:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        # 警告列表
        if self.warnings:
            print(f"\n⚠️ 警告详情:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        # 保存报告
        report_data = {
            'validation_time': validation_time,
            'overall_status': self.validation_results['overall_status'],
            'results': self.validation_results,
            'errors': self.errors,
            'warnings': self.warnings,
            'timestamp': time.time()
        }
        
        import json
        with open('system_validation_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: system_validation_report.json")
        
        # 建议
        print(f"\n💡 建议:")
        if self.validation_results['overall_status'] == 'PASS':
            print("  🎉 系统状态良好，可以正常使用！")
        elif self.validation_results['overall_status'] == 'PASS_WITH_WARNINGS':
            print("  ⚠️ 系统基本正常，建议关注警告项目")
        else:
            print("  ❌ 系统存在问题，建议修复错误后重新验证")

if __name__ == "__main__":
    validator = SystemValidator()
    validator.run_full_validation()
