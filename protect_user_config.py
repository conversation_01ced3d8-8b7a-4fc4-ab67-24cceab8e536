#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ WMZC用户配置保护脚本
防止用户API配置被系统重置或覆盖
"""

import os
import json
from datetime import datetime

def protect_user_config():
    """保护用户配置不被覆盖"""
    print("🛡️ 开始保护用户配置...")
    
    config_files = {
        'trading_config.json': 'trading_config.json',
        'wmzc_config.json': 'wmzc_config.json'
    }
    
    protected_count = 0
    
    for name, path in config_files.items():
        if os.path.exists(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查是否有用户API配置
                if name == 'trading_config.json':
                    has_user_api = (config.get('API_KEY') and 
                                   config.get('API_SECRET') and 
                                   config.get('PASSPHRASE'))
                    api_info = f"API_KEY={config.get('API_KEY', '')[:10]}..."
                else:
                    has_user_api = (config.get('okx_api_key') and 
                                   config.get('okx_secret_key') and 
                                   config.get('okx_passphrase'))
                    api_info = f"okx_api_key={config.get('okx_api_key', '')[:10]}..."
                
                if has_user_api:
                    print(f"  🔒 检测到用户API配置: {name} ({api_info})")
                    
                    # 添加保护标记
                    config['_CONFIG_PROTECTED'] = True
                    config['_PROTECTION_ENABLED'] = datetime.now().isoformat()
                    config['_USER_API_DETECTED'] = True
                    
                    # 保存配置
                    with open(path, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=2, ensure_ascii=False)
                    
                    print(f"  ✅ {name} 保护已启用")
                    protected_count += 1
                else:
                    print(f"  ⚠️ {name} 未检测到用户API配置")
                
            except Exception as e:
                print(f"  ❌ 保护 {name} 失败: {e}")
        else:
            print(f"  ❌ {name} 文件不存在")
    
    return protected_count

def disable_config_override_scripts():
    """禁用配置覆盖脚本"""
    print("\n🚫 禁用配置覆盖脚本...")
    
    override_scripts = [
        'wmzc_config_warning_silencer.py',
        'wmzc_final_config_optimizer.py',
        'wmzc_config_fixer.py'
    ]
    
    disabled_count = 0
    
    for script in override_scripts:
        if os.path.exists(script):
            try:
                # 检查脚本是否包含硬编码API密钥
                with open(script, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'da636867-490f-4e3e-81b2-870841afb860' in content:
                    print(f"  ⚠️ {script} 包含硬编码API密钥，需要禁用")
                    
                    # 重命名脚本
                    disabled_name = f"{script}.disabled"
                    if not os.path.exists(disabled_name):
                        os.rename(script, disabled_name)
                        print(f"  ✅ 已禁用: {script} -> {disabled_name}")
                        disabled_count += 1
                    else:
                        print(f"  ⚠️ {script} 已经被禁用")
                else:
                    print(f"  ✅ {script} 不包含硬编码API密钥，保持启用")
                    
            except Exception as e:
                print(f"  ❌ 检查 {script} 失败: {e}")
        else:
            print(f"  ✅ {script} 不存在")
    
    return disabled_count

def create_config_backup():
    """创建配置备份"""
    print("\n💾 创建配置备份...")
    
    backup_dir = 'user_config_backups'
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_count = 0
    
    config_files = ['trading_config.json', 'wmzc_config.json']
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                backup_path = os.path.join(backup_dir, f"{config_file}.backup_{timestamp}")
                
                # 复制文件
                import shutil
                shutil.copy2(config_file, backup_path)
                
                print(f"  ✅ {config_file} -> {backup_path}")
                backup_count += 1
                
            except Exception as e:
                print(f"  ❌ 备份 {config_file} 失败: {e}")
    
    return backup_count

def verify_protection():
    """验证保护效果"""
    print("\n🔍 验证保护效果...")
    
    config_files = ['trading_config.json', 'wmzc_config.json']
    protected_count = 0
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                if config.get('_CONFIG_PROTECTED'):
                    print(f"  ✅ {config_file}: 保护已启用")
                    
                    # 检查API配置
                    if config_file == 'trading_config.json':
                        has_api = config.get('API_KEY') and config.get('API_SECRET')
                    else:
                        has_api = config.get('okx_api_key') and config.get('okx_secret_key')
                    
                    if has_api:
                        print(f"    🔒 API配置存在且受保护")
                        protected_count += 1
                    else:
                        print(f"    ⚠️ API配置为空")
                else:
                    print(f"  ⚠️ {config_file}: 保护未启用")
                    
            except Exception as e:
                print(f"  ❌ 验证 {config_file} 失败: {e}")
        else:
            print(f"  ❌ {config_file}: 文件不存在")
    
    return protected_count

def main():
    """主函数"""
    print("🛡️ WMZC用户配置保护工具")
    print("=" * 50)
    
    try:
        # 1. 创建配置备份
        backup_count = create_config_backup()
        
        # 2. 保护用户配置
        protected_count = protect_user_config()
        
        # 3. 禁用配置覆盖脚本
        disabled_count = disable_config_override_scripts()
        
        # 4. 验证保护效果
        verified_count = verify_protection()
        
        print("\n" + "=" * 50)
        print("📊 保护结果总结:")
        print(f"  💾 配置备份: {backup_count} 个文件")
        print(f"  🛡️ 配置保护: {protected_count} 个文件")
        print(f"  🚫 脚本禁用: {disabled_count} 个脚本")
        print(f"  ✅ 保护验证: {verified_count} 个文件")
        
        if protected_count > 0:
            print("\n🎉 用户配置保护已启用！")
            print("\n💡 保护机制:")
            print("  1. ✅ 配置文件已添加保护标记")
            print("  2. ✅ 配置覆盖脚本已禁用")
            print("  3. ✅ 用户API配置已备份")
            
            print("\n🚀 现在您可以:")
            print("  1. 重新启动WMZC系统")
            print("  2. 配置应该不会被重置")
            print("  3. 如果仍有问题，请检查WMZC.py中的配置加载逻辑")
            
        else:
            print("\n⚠️ 未检测到需要保护的用户配置")
            print("💡 请先在WMZC中配置API密钥，然后重新运行此脚本")
        
        return protected_count > 0
        
    except Exception as e:
        print(f"❌ 保护过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
