#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 配置警告修复验证脚本
验证我们对WMZC系统配置警告问题的修复效果
"""

import json
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

def test_config_validation():
    """测试配置验证功能"""
    print("🔍 测试配置验证功能...")
    
    try:
        from WMZC import ConfigValidator
        
        # 测试1: 加载实际配置文件
        print("  测试1: 验证实际配置文件...")
        
        config_files = [
            'wmzc_config.json',
            'trading_config.json',
            'user_settings.json'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"    验证 {config_file}...")
                
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    # 验证配置
                    result = ConfigValidator.validate_config(config_data, strict_mode=False)
                    
                    print(f"      验证结果: {'✅ 通过' if result['valid'] else '❌ 失败'}")
                    
                    if result['errors']:
                        print(f"      错误数量: {len(result['errors'])}")
                        for error in result['errors'][:3]:  # 只显示前3个错误
                            print(f"        - {error}")
                    
                    if result['warnings']:
                        print(f"      警告数量: {len(result['warnings'])}")
                        for warning in result['warnings'][:3]:  # 只显示前3个警告
                            print(f"        - {warning}")
                    
                    if not result['errors'] and not result['warnings']:
                        print("      ✅ 无错误和警告")
                    
                except Exception as e:
                    print(f"      ❌ 验证失败: {e}")
            else:
                print(f"    ⚠️ 配置文件不存在: {config_file}")
        
        # 测试2: 测试已知有效字段
        print("  测试2: 验证已知有效字段...")
        
        valid_config = {
            'EXCHANGE': 'OKX',
            'SYMBOL': 'BTC-USDT-SWAP',
            'TIMEFRAME': '1m',
            'API_KEY': 'da636867-490f-4e3e-81b2-870841afb860',
            'PASSPHRASE': 'Mx123456@',
            'ORDER_AMOUNT': 10.0,
            'LEVERAGE': 3,
            'ENABLE_TRADING': False,
            'strategies': {'rsi': {'enabled': True}},
            'ai_config': {'features': {'sentiment_analysis': True}},
            'risk': {'stop_loss_pct': 1.0}
        }
        
        result = ConfigValidator.validate_config(valid_config, strict_mode=False)
        
        if result['valid'] and not result['warnings']:
            print("    ✅ 已知有效字段验证通过，无警告")
        else:
            print(f"    ⚠️ 已知有效字段产生了警告: {len(result.get('warnings', []))}")
            for warning in result.get('warnings', []):
                print(f"      - {warning}")
        
        # 测试3: 测试明显的测试值检测
        print("  测试3: 测试明显的测试值检测...")
        
        test_config = {
            'API_KEY': 'test_api_key',
            'PASSPHRASE': 'your_passphrase',
            'API_SECRET': '123456789'
        }
        
        result = ConfigValidator.validate_config(test_config, strict_mode=False)
        
        test_warnings = [w for w in result.get('warnings', []) if '测试值' in w or '占位符' in w]
        if test_warnings:
            print("    ✅ 正确检测到测试值")
            for warning in test_warnings:
                print(f"      - {warning}")
        else:
            print("    ❌ 未能检测到明显的测试值")
        
        # 测试4: 测试正常API密钥不被误报
        print("  测试4: 测试正常API密钥不被误报...")
        
        normal_config = {
            'API_KEY': 'da636867-490f-4e3e-81b2-870841afb860',
            'PASSPHRASE': 'Mx123456@',
            'API_SECRET': 'C15B6EE0CF3FFDEE5834865D3839325E'
        }
        
        result = ConfigValidator.validate_config(normal_config, strict_mode=False)
        
        test_warnings = [w for w in result.get('warnings', []) if '测试值' in w or '占位符' in w]
        if not test_warnings:
            print("    ✅ 正常API密钥未被误报为测试值")
        else:
            print("    ❌ 正常API密钥被误报为测试值")
            for warning in test_warnings:
                print(f"      - {warning}")
        
        print("  ✅ 配置验证功能测试完成")
        
    except Exception as e:
        print(f"  ❌ 配置验证功能测试失败: {e}")

def test_schema_coverage():
    """测试配置模式覆盖率"""
    print("🔍 测试配置模式覆盖率...")
    
    try:
        from WMZC import ConfigValidator
        
        # 统计CONFIG_SCHEMA中的字段数量
        schema_fields = set(ConfigValidator.CONFIG_SCHEMA.keys())
        print(f"  CONFIG_SCHEMA包含字段数量: {len(schema_fields)}")
        
        # 检查实际配置文件中的字段覆盖率
        all_config_fields = set()
        
        config_files = ['wmzc_config.json', 'trading_config.json']
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    # 递归收集所有字段名
                    def collect_fields(data, prefix=''):
                        fields = set()
                        if isinstance(data, dict):
                            for key, value in data.items():
                                full_key = f"{prefix}.{key}" if prefix else key
                                fields.add(key)  # 只添加顶级字段名
                                if isinstance(value, dict):
                                    fields.update(collect_fields(value, full_key))
                        return fields
                    
                    file_fields = collect_fields(config_data)
                    all_config_fields.update(file_fields)
                    print(f"  {config_file} 包含字段数量: {len(file_fields)}")
                    
                except Exception as e:
                    print(f"  ❌ 读取 {config_file} 失败: {e}")
        
        print(f"  所有配置文件字段总数: {len(all_config_fields)}")
        
        # 计算覆盖率
        covered_fields = all_config_fields.intersection(schema_fields)
        uncovered_fields = all_config_fields - schema_fields
        
        coverage_rate = len(covered_fields) / len(all_config_fields) * 100 if all_config_fields else 0
        
        print(f"  模式覆盖率: {coverage_rate:.1f}% ({len(covered_fields)}/{len(all_config_fields)})")
        
        if uncovered_fields:
            print(f"  未覆盖字段数量: {len(uncovered_fields)}")
            if len(uncovered_fields) <= 10:
                print(f"  未覆盖字段: {sorted(list(uncovered_fields))}")
            else:
                print(f"  未覆盖字段示例: {sorted(list(uncovered_fields))[:10]}...")
        
        if coverage_rate >= 90:
            print("  ✅ 配置模式覆盖率良好")
        elif coverage_rate >= 70:
            print("  ⚠️ 配置模式覆盖率一般，建议继续完善")
        else:
            print("  ❌ 配置模式覆盖率较低，需要大幅改进")
        
        print("  ✅ 配置模式覆盖率测试完成")
        
    except Exception as e:
        print(f"  ❌ 配置模式覆盖率测试失败: {e}")

def test_warning_reduction():
    """测试警告减少效果"""
    print("🔍 测试警告减少效果...")
    
    try:
        from WMZC import ConfigValidator
        
        # 模拟修复前的配置验证（使用小的schema）
        old_schema_fields = {
            'API_KEY', 'API_SECRET', 'PASSPHRASE', 'SYMBOL', 'TIMEFRAME', 
            'AMOUNT', 'ENABLE_TRADING', 'ENABLE_KDJ', 'ENABLE_MACD', 
            'ENABLE_PINBAR', 'CURRENT_EXCHANGE', 'KDJ', 'MACD', 'RSI'
        }
        
        # 加载实际配置
        if os.path.exists('trading_config.json'):
            with open('trading_config.json', 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 计算使用旧schema会产生的未知字段数量
            old_unknown_fields = set(config_data.keys()) - old_schema_fields
            
            # 计算使用新schema的未知字段数量
            result = ConfigValidator.validate_config(config_data, strict_mode=False)
            new_unknown_warnings = [w for w in result.get('warnings', []) if '未知配置字段' in w]
            
            print(f"  修复前可能的未知字段数量: {len(old_unknown_fields)}")
            print(f"  修复后的未知字段警告数量: {len(new_unknown_warnings)}")
            
            reduction_rate = (len(old_unknown_fields) - len(new_unknown_warnings)) / len(old_unknown_fields) * 100 if old_unknown_fields else 100
            
            print(f"  警告减少率: {reduction_rate:.1f}%")
            
            if reduction_rate >= 80:
                print("  ✅ 警告减少效果显著")
            elif reduction_rate >= 50:
                print("  ⚠️ 警告减少效果一般")
            else:
                print("  ❌ 警告减少效果不明显")
        
        print("  ✅ 警告减少效果测试完成")
        
    except Exception as e:
        print(f"  ❌ 警告减少效果测试失败: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("🔧 配置警告修复验证")
    print("=" * 60)
    
    # 运行所有测试
    test_config_validation()
    print()
    test_schema_coverage()
    print()
    test_warning_reduction()
    
    print("=" * 60)
    print("✅ 验证完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
