# 🔍 WMZC量化交易系统全方位BUG检测提示词

## 🎯 检测目标
基于对WMZC.py系统53,489行代码的100%深度理解，执行全方位、多维度的BUG检测，确保系统零错误运行。

## 🔒 检测原则
- ✅ 严格遵守异步编程模式，禁止多线程
- ✅ 使用真实API数据，禁止模拟数据
- ✅ 全局视角分析，禁止局部思考
- ✅ 零容忍BUG政策，必须发现并修复所有问题

---

## 📊 **1. 语法层面BUG检测**

### **1.1 变量和引用错误**
检测以下问题：
- 未定义变量的使用
- 变量名拼写错误
- 作用域错误（全局变量vs局部变量）
- 循环引用问题
- 变量类型不一致

**检测模式**:
```python
# 搜索模式
- 使用未定义的变量: `NameError`风险
- 全局变量访问: `global`关键字使用
- 变量重复定义: 同名变量在不同作用域
- 类型注解不一致: 声明类型与实际使用不符
```

### **1.2 导入和模块错误**
检测以下问题：
- 缺失的import语句
- 循环导入问题
- 模块路径错误
- 条件导入的异常处理

**检测模式**:
```python
# 搜索模式
- 未导入的模块使用: `ModuleNotFoundError`风险
- 循环导入: A导入B，B导入A
- 相对导入错误: `from .module import`
- 动态导入异常: `importlib`使用
```

### **1.3 语法结构错误**
检测以下问题：
- 缩进错误
- 括号不匹配
- 字符串引号不匹配
- 函数定义语法错误

---

## 📊 **2. 逻辑层面BUG检测**

### **2.1 条件判断错误**
检测以下问题：
- 条件表达式逻辑错误
- 空值检查缺失
- 类型检查不完整
- 边界条件处理错误

**检测模式**:
```python
# 关键检测点
- `if x:` vs `if x is not None:`
- `if len(list):` vs `if list:`
- 数值比较的精度问题
- 布尔值的隐式转换
```

### **2.2 循环逻辑错误**
检测以下问题：
- 无限循环风险
- 循环变量修改错误
- 循环中的异步操作
- 循环退出条件错误

**检测模式**:
```python
# 关键检测点
- `while True:` 没有break条件
- 循环中修改被迭代的容器
- 异步循环中的同步操作
- 嵌套循环的性能问题
```

### **2.3 异常处理错误**
检测以下问题：
- 异常捕获范围过宽
- 异常处理不完整
- 资源清理缺失
- 异常重新抛出错误

**检测模式**:
```python
# 关键检测点
- `except Exception:` 过于宽泛
- `try-except` 没有finally
- 异步函数的异常处理
- 上下文管理器使用错误
```

---

## 📊 **3. 架构层面BUG检测**

### **3.1 异步/同步混用问题**
检测以下问题：
- 异步函数中调用同步阻塞操作
- 同步函数中调用异步函数未await
- 异步锁和同步锁混用
- 事件循环管理错误

**检测模式**:
```python
# 关键检测点
- `async def` 中使用 `time.sleep()`
- 调用异步函数未使用 `await`
- `threading.Lock()` 在异步环境中
- `asyncio.run()` 在已有事件循环中
```

### **3.2 资源泄漏风险**
检测以下问题：
- 文件句柄未关闭
- 网络连接未释放
- 内存对象未清理
- 异步任务未取消

**检测模式**:
```python
# 关键检测点
- `open()` 没有对应的 `close()`
- 网络请求没有超时设置
- 大对象没有显式删除
- `asyncio.create_task()` 没有取消机制
```

### **3.3 死锁和竞态条件**
检测以下问题：
- 多个锁的获取顺序不一致
- 异步锁的不当使用
- 共享资源的并发访问
- 信号量使用错误

**检测模式**:
```python
# 关键检测点
- 多个 `async with lock:` 嵌套
- 全局变量的并发修改
- 异步回调中的状态修改
- 队列操作的原子性
```

---

## 📊 **4. 业务层面BUG检测**

### **4.1 交易逻辑错误**
检测以下问题：
- 买卖信号逻辑错误
- 价格计算精度问题
- 订单参数验证不完整
- 仓位管理逻辑错误

**检测模式**:
```python
# 关键检测点
- 浮点数精度问题: `price == target_price`
- 订单数量为0或负数
- 止损止盈逻辑冲突
- 仓位计算溢出
```

### **4.2 数据完整性问题**
检测以下问题：
- K线数据缺失处理
- 技术指标计算错误
- 数据类型转换错误
- 时间戳处理错误

**检测模式**:
```python
# 关键检测点
- DataFrame空值处理
- 时间序列数据对齐
- 数值类型转换精度
- 数据边界检查
```

### **4.3 API调用错误**
检测以下问题：
- API限频处理不当
- 错误响应处理缺失
- 超时设置不合理
- 重试机制缺陷

**检测模式**:
```python
# 关键检测点
- API调用没有限频控制
- HTTP状态码检查不完整
- 网络异常处理缺失
- 重试次数无上限
```

---

## 📊 **5. 性能层面BUG检测**

### **5.1 内存泄漏问题**
检测以下问题：
- 大对象未及时释放
- 循环引用导致内存泄漏
- 缓存无限增长
- 事件监听器未移除

**检测模式**:
```python
# 关键检测点
- 全局容器持续增长
- 回调函数引用外部对象
- 缓存没有过期机制
- 定时器未清理
```

### **5.2 无限循环和阻塞**
检测以下问题：
- 死循环条件
- 同步阻塞操作
- 网络请求无超时
- 队列阻塞等待

**检测模式**:
```python
# 关键检测点
- `while True:` 无退出条件
- `requests.get()` 无timeout
- `queue.get()` 无timeout
- 递归调用无终止条件
```

### **5.3 性能瓶颈**
检测以下问题：
- 频繁的字符串拼接
- 不必要的重复计算
- 低效的数据结构使用
- 同步操作阻塞异步流程

---

## 🎯 **检测执行策略**

### **阶段1: 静态代码分析**
1. 语法检查 - 使用AST解析
2. 类型检查 - 使用mypy等工具
3. 导入检查 - 验证所有import
4. 函数签名检查 - 参数类型一致性

### **阶段2: 逻辑流程分析**
1. 控制流分析 - 条件分支完整性
2. 数据流分析 - 变量生命周期
3. 异常路径分析 - 错误处理覆盖
4. 资源管理分析 - 获取/释放配对

### **阶段3: 运行时行为分析**
1. 异步操作检查 - await使用正确性
2. 并发安全检查 - 锁使用正确性
3. 性能热点检查 - 潜在瓶颈识别
4. 内存使用检查 - 泄漏风险评估

### **阶段4: 业务逻辑验证**
1. 交易流程完整性
2. 数据处理准确性
3. API集成正确性
4. 错误恢复机制

---

## 📋 **BUG分类标准**

### **🔴 高危BUG (Critical)**
- 系统崩溃风险
- 数据丢失风险
- 安全漏洞
- 死锁/无限循环

### **🟡 中危BUG (Major)**
- 功能异常
- 性能问题
- 内存泄漏
- 逻辑错误

### **🟢 低危BUG (Minor)**
- 代码规范问题
- 注释缺失
- 变量命名不规范
- 冗余代码

---

## 🚀 **检测输出要求**

### **BUG报告格式**
```markdown
## BUG #X: [BUG标题]
**文件**: [文件路径]
**位置**: 第X行
**类型**: [语法/逻辑/架构/业务/性能]
**严重程度**: [高危/中危/低危]
**问题描述**: [详细描述]
**影响范围**: [全局影响分析]
**修复建议**: [具体修复方案]
**验证方法**: [如何验证修复效果]
```

### **检测统计**
- 总检测文件数
- 发现BUG总数
- 按严重程度分类统计
- 按类型分类统计
- 修复优先级排序

这个检测提示词将确保对WMZC系统进行全方位、无死角的BUG检测，保证系统的稳定性和可靠性！
