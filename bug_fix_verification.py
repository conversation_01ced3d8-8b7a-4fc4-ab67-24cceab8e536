#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Bug修复验证脚本
验证P0级别bug修复的有效性
"""

import sys
import os
import traceback
import pandas as pd
import numpy as np
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append('.')

def test_dataframe_safety():
    """测试DataFrame安全检查函数"""
    print("\n🧪 测试1: DataFrame安全检查函数")
    
    try:
        # 导入WMZC模块
        import WMZC
        
        # 测试空DataFrame
        empty_df = pd.DataFrame()
        result1 = WMZC._safe_dataframe_check(empty_df)
        print(f"   空DataFrame检查: {result1} (期望: False)")
        
        # 测试有数据的DataFrame
        data_df = pd.DataFrame({'close': [1, 2, 3, 4, 5]})
        result2 = WMZC._safe_dataframe_check(data_df)
        print(f"   有数据DataFrame检查: {result2} (期望: True)")
        
        # 测试None值
        result3 = WMZC._safe_dataframe_check(None)
        print(f"   None值检查: {result3} (期望: False)")
        
        # 测试新的值检查函数
        result4 = WMZC._safe_dataframe_value_check(data_df, 'close')
        print(f"   DataFrame值检查: {result4} (期望: True)")
        
        print("   ✅ DataFrame安全检查测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ DataFrame安全检查测试失败: {e}")
        traceback.print_exc()
        return False

def test_cache_key_generation():
    """测试缓存键生成函数的安全性"""
    print("\n🧪 测试2: 缓存键生成安全性")
    
    try:
        import WMZC
        
        # 测试正常DataFrame
        df = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107], 
            'low': [99, 100, 101],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        })
        
        params = {'period': 14, 'timeframe': '1m'}
        
        # 测试缓存键生成
        cache_key = WMZC._get_cache_key(df, 'test_indicator', params)
        print(f"   正常DataFrame缓存键: {cache_key is not None} (期望: True)")
        
        # 测试空DataFrame
        empty_df = pd.DataFrame()
        cache_key2 = WMZC._get_cache_key(empty_df, 'test_indicator', params)
        print(f"   空DataFrame缓存键: {cache_key2 is None} (期望: True)")
        
        # 测试None值
        cache_key3 = WMZC._get_cache_key(None, 'test_indicator', params)
        print(f"   None值缓存键: {cache_key3 is None} (期望: True)")
        
        # 测试大型DataFrame（模拟递归风险场景）
        large_df = pd.DataFrame(np.random.randn(1000, 10))
        cache_key4 = WMZC._get_cache_key(large_df, 'test_indicator', params)
        print(f"   大型DataFrame缓存键: {cache_key4 is not None} (期望: True)")
        
        print("   ✅ 缓存键生成安全性测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 缓存键生成安全性测试失败: {e}")
        traceback.print_exc()
        return False

def test_technical_indicator_classes():
    """测试技术指标类的完整性"""
    print("\n🧪 测试3: 技术指标类完整性")
    
    try:
        import WMZC
        
        # 检查TechnicalIndicatorCalculator类是否存在
        if hasattr(WMZC, 'TechnicalIndicatorCalculator'):
            print("   ✅ TechnicalIndicatorCalculator类存在")
            
            # 检查关键方法
            calc_class = WMZC.TechnicalIndicatorCalculator
            methods = ['calculate_macd', 'calculate_rsi', 'calculate_kdj', 'calculate_sma', 'calculate_ema']
            
            for method in methods:
                if hasattr(calc_class, method):
                    print(f"   ✅ {method}方法存在")
                else:
                    print(f"   ❌ {method}方法缺失")
                    return False
        else:
            print("   ❌ TechnicalIndicatorCalculator类不存在")
            return False
            
        print("   ✅ 技术指标类完整性测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 技术指标类完整性测试失败: {e}")
        traceback.print_exc()
        return False

def test_function_uniqueness():
    """测试函数唯一性（检查是否还有重复定义）"""
    print("\n🧪 测试4: 函数唯一性检查")
    
    try:
        import WMZC
        
        # 检查是否存在全局的技术指标函数
        global_functions = ['calculate_macd', 'calculate_rsi', 'calculate_kdj']
        
        for func_name in global_functions:
            if hasattr(WMZC, func_name):
                func = getattr(WMZC, func_name)
                print(f"   ⚠️ 全局函数 {func_name} 仍然存在: {type(func)}")
                # 这可能是预期的，取决于系统设计
            else:
                print(f"   ✅ 全局函数 {func_name} 已清理")
        
        print("   ✅ 函数唯一性检查完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 函数唯一性检查失败: {e}")
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理机制"""
    print("\n🧪 测试5: 错误处理机制")
    
    try:
        import WMZC
        
        # 测试统一错误处理器
        if hasattr(WMZC, 'global_error_handler'):
            error_handler = WMZC.global_error_handler
            
            # 模拟一个错误
            test_error = ValueError("测试错误")
            result = error_handler.handle_error(test_error, "测试上下文", "WARNING")
            
            print(f"   错误处理结果: {result.get('action', 'unknown')}")
            
            # 获取错误摘要
            summary = error_handler.get_error_summary()
            print(f"   错误统计: {summary.get('total_errors', 0)}个错误")
            
            print("   ✅ 错误处理机制测试通过")
        else:
            print("   ❌ 全局错误处理器不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ 错误处理机制测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("🔧 WMZC系统Bug修复验证")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        test_dataframe_safety,
        test_cache_key_generation,
        test_technical_indicator_classes,
        test_function_uniqueness,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"   ❌ 测试执行异常: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Bug修复验证成功")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
