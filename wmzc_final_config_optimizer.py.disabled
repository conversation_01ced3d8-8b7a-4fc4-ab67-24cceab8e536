#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC最终配置优化脚本
彻底解决所有配置警告，创建完美的配置文件
"""

import json
import os

class FinalConfigOptimizer:
    """最终配置优化器"""
    
    def __init__(self):
        self.config_dir = os.path.expanduser("~/.wmzc_trading")
        self.wmzc_config_path = os.path.join(self.config_dir, "wmzc_config.json")
        self.trading_config_path = os.path.join(self.config_dir, "trading_config.json")
        
    def create_perfect_wmzc_config(self):
        """创建完美的wmzc_config.json"""
        print("🔧 创建完美的wmzc_config.json...")
        
        # 只包含系统期望的标准字段
        perfect_config = {
            # 核心API配置
            "API_KEY": "da636867-490f-4e3e-81b2-870841afb860",
            "SECRET_KEY": "C15B6EE0CF3FFDEE5834865D3839325E",
            "PASSPHRASE": "Mx123456@",
            
            # 基本配置
            "EXCHANGE": "OKX",
            "SYMBOL": "BTC-USDT-SWAP",
            "TIMEFRAME": "1m",
            
            # 交易参数
            "DEFAULT_AMOUNT": 10.0,
            "LEVERAGE": 3,
            "STOP_LOSS_PCT": 2.0,
            "TAKE_PROFIT_PCT": 4.0,
            
            # 系统配置
            "TEST_MODE": True,
            "ENABLE_TRADING": False,
            "LOG_LEVEL": "INFO",
            
            # 风险管理
            "MAX_POSITION_SIZE": 100.0,
            "RISK_PERCENT": 4.0,
            
            # 界面配置
            "THEME": "default",
            "AUTO_SAVE": True,
            "WINDOW_GEOMETRY": "1200x800+100+100"
        }
        
        try:
            with open(self.wmzc_config_path, 'w', encoding='utf-8') as f:
                json.dump(perfect_config, f, indent=2, ensure_ascii=False)
            
            print("✅ 完美的wmzc_config.json已创建")
            return True
            
        except Exception as e:
            print(f"❌ 创建wmzc_config.json失败: {e}")
            return False
    
    def create_perfect_trading_config(self):
        """创建完美的trading_config.json"""
        print("🔧 创建完美的trading_config.json...")
        
        # 只包含系统期望的标准字段
        perfect_config = {
            # 基本交易配置
            "SYMBOL": "BTC-USDT-SWAP",
            "EXCHANGE": "OKX",
            "TIMEFRAME": "1m",
            
            # API配置
            "API_KEY": "da636867-490f-4e3e-81b2-870841afb860",
            "SECRET_KEY": "C15B6EE0CF3FFDEE5834865D3839325E",
            "PASSPHRASE": "Mx123456@",
            
            # OKX专用配置
            "OKX_API_KEY": "da636867-490f-4e3e-81b2-870841afb860",
            "OKX_SECRET_KEY": "C15B6EE0CF3FFDEE5834865D3839325E",
            "OKX_PASSPHRASE": "Mx123456@",
            
            # Gate.io专用配置
            "GATE_API_KEY": "d5ea5faa068d66204bb68b75201c56d5",
            "GATE_SECRET_KEY": "5b516e55788fba27e61f9bd06b22ab3661b3115797076d5e73199bea3a8afb1c",
            
            # 交易参数
            "ORDER_AMOUNT": 5.0,
            "ORDER_USDT_AMOUNT": 10.0,
            "LEVERAGE": 3,
            "RISK_PERCENT": 4.0,
            
            # 风险管理
            "ENABLE_STOP_LOSS": True,
            "STOP_LOSS_PCT": 1.0,
            "PROFIT_TARGET_PCT": 2.0,
            
            # 系统配置
            "ENABLE_TRADING": False,
            "TEST_MODE": True,
            
            # 策略启用状态
            "ENABLE_KDJ": True,
            "ENABLE_MACD": True,
            "ENABLE_PINBAR": True,
            "ENABLE_ADVANCED_MACD": False,
            
            # 技术指标参数
            "KDJ": {
                "LOW": -2.0,
                "HIGH": 90.0,
                "window": 14,
                "smooth_window": 3
            },
            
            "MACD": {
                "SHORT": 12,
                "LONG": 26,
                "SIGNAL": 9
            },
            
            "PINBAR": {
                "DROP_TIME_WINDOW": 2,
                "DROP_PERCENT": 5.0,
                "REBOUND_PERCENT": 1.0,
                "RISE_TIME_WINDOW": 2,
                "RISE_PERCENT": 5.0,
                "FALL_PERCENT": 1.0,
                "TAKE_PROFIT_PCT": 50.0
            },
            
            # 推送配置
            "PUSH_MACD_SIGNAL": True,
            "PUSH_KDJ_SIGNAL": False,
            "PUSH_PINBAR_SIGNAL": False,
            "PUSH_RSI_SIGNAL": True,
            "PUSH_BUY_SIGNAL": True,
            "PUSH_SELL_SIGNAL": True,
            "ENABLE_WECHAT_PUSH": False,
            "WECHAT_SENDKEY": "",
            
            # 界面配置
            "window_geometry": "1382x800+77+32",
            "current_tab": 0,
            "auto_refresh_news": False,
            "news_refresh_interval": 3,
            
            # 自动恢复配置
            "AUTO_RECOVERY": {
                "enabled": True,
                "timeout_minutes": 1,
                "max_restarts": 3
            }
        }
        
        try:
            with open(self.trading_config_path, 'w', encoding='utf-8') as f:
                json.dump(perfect_config, f, indent=2, ensure_ascii=False)
            
            print("✅ 完美的trading_config.json已创建")
            return True
            
        except Exception as e:
            print(f"❌ 创建trading_config.json失败: {e}")
            return False
    
    def validate_final_configs(self):
        """验证最终配置"""
        print("🔍 验证最终配置...")
        
        try:
            # 验证wmzc_config.json
            with open(self.wmzc_config_path, 'r', encoding='utf-8') as f:
                wmzc_config = json.load(f)
            
            api_key = wmzc_config.get("API_KEY", "")
            passphrase = wmzc_config.get("PASSPHRASE", "")
            
            print(f"✅ wmzc_config.json 字段数: {len(wmzc_config)}")
            print(f"✅ API_KEY 长度: {len(api_key)} (>= 10)")
            print(f"✅ PASSPHRASE 长度: {len(passphrase)} (>= 3)")
            
            # 验证trading_config.json
            with open(self.trading_config_path, 'r', encoding='utf-8') as f:
                trading_config = json.load(f)
            
            print(f"✅ trading_config.json 字段数: {len(trading_config)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证配置失败: {e}")
            return False
    
    def run_optimization(self):
        """运行最终优化"""
        print("🚀 开始最终配置优化...")
        
        success = True
        success &= self.create_perfect_wmzc_config()
        success &= self.create_perfect_trading_config()
        
        if not success:
            return False
        
        if not self.validate_final_configs():
            return False
        
        print("🎉 最终配置优化完成！")
        print("📋 优化结果:")
        print("   ✅ 创建了标准化的配置文件")
        print("   ✅ 移除了所有未知字段")
        print("   ✅ 设置了正确的API密钥长度")
        print("   ✅ 优化了配置文件结构")
        
        return True

def main():
    """主函数"""
    print("=" * 80)
    print("🔧 WMZC最终配置优化工具")
    print("彻底解决所有配置警告，创建完美的配置文件")
    print("=" * 80)
    
    optimizer = FinalConfigOptimizer()
    
    try:
        success = optimizer.run_optimization()
        
        if success:
            print("\n✅ 最终配置优化成功！")
            print("💡 现在重新启动系统，所有配置警告都应该消失")
            return True
        else:
            print("\n❌ 最终配置优化失败")
            return False
            
    except Exception as e:
        print(f"\n💥 优化异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
