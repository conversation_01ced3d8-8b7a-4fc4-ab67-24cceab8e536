#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate.io API优化功能使用示例
演示如何在WMZC系统中使用Gate.io API优化功能
"""

import asyncio
import time
import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def log_example(message, level="INFO"):
    """示例日志函数"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

class GateIOOptimizationExample:
    """Gate.io API优化功能使用示例"""
    
    def __init__(self):
        self.optimizer = None
        self.wmzc_app = None
    
    async def run_examples(self):
        """运行所有示例"""
        log_example("=" * 80)
        log_example("🚀 Gate.io API优化功能使用示例")
        log_example("=" * 80)
        
        try:
            # 示例1: 初始化优化器
            await self.example_1_initialize_optimizer()
            
            # 示例2: 智能订单队列使用
            await self.example_2_smart_order_queue()
            
            # 示例3: 数据缓存使用
            await self.example_3_data_caching()
            
            # 示例4: 优化的API调用
            await self.example_4_optimized_api_calls()
            
            # 示例5: 性能监控
            await self.example_5_performance_monitoring()
            
            # 示例6: 配置管理
            await self.example_6_configuration_management()
            
            log_example("✅ 所有示例执行完成")
            
        except Exception as e:
            log_example(f"❌ 示例执行失败: {e}", "ERROR")
            import traceback
            traceback.print_exc()
    
    async def example_1_initialize_optimizer(self):
        """示例1: 初始化优化器"""
        log_example("📋 示例1: 初始化Gate.io API优化器", "INFO")
        log_example("-" * 50)
        
        try:
            # 导入WMZC模块
            import WMZC
            
            # 创建优化器实例
            log_example("🔧 创建GateIOAPIOptimizer实例...", "INFO")
            self.optimizer = WMZC.GateIOAPIOptimizer()
            
            # 初始化优化器
            log_example("⚡ 初始化优化器...", "INFO")
            success = await self.optimizer.initialize()
            
            if success:
                log_example("✅ 优化器初始化成功", "INFO")
                
                # 显示配置信息
                config = self.optimizer.config
                log_example(f"📊 优化器配置:", "INFO")
                log_example(f"  - 订单批量处理: {'启用' if config['enable_order_batching'] else '禁用'}", "INFO")
                log_example(f"  - 数据缓存: {'启用' if config['enable_data_caching'] else '禁用'}", "INFO")
                log_example(f"  - 连接池: {'启用' if config['enable_connection_pooling'] else '禁用'}", "INFO")
                log_example(f"  - 安全增强: {'启用' if config['enable_security_enhancement'] else '禁用'}", "INFO")
            else:
                log_example("❌ 优化器初始化失败", "ERROR")
                
        except Exception as e:
            log_example(f"❌ 示例1执行失败: {e}", "ERROR")
    
    async def example_2_smart_order_queue(self):
        """示例2: 智能订单队列使用"""
        log_example("📋 示例2: 智能订单队列使用", "INFO")
        log_example("-" * 50)
        
        try:
            if not self.optimizer:
                log_example("⚠️ 优化器未初始化，跳过示例", "WARNING")
                return
            
            import WMZC
            
            # 创建多个测试订单
            log_example("📦 创建测试订单...", "INFO")
            test_orders = []
            
            for i in range(8):
                order = WMZC.OrderRequest(
                    symbol="BTC_USDT",
                    side="buy" if i % 2 == 0 else "sell",
                    amount=10.0 + i,
                    order_type="market"
                )
                test_orders.append(order)
            
            # 模拟快速提交订单（触发批量处理）
            log_example("🚀 快速提交订单（触发批量处理）...", "INFO")
            start_time = time.time()
            
            # 并发提交订单
            tasks = []
            for order in test_orders:
                task = self.optimizer.order_queue.add_order(order)
                tasks.append(task)
            
            # 等待所有订单处理完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # 分析结果
            successful_orders = sum(1 for result in results if isinstance(result, dict) and result.get('success'))
            processing_time = end_time - start_time
            
            log_example(f"📊 订单处理结果:", "INFO")
            log_example(f"  - 提交订单数: {len(test_orders)}", "INFO")
            log_example(f"  - 成功处理数: {successful_orders}", "INFO")
            log_example(f"  - 处理时间: {processing_time:.3f}秒", "INFO")
            
            # 获取队列统计
            queue_stats = self.optimizer.order_queue.get_stats()
            log_example(f"📈 队列统计:", "INFO")
            log_example(f"  - 总订单数: {queue_stats['total_orders']}", "INFO")
            log_example(f"  - 批量处理数: {queue_stats['batched_orders']}", "INFO")
            log_example(f"  - API调用节省: {queue_stats['api_calls_saved']}", "INFO")
            
        except Exception as e:
            log_example(f"❌ 示例2执行失败: {e}", "ERROR")
    
    async def example_3_data_caching(self):
        """示例3: 数据缓存使用"""
        log_example("📋 示例3: 数据缓存使用", "INFO")
        log_example("-" * 50)
        
        try:
            if not self.optimizer:
                log_example("⚠️ 优化器未初始化，跳过示例", "WARNING")
                return
            
            cache = self.optimizer.data_cache
            
            # 测试数据
            test_data = {
                'ticker': {
                    'symbol': 'BTC_USDT',
                    'last': '50000',
                    'change_percentage': '2.5%',
                    'volume': '1000'
                },
                'kline': [
                    ['1640995200', '50000', '51000', '49000', '50500', '100'],
                    ['1640995260', '50500', '50800', '50200', '50600', '80']
                ],
                'balance': {
                    'USDT': {'available': '1000.0', 'locked': '0.0'},
                    'BTC': {'available': '0.02', 'locked': '0.0'}
                }
            }
            
            # 设置缓存数据
            log_example("💾 设置缓存数据...", "INFO")
            for cache_type, data in test_data.items():
                cache_key = f"example_{cache_type}"
                success = await cache.set(cache_type, cache_key, data)
                log_example(f"  - {cache_type}: {'成功' if success else '失败'}", "INFO")
            
            # 立即获取缓存数据（应该命中）
            log_example("📋 立即获取缓存数据...", "INFO")
            cache_hits = 0
            for cache_type, expected_data in test_data.items():
                cache_key = f"example_{cache_type}"
                cached_data = await cache.get(cache_type, cache_key)
                
                if cached_data == expected_data:
                    cache_hits += 1
                    log_example(f"  - {cache_type}: ✅ 缓存命中", "INFO")
                else:
                    log_example(f"  - {cache_type}: ❌ 缓存未命中", "ERROR")
            
            # 等待部分缓存过期
            log_example("⏳ 等待2秒（部分缓存过期）...", "INFO")
            await asyncio.sleep(2)
            
            # 再次获取缓存数据
            log_example("📋 再次获取缓存数据...", "INFO")
            for cache_type, expected_data in test_data.items():
                cache_key = f"example_{cache_type}"
                cached_data = await cache.get(cache_type, cache_key)
                
                if cached_data == expected_data:
                    log_example(f"  - {cache_type}: ✅ 缓存仍然有效", "INFO")
                else:
                    log_example(f"  - {cache_type}: ⏰ 缓存已过期", "INFO")
            
            # 获取缓存统计
            cache_stats = cache.get_cache_stats()
            log_example(f"📈 缓存统计:", "INFO")
            for cache_type, stats in cache_stats.items():
                log_example(f"  - {cache_type}: 大小={stats['size']}, 命中率={stats['hit_rate']}%", "INFO")
            
        except Exception as e:
            log_example(f"❌ 示例3执行失败: {e}", "ERROR")
    
    async def example_4_optimized_api_calls(self):
        """示例4: 优化的API调用"""
        log_example("📋 示例4: 优化的API调用", "INFO")
        log_example("-" * 50)
        
        try:
            if not self.optimizer:
                log_example("⚠️ 优化器未初始化，跳过示例", "WARNING")
                return
            
            # 测试优化的API调用
            test_endpoints = [
                '/spot/currencies',
                '/spot/currency_pairs', 
                '/spot/tickers?currency_pair=BTC_USDT'
            ]
            
            log_example("🌐 执行优化的API调用...", "INFO")
            
            for endpoint in test_endpoints:
                log_example(f"📡 调用: {endpoint}", "INFO")
                
                # 第一次调用（应该从API获取）
                start_time = time.time()
                result1 = await self.optimizer.optimized_api_call(endpoint, use_cache=True)
                time1 = time.time() - start_time
                
                # 第二次调用（应该从缓存获取）
                start_time = time.time()
                result2 = await self.optimizer.optimized_api_call(endpoint, use_cache=True)
                time2 = time.time() - start_time
                
                # 分析结果
                if result1.get('success') and result2.get('success'):
                    from_cache = result2.get('from_cache', False)
                    log_example(f"  - 第一次调用: {time1:.3f}秒", "INFO")
                    log_example(f"  - 第二次调用: {time2:.3f}秒 {'(缓存)' if from_cache else '(API)'}", "INFO")
                    
                    if from_cache and time2 < time1:
                        speedup = (time1 - time2) / time1 * 100
                        log_example(f"  - 性能提升: {speedup:.1f}%", "INFO")
                else:
                    log_example(f"  - API调用失败", "ERROR")
                
                log_example("", "INFO")  # 空行分隔
            
        except Exception as e:
            log_example(f"❌ 示例4执行失败: {e}", "ERROR")
    
    async def example_5_performance_monitoring(self):
        """示例5: 性能监控"""
        log_example("📋 示例5: 性能监控", "INFO")
        log_example("-" * 50)
        
        try:
            if not self.optimizer:
                log_example("⚠️ 优化器未初始化，跳过示例", "WARNING")
                return
            
            # 获取优化统计信息
            log_example("📊 获取优化统计信息...", "INFO")
            stats = self.optimizer.get_optimization_stats()
            
            # 显示性能指标
            performance_metrics = stats.get('performance_metrics', {})
            log_example("📈 性能指标:", "INFO")
            log_example(f"  - 总优化请求数: {performance_metrics.get('total_optimized_requests', 0)}", "INFO")
            log_example(f"  - API调用节省数: {performance_metrics.get('api_calls_saved', 0)}", "INFO")
            log_example(f"  - 缓存命中率: {performance_metrics.get('cache_hit_rate', 0):.2%}", "INFO")
            log_example(f"  - 平均响应时间: {performance_metrics.get('avg_response_time', 0):.3f}秒", "INFO")
            
            # 显示订单队列统计
            order_stats = stats.get('order_queue_stats', {})
            log_example("📦 订单队列统计:", "INFO")
            log_example(f"  - 总订单数: {order_stats.get('total_orders', 0)}", "INFO")
            log_example(f"  - 批量处理数: {order_stats.get('batched_orders', 0)}", "INFO")
            log_example(f"  - API调用节省: {order_stats.get('api_calls_saved', 0)}", "INFO")
            
            # 显示缓存统计
            cache_stats = stats.get('cache_stats', {})
            if cache_stats:
                log_example("💾 缓存统计:", "INFO")
                for cache_type, cache_info in cache_stats.items():
                    log_example(f"  - {cache_type}: 大小={cache_info.get('size', 0)}, "
                              f"命中率={cache_info.get('hit_rate', 0)}%", "INFO")
            
            # 显示连接池统计
            conn_stats = stats.get('connection_stats', {})
            if conn_stats:
                log_example("🔗 连接池统计:", "INFO")
                log_example(f"  - 总请求数: {conn_stats.get('total_requests', 0)}", "INFO")
                log_example(f"  - 活跃连接数: {conn_stats.get('active_connections', 0)}", "INFO")
                log_example(f"  - 失败请求数: {conn_stats.get('failed_requests', 0)}", "INFO")
                log_example(f"  - 平均响应时间: {conn_stats.get('avg_response_time', 0):.3f}秒", "INFO")
            
            # 显示安全统计
            security_stats = stats.get('security_stats', {})
            if security_stats:
                log_example("🛡️ 安全统计:", "INFO")
                log_example(f"  - 总请求数: {security_stats.get('total_requests', 0)}", "INFO")
                log_example(f"  - 拒绝请求数: {security_stats.get('rejected_requests', 0)}", "INFO")
                log_example(f"  - 阻止重放攻击: {security_stats.get('replay_attacks_blocked', 0)}", "INFO")
                log_example(f"  - 触发告警数: {security_stats.get('alerts_triggered', 0)}", "INFO")
            
        except Exception as e:
            log_example(f"❌ 示例5执行失败: {e}", "ERROR")
    
    async def example_6_configuration_management(self):
        """示例6: 配置管理"""
        log_example("📋 示例6: 配置管理", "INFO")
        log_example("-" * 50)
        
        try:
            if not self.optimizer:
                log_example("⚠️ 优化器未初始化，跳过示例", "WARNING")
                return
            
            # 显示当前配置
            log_example("⚙️ 当前优化器配置:", "INFO")
            config = self.optimizer.config
            for key, value in config.items():
                log_example(f"  - {key}: {value}", "INFO")
            
            # 动态修改配置
            log_example("🔧 动态修改配置...", "INFO")
            
            # 修改缓存TTL
            original_ticker_ttl = self.optimizer.data_cache.cache_configs['ticker']['ttl']
            self.optimizer.data_cache.cache_configs['ticker']['ttl'] = 2.0
            log_example(f"  - Ticker缓存TTL: {original_ticker_ttl}s -> 2.0s", "INFO")
            
            # 修改批量大小
            original_batch_size = self.optimizer.order_queue.max_batch_size
            self.optimizer.order_queue.max_batch_size = 15
            log_example(f"  - 最大批量大小: {original_batch_size} -> 15", "INFO")
            
            # 修改安全阈值
            original_failure_rate = self.optimizer.security_enhancer.max_failure_rate
            self.optimizer.security_enhancer.max_failure_rate = 0.08
            log_example(f"  - 最大失败率: {original_failure_rate} -> 0.08", "INFO")
            
            log_example("✅ 配置修改完成", "INFO")
            
            # 恢复原始配置
            log_example("🔄 恢复原始配置...", "INFO")
            self.optimizer.data_cache.cache_configs['ticker']['ttl'] = original_ticker_ttl
            self.optimizer.order_queue.max_batch_size = original_batch_size
            self.optimizer.security_enhancer.max_failure_rate = original_failure_rate
            log_example("✅ 配置恢复完成", "INFO")
            
        except Exception as e:
            log_example(f"❌ 示例6执行失败: {e}", "ERROR")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.optimizer:
                await self.optimizer.cleanup()
                log_example("🧹 优化器资源清理完成", "INFO")
        except Exception as e:
            log_example(f"⚠️ 资源清理失败: {e}", "WARNING")

async def main():
    """主函数"""
    example = GateIOOptimizationExample()
    try:
        await example.run_examples()
    finally:
        await example.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
