{"exchange_selection": "OKX", "okx_api_key": "", "okx_secret_key": "", "okx_passphrase": "", "gate_api_key": "", "gate_secret_key": "", "default_amount": "10.0", "stop_loss_percent": "2.0", "take_profit_percent": "4.0", "max_position_size": "100.0", "rsi_period": "14", "rsi_overbought": "70", "rsi_oversold": "30", "rsi_amount": "10.0", "macd_fast": "12", "macd_slow": "26", "macd_signal": "9", "macd_amount": "10.0", "kdj_period": "9", "kdj_k_period": "3", "kdj_d_period": "3", "kdj_amount": "10.0", "bollinger_period": "20", "bollinger_std": "2", "bollinger_amount": "10.0", "ema_fast": "12", "ema_slow": "26", "ema_amount": "10.0", "rsi_strategy_enabled": false, "macd_strategy_enabled": false, "kdj_strategy_enabled": false, "bollinger_strategy_enabled": false, "ema_strategy_enabled": false, "advanced_macd_strategy_enabled": false, "window_geometry": "1200x800+100+100", "current_tab": 0, "theme": "default", "font_size": "10", "log_level": "INFO", "update_interval": "1000", "auto_save": true, "notification_enabled": true, "auto_refresh_news": false, "news_refresh_interval": 3, "deepseek_api_key": "", "ai_features": {"sentiment_analysis": true, "signal_enhancement": true, "daily_cost_limit": 20.0, "timeout": 5.0}, "free_api_sources": [{"name": "laozhang_free", "api_key": "", "base_url": "https://api.laozhang.ai/v1", "model": "deepseek-r1", "priority": 1, "free_quota": 100.0, "enabled": true, "description": "老张API - $100免费额度", "timeout": 30, "retry_count": 3}, {"name": "huggingface_free_1", "api_key": "", "base_url": "https://api-inference.huggingface.co/models", "model": "deepseek-ai/deepseek-coder-33b-instruct", "priority": 2, "monthly_calls_limit": 30000, "enabled": true, "description": "Hugging Face 1 - 30,000次/月免费", "timeout": 30, "retry_count": 3}, {"name": "huggingface_free_2", "api_key": "", "base_url": "https://api-inference.huggingface.co/models", "model": "deepseek-ai/deepseek-coder-7b-instruct", "priority": 3, "monthly_calls_limit": 30000, "enabled": true, "description": "Hugging Face 2 - 30,000次/月免费", "timeout": 30, "retry_count": 3}], "gate_io_optimization": {"enable_order_batching": true, "enable_data_caching": true, "enable_connection_pooling": true, "enable_websocket_balancing": true, "enable_security_enhancement": true, "batch_interval": 0.1, "max_batch_size": 20, "max_concurrent_requests": 20, "cache_ttl_ticker": 1.0, "cache_ttl_kline": 5.0, "cache_ttl_balance": 10.0, "cache_ttl_orderbook": 0.5, "cache_ttl_trades": 2.0, "max_connections": 5, "connection_timeout": 30.0, "max_websocket_connections": 3, "timestamp_tolerance": 30.0, "max_failure_rate": 0.05, "max_response_time": 3.0, "enable_performance_monitoring": true, "performance_log_interval": 60}, "STOP_LOSS_PCT": 1.0, "TAKE_PROFIT_PCT": 2.0, "MAX_POSITION_SIZE": 1000.0, "RISK_PER_TRADE": 2.0}