#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 最终配置持久化解决方案
彻底解决重启后需要重新设置API密钥的问题
"""

import os
import json
import shutil
from datetime import datetime

class FinalPersistenceSolution:
    """最终持久化解决方案"""
    
    def __init__(self):
        self.config_files = ['trading_config.json', 'wmzc_config.json']
        self.fixes_applied = 0
        
    def run_final_solution(self):
        """运行最终解决方案"""
        print("🎯 最终配置持久化解决方案")
        print("=" * 60)
        
        # 1. 检查当前状态
        self.check_current_state()
        
        # 2. 清理测试配置，设置真实配置模板
        self.setup_real_config_template()
        
        # 3. 修复配置同步问题
        self.fix_config_sync_issue()
        
        # 4. 创建配置验证机制
        self.create_config_verification()
        
        # 5. 提供使用指南
        self.provide_usage_guide()
        
        return self.fixes_applied > 0
    
    def check_current_state(self):
        """检查当前状态"""
        print("\n📋 检查当前配置状态...")
        
        for config_file in self.config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    print(f"\n📄 {config_file}:")
                    
                    # 检查API配置
                    if config_file == 'trading_config.json':
                        api_key = config.get('API_KEY', '')
                        api_secret = config.get('API_SECRET', '')
                        passphrase = config.get('PASSPHRASE', '')
                    else:
                        api_key = config.get('okx_api_key', '')
                        api_secret = config.get('okx_secret_key', '')
                        passphrase = config.get('okx_passphrase', '')
                    
                    if api_key and api_secret and passphrase:
                        if 'test_final_api_key' in api_key:
                            print(f"  ⚠️ 包含测试API配置: {api_key[:20]}...")
                            print(f"  💡 这是测试配置，需要替换为真实API密钥")
                        else:
                            print(f"  ✅ 包含API配置: {api_key[:15]}...")
                    else:
                        print(f"  ❌ API配置缺失或不完整")
                    
                    # 检查保护标记
                    if config.get('_CONFIG_PROTECTED'):
                        print(f"  ✅ 配置保护已启用")
                    else:
                        print(f"  ❌ 配置保护未启用")
                    
                except Exception as e:
                    print(f"  ❌ 读取失败: {e}")
            else:
                print(f"\n📄 {config_file}: ❌ 文件不存在")
    
    def setup_real_config_template(self):
        """设置真实配置模板"""
        print("\n🔧 设置真实配置模板...")
        
        # 创建trading_config.json模板
        trading_config_template = {
            # API配置 - 用户需要填写真实值
            "API_KEY": "",
            "API_SECRET": "",
            "PASSPHRASE": "",
            "OKX_API_KEY": "",
            "OKX_SECRET_KEY": "",
            "OKX_PASSPHRASE": "",
            
            # 基本交易配置
            "EXCHANGE": "OKX",
            "SYMBOL": "BTC-USDT-SWAP",
            "TIMEFRAME": "1m",
            "ORDER_USDT_AMOUNT": 10,
            "LEVERAGE": 3,
            "RISK_PERCENT": 1.0,
            
            # 策略配置
            "ENABLE_KDJ": True,
            "ENABLE_MACD": True,
            "ENABLE_PINBAR": True,
            "ENABLE_RSI": True,
            
            # 系统配置
            "LOG_LEVEL": "INFO",
            "TEST_MODE": True,
            "ENABLE_TRADING": False,
            
            # 保护标记
            "_CONFIG_PROTECTED": True,
            "_FINAL_SOLUTION_APPLIED": datetime.now().isoformat(),
            "_DO_NOT_OVERRIDE": True,
            "_USER_CONFIG_TEMPLATE": True
        }
        
        # 创建wmzc_config.json模板
        wmzc_config_template = {
            "exchange_selection": "OKX",
            "okx_api_key": "",
            "okx_secret_key": "",
            "okx_passphrase": "",
            "default_symbol": "BTC-USDT-SWAP",
            "default_timeframe": "1m",
            
            # 保护标记
            "_CONFIG_PROTECTED": True,
            "_FINAL_SOLUTION_APPLIED": datetime.now().isoformat(),
            "_DO_NOT_OVERRIDE": True,
            "_USER_CONFIG_TEMPLATE": True
        }
        
        # 检查是否有现有的API配置需要保留
        existing_api_config = {}
        
        # 从现有文件中提取API配置
        for config_file in self.config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    if config_file == 'trading_config.json':
                        api_key = config.get('API_KEY', '')
                        api_secret = config.get('API_SECRET', '')
                        passphrase = config.get('PASSPHRASE', '')
                    else:
                        api_key = config.get('okx_api_key', '')
                        api_secret = config.get('okx_secret_key', '')
                        passphrase = config.get('okx_passphrase', '')
                    
                    # 只保留非测试的API配置
                    if api_key and api_secret and passphrase and 'test_final_api_key' not in api_key:
                        existing_api_config = {
                            'API_KEY': api_key,
                            'API_SECRET': api_secret,
                            'PASSPHRASE': passphrase
                        }
                        print(f"  ✅ 从 {config_file} 提取到真实API配置")
                        break
                        
                except Exception as e:
                    print(f"  ❌ 提取 {config_file} API配置失败: {e}")
        
        # 如果有真实API配置，应用到模板
        if existing_api_config:
            trading_config_template.update(existing_api_config)
            trading_config_template['OKX_API_KEY'] = existing_api_config['API_KEY']
            trading_config_template['OKX_SECRET_KEY'] = existing_api_config['API_SECRET']
            trading_config_template['OKX_PASSPHRASE'] = existing_api_config['PASSPHRASE']
            
            wmzc_config_template['okx_api_key'] = existing_api_config['API_KEY']
            wmzc_config_template['okx_secret_key'] = existing_api_config['API_SECRET']
            wmzc_config_template['okx_passphrase'] = existing_api_config['PASSPHRASE']
            
            print(f"  ✅ 真实API配置已应用到模板")
        else:
            print(f"  ⚠️ 未找到真实API配置，将创建空模板")
        
        # 保存配置模板
        try:
            with open('trading_config.json', 'w', encoding='utf-8') as f:
                json.dump(trading_config_template, f, indent=2, ensure_ascii=False)
            print(f"  ✅ trading_config.json 模板已创建")
            self.fixes_applied += 1
        except Exception as e:
            print(f"  ❌ 创建 trading_config.json 模板失败: {e}")
        
        try:
            with open('wmzc_config.json', 'w', encoding='utf-8') as f:
                json.dump(wmzc_config_template, f, indent=2, ensure_ascii=False)
            print(f"  ✅ wmzc_config.json 模板已创建")
            self.fixes_applied += 1
        except Exception as e:
            print(f"  ❌ 创建 wmzc_config.json 模板失败: {e}")
    
    def fix_config_sync_issue(self):
        """修复配置同步问题"""
        print("\n🔧 修复配置同步问题...")
        
        # 创建配置同步脚本
        sync_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 配置同步脚本
确保trading_config.json和wmzc_config.json中的API配置保持一致
"""

import os
import json
from datetime import datetime

def sync_api_config():
    """同步API配置"""
    print("🔄 开始同步API配置...")
    
    # 读取两个配置文件
    trading_config = {}
    wmzc_config = {}
    
    if os.path.exists('trading_config.json'):
        try:
            with open('trading_config.json', 'r', encoding='utf-8') as f:
                trading_config = json.load(f)
        except Exception as e:
            print(f"❌ 读取trading_config.json失败: {e}")
    
    if os.path.exists('wmzc_config.json'):
        try:
            with open('wmzc_config.json', 'r', encoding='utf-8') as f:
                wmzc_config = json.load(f)
        except Exception as e:
            print(f"❌ 读取wmzc_config.json失败: {e}")
    
    # 确定主要的API配置源
    trading_api = (trading_config.get('API_KEY', ''),
                  trading_config.get('API_SECRET', ''),
                  trading_config.get('PASSPHRASE', ''))
    
    wmzc_api = (wmzc_config.get('okx_api_key', ''),
               wmzc_config.get('okx_secret_key', ''),
               wmzc_config.get('okx_passphrase', ''))
    
    # 选择更完整的API配置
    if all(trading_api) and not all(wmzc_api):
        # 使用trading_config的API配置
        master_api = {
            'API_KEY': trading_api[0],
            'API_SECRET': trading_api[1],
            'PASSPHRASE': trading_api[2]
        }
        print("✅ 使用trading_config.json的API配置作为主配置")
    elif all(wmzc_api) and not all(trading_api):
        # 使用wmzc_config的API配置
        master_api = {
            'API_KEY': wmzc_api[0],
            'API_SECRET': wmzc_api[1],
            'PASSPHRASE': wmzc_api[2]
        }
        print("✅ 使用wmzc_config.json的API配置作为主配置")
    elif all(trading_api) and all(wmzc_api):
        # 两个都有，使用最近修改的
        trading_mtime = os.path.getmtime('trading_config.json') if os.path.exists('trading_config.json') else 0
        wmzc_mtime = os.path.getmtime('wmzc_config.json') if os.path.exists('wmzc_config.json') else 0
        
        if trading_mtime >= wmzc_mtime:
            master_api = {
                'API_KEY': trading_api[0],
                'API_SECRET': trading_api[1],
                'PASSPHRASE': trading_api[2]
            }
            print("✅ 使用trading_config.json的API配置（更新）")
        else:
            master_api = {
                'API_KEY': wmzc_api[0],
                'API_SECRET': wmzc_api[1],
                'PASSPHRASE': wmzc_api[2]
            }
            print("✅ 使用wmzc_config.json的API配置（更新）")
    else:
        print("⚠️ 两个配置文件都没有完整的API配置")
        return False
    
    # 同步到两个文件
    try:
        # 更新trading_config.json
        trading_config.update(master_api)
        trading_config['OKX_API_KEY'] = master_api['API_KEY']
        trading_config['OKX_SECRET_KEY'] = master_api['API_SECRET']
        trading_config['OKX_PASSPHRASE'] = master_api['PASSPHRASE']
        trading_config['_LAST_SYNC'] = datetime.now().isoformat()
        
        with open('trading_config.json', 'w', encoding='utf-8') as f:
            json.dump(trading_config, f, indent=2, ensure_ascii=False)
        
        print("✅ trading_config.json 已同步")
        
        # 更新wmzc_config.json
        wmzc_config['okx_api_key'] = master_api['API_KEY']
        wmzc_config['okx_secret_key'] = master_api['API_SECRET']
        wmzc_config['okx_passphrase'] = master_api['PASSPHRASE']
        wmzc_config['_LAST_SYNC'] = datetime.now().isoformat()
        
        with open('wmzc_config.json', 'w', encoding='utf-8') as f:
            json.dump(wmzc_config, f, indent=2, ensure_ascii=False)
        
        print("✅ wmzc_config.json 已同步")
        
        print(f"🎉 API配置同步完成: {master_api['API_KEY'][:15]}...")
        return True
        
    except Exception as e:
        print(f"❌ 同步API配置失败: {e}")
        return False

if __name__ == "__main__":
    sync_api_config()
'''
        
        try:
            with open('sync_api_config.py', 'w', encoding='utf-8') as f:
                f.write(sync_script)
            print("  ✅ 配置同步脚本已创建: sync_api_config.py")
            
            # 立即运行同步
            exec(sync_script.split('if __name__ == "__main__":')[0] + 'sync_api_config()')
            
            self.fixes_applied += 1
            
        except Exception as e:
            print(f"  ❌ 创建配置同步脚本失败: {e}")
    
    def create_config_verification(self):
        """创建配置验证机制"""
        print("\n🔍 创建配置验证机制...")
        
        verification_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 配置验证脚本
验证API配置是否正确保存和加载
"""

import os
import json

def verify_config():
    """验证配置"""
    print("🔍 开始验证配置...")
    
    issues = []
    
    # 检查配置文件
    config_files = ['trading_config.json', 'wmzc_config.json']
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                print(f"\\n📄 {config_file}:")
                
                # 检查API配置
                if config_file == 'trading_config.json':
                    api_key = config.get('API_KEY', '')
                    api_secret = config.get('API_SECRET', '')
                    passphrase = config.get('PASSPHRASE', '')
                else:
                    api_key = config.get('okx_api_key', '')
                    api_secret = config.get('okx_secret_key', '')
                    passphrase = config.get('okx_passphrase', '')
                
                if api_key and api_secret and passphrase:
                    print(f"  ✅ API配置完整: {api_key[:15]}...")
                else:
                    print(f"  ❌ API配置不完整")
                    issues.append(f"{config_file} API配置不完整")
                
                # 检查保护标记
                if config.get('_CONFIG_PROTECTED'):
                    print(f"  ✅ 配置保护已启用")
                else:
                    print(f"  ❌ 配置保护未启用")
                    issues.append(f"{config_file} 配置保护未启用")
                
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")
                issues.append(f"读取 {config_file} 失败: {e}")
        else:
            print(f"\\n📄 {config_file}: ❌ 文件不存在")
            issues.append(f"{config_file} 文件不存在")
    
    if issues:
        print(f"\\n❌ 发现 {len(issues)} 个问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        return False
    else:
        print(f"\\n✅ 配置验证通过！")
        return True

if __name__ == "__main__":
    verify_config()
'''
        
        try:
            with open('verify_config.py', 'w', encoding='utf-8') as f:
                f.write(verification_script)
            print("  ✅ 配置验证脚本已创建: verify_config.py")
            self.fixes_applied += 1
        except Exception as e:
            print(f"  ❌ 创建配置验证脚本失败: {e}")
    
    def provide_usage_guide(self):
        """提供使用指南"""
        print("\n📖 使用指南...")
        
        guide = """
🎯 WMZC配置持久化最终解决方案使用指南

📋 问题解决步骤:

1. 🔧 配置API密钥
   - 启动WMZC系统: python "2019启动ZC.py"
   - 在"主配置"页面填写您的真实OKX API密钥:
     * API_KEY: 您的API密钥
     * API_SECRET: 您的API密钥密码  
     * PASSPHRASE: 您的API密钥口令
   - 点击"💾 保存配置"按钮

2. 🔄 同步配置
   - 运行配置同步: python sync_api_config.py
   - 确保两个配置文件的API配置一致

3. ✅ 验证配置
   - 运行配置验证: python verify_config.py
   - 确认API配置正确保存

4. 🔄 重启测试
   - 完全关闭WMZC系统
   - 重新启动: python "2019启动ZC.py"
   - 检查API配置是否自动加载

💡 如果仍有问题:

1. 检查控制台输出，查找配置加载相关日志
2. 确认配置文件权限正常
3. 运行 python verify_config.py 检查配置状态
4. 重新运行 python sync_api_config.py 同步配置

🛡️ 保护机制:
- 配置文件已添加保护标记
- 自动同步机制确保配置一致性
- 验证机制确保配置正确性
"""
        
        try:
            with open('CONFIG_USAGE_GUIDE.md', 'w', encoding='utf-8') as f:
                f.write(guide)
            print("  ✅ 使用指南已创建: CONFIG_USAGE_GUIDE.md")
        except Exception as e:
            print(f"  ❌ 创建使用指南失败: {e}")

def main():
    """主函数"""
    print("🎯 WMZC配置持久化最终解决方案")
    print("=" * 60)
    
    solution = FinalPersistenceSolution()
    
    try:
        success = solution.run_final_solution()
        
        print("\n" + "=" * 60)
        if success:
            print(f"🎉 最终解决方案完成！共应用了 {solution.fixes_applied} 个修复")
            print("\n💡 解决方案内容:")
            print("  1. ✅ 清理了测试配置，创建了真实配置模板")
            print("  2. ✅ 创建了配置同步机制")
            print("  3. ✅ 建立了配置验证机制")
            print("  4. ✅ 提供了详细的使用指南")
            
            print("\n🚀 现在请按照以下步骤操作:")
            print("  1. 重新启动WMZC系统")
            print("  2. 在主配置页面填写真实的API密钥")
            print("  3. 保存配置")
            print("  4. 运行 python sync_api_config.py 同步配置")
            print("  5. 运行 python verify_config.py 验证配置")
            print("  6. 重启系统验证配置持久化效果")
            
        else:
            print("❌ 最终解决方案执行失败")
            print("💡 请检查错误信息并手动修复")
        
        return success
        
    except Exception as e:
        print(f"❌ 执行过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
