#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 WMZC现代化GUI界面
基于CustomTkinter的现代化界面设计，支持暗色主题、响应式布局和动画效果
保持所有原有功能完整性的同时提升用户体验
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import json
import os
import sys
from typing import Dict, List, Optional, Any, Callable
import asyncio
from datetime import datetime

# 设置CustomTkinter外观
ctk.set_appearance_mode("dark")  # 默认暗色主题
ctk.set_default_color_theme("blue")  # 蓝色主题

class ModernWMZCGUI:
    """WMZC现代化GUI主类"""
    
    def __init__(self):
        self.root = None
        self.sidebar = None
        self.main_content = None
        self.current_tab = None
        self.tabs = {}
        self.tab_frames = {}
        self.theme_mode = "dark"
        
        # 原WMZC实例引用
        self.wmzc_instance = None

        # 动画控制
        self.animation_speed = 200
        self.fade_steps = 10
        self.animation_queue = []
        self.is_animating = False

        # 响应式布局控制
        self.window_width = 1400
        self.window_height = 900
        self.sidebar_width = 280
        self.is_sidebar_collapsed = False

        # 连接测试控制
        self.is_testing_connection = False
        self.is_testing_ai_connection = False
        self.test_connection_lock = threading.Lock()
        self.test_ai_connection_lock = threading.Lock()
        
        self.setup_main_window()
        self.setup_sidebar()
        self.setup_main_content()
        self.setup_status_bar()
        self.create_all_tabs()

        # 加载窗口状态
        self.load_window_state()
        
    def setup_main_window(self):
        """设置主窗口"""
        self.root = ctk.CTk()
        self.root.title("🚀 WMZC量化交易系统 - 现代化版本")
        self.root.geometry(f"{self.window_width}x{self.window_height}")
        self.root.minsize(1200, 800)

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("wmzc_icon.ico")
        except (tk.TclError, FileNotFoundError):
            # 图标文件不存在或格式错误，使用默认图标
            pass

        # 配置网格权重
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(0, weight=1)

        # 绑定窗口大小变化事件
        self.root.bind("<Configure>", self.on_window_resize)

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_sidebar(self):
        """设置侧边栏导航"""
        self.sidebar = ctk.CTkFrame(self.root, width=self.sidebar_width, corner_radius=0)
        self.sidebar.grid(row=0, column=0, rowspan=2, sticky="nsew")
        self.sidebar.grid_rowconfigure(20, weight=1)  # 让底部按钮区域可伸缩
        self.sidebar.grid_propagate(False)  # 防止自动调整大小
        
        # 标题
        title_label = ctk.CTkLabel(
            self.sidebar, 
            text="🚀 WMZC量化交易",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        
        # 主题切换按钮
        self.theme_button = ctk.CTkButton(
            self.sidebar,
            text="🌙 暗色主题",
            command=self.toggle_theme,
            width=200,
            height=32
        )
        self.theme_button.grid(row=1, column=0, padx=20, pady=10)
        
        # 标签页按钮列表
        self.tab_buttons = {}
        self.create_tab_buttons()
        
        # 底部状态信息
        self.create_sidebar_status()
        
    def create_tab_buttons(self):
        """创建标签页导航按钮"""
        tab_configs = [
            ("⚙️ 主配置", "main_config", "🔧"),
            ("🏪 策略赶集", "strategy_market", "🏪"),
            ("📈 交易记录", "trading_records", "📈"),
            ("📰 新闻资讯", "news", "📰"),
            ("📊 指标", "indicators", "📊"),
            ("🤖 AI", "ai", "🤖"),
            ("🎯 高级MACD", "advanced_macd", "🎯"),
            ("📉 插针策略", "pin_strategy", "📉"),
            ("📊 RSI策略", "rsi_strategy", "📊"),
            ("💰 止盈止损", "stop_profit_loss", "💰"),
            ("📈 等量加仓", "equal_position", "📈"),
            ("🏦 银行级风控", "risk_control", "🏦"),
            ("🔄 指标同步", "indicator_sync", "🔄"),
            ("📊 回测系统", "backtest", "📊"),
            ("🧪 参数优化", "optimization", "🧪"),
            ("🤖 LSTM预测", "lstm_prediction", "🤖"),
            ("🤖 AI助手", "ai_assistant", "🤖"),
            ("⚙️ 系统设置", "system_settings", "⚙️"),
            ("🔧 杂项配置", "misc_config", "🔧"),
            ("📜 日志控制台", "log_console", "📜")
        ]
        
        for i, (display_name, tab_id, icon) in enumerate(tab_configs):
            button = ctk.CTkButton(
                self.sidebar,
                text=f"{icon} {display_name.split(' ', 1)[1]}",  # 移除emoji，使用icon
                command=lambda tid=tab_id: self.switch_tab(tid),
                width=240,
                height=36,
                anchor="w",
                font=ctk.CTkFont(size=13)
            )
            button.grid(row=i+2, column=0, padx=20, pady=2, sticky="ew")
            self.tab_buttons[tab_id] = button
            
    def create_sidebar_status(self):
        """创建侧边栏状态信息"""
        # 分隔线
        separator = ctk.CTkFrame(self.sidebar, height=2)
        separator.grid(row=22, column=0, padx=20, pady=10, sticky="ew")
        
        # 连接状态
        self.connection_status = ctk.CTkLabel(
            self.sidebar,
            text="🔴 未连接",
            font=ctk.CTkFont(size=12)
        )
        self.connection_status.grid(row=23, column=0, padx=20, pady=5)
        
        # 系统状态
        self.system_status = ctk.CTkLabel(
            self.sidebar,
            text="⏸️ 系统待机",
            font=ctk.CTkFont(size=12)
        )
        self.system_status.grid(row=24, column=0, padx=20, pady=5)
        
        # 版本信息
        version_label = ctk.CTkLabel(
            self.sidebar,
            text="v2.0 Modern",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        version_label.grid(row=25, column=0, padx=20, pady=(5, 20))
        
    def setup_main_content(self):
        """设置主内容区域"""
        self.main_content = ctk.CTkFrame(self.root, corner_radius=0)
        self.main_content.grid(row=0, column=1, sticky="nsew", padx=(0, 0))
        self.main_content.grid_columnconfigure(0, weight=1)
        self.main_content.grid_rowconfigure(0, weight=1)
        
        # 内容标题栏
        self.title_frame = ctk.CTkFrame(self.main_content, height=60, corner_radius=0)
        self.title_frame.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        self.title_frame.grid_columnconfigure(0, weight=1)
        
        self.content_title = ctk.CTkLabel(
            self.title_frame,
            text="欢迎使用WMZC量化交易系统",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.content_title.grid(row=0, column=0, padx=20, pady=15, sticky="w")
        
        # 主内容滚动区域
        self.content_frame = ctk.CTkScrollableFrame(self.main_content)
        self.content_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=10)
        self.content_frame.grid_columnconfigure(0, weight=1)
        
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = ctk.CTkFrame(self.root, height=30, corner_radius=0)
        self.status_bar.grid(row=1, column=1, sticky="ew", padx=0, pady=0)
        self.status_bar.grid_columnconfigure(1, weight=1)
        
        # 状态信息
        self.status_label = ctk.CTkLabel(
            self.status_bar,
            text="就绪",
            font=ctk.CTkFont(size=11)
        )
        self.status_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        # 时间显示
        self.time_label = ctk.CTkLabel(
            self.status_bar,
            text="",
            font=ctk.CTkFont(size=11)
        )
        self.time_label.grid(row=0, column=2, padx=10, pady=5, sticky="e")
        
        # 启动时间更新
        self.update_time()
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.configure(text=current_time)
        self.root.after(1000, self.update_time)
        
    def toggle_theme(self):
        """切换主题"""
        if self.theme_mode == "dark":
            ctk.set_appearance_mode("light")
            self.theme_mode = "light"
            self.theme_button.configure(text="☀️ 亮色主题")
        else:
            ctk.set_appearance_mode("dark")
            self.theme_mode = "dark"
            self.theme_button.configure(text="🌙 暗色主题")
            
        self.update_status("主题已切换到" + ("暗色" if self.theme_mode == "dark" else "亮色"))
        
    def switch_tab(self, tab_id):
        """切换标签页"""
        if tab_id == self.current_tab:
            return
            
        # 重置所有按钮状态
        for button in self.tab_buttons.values():
            button.configure(fg_color=("gray75", "gray25"))
            
        # 高亮当前按钮
        if tab_id in self.tab_buttons:
            self.tab_buttons[tab_id].configure(fg_color=("gray65", "gray35"))
            
        # 清空当前内容
        for widget in self.content_frame.winfo_children():
            widget.destroy()
            
        # 更新标题
        tab_names = {
            "main_config": "⚙️ 主配置",
            "strategy_market": "🏪 策略赶集",
            "trading_records": "📈 交易记录",
            "news": "📰 新闻资讯",
            "indicators": "📊 指标",
            "ai": "🤖 AI",
            "advanced_macd": "🎯 高级MACD",
            "pin_strategy": "📉 插针策略",
            "rsi_strategy": "📊 RSI策略",
            "stop_profit_loss": "💰 止盈止损",
            "equal_position": "📈 等量加仓",
            "risk_control": "🏦 银行级风控",
            "indicator_sync": "🔄 指标同步",
            "backtest": "📊 回测系统",
            "optimization": "🧪 参数优化",
            "lstm_prediction": "🤖 LSTM预测",
            "ai_assistant": "🤖 AI助手",
            "system_settings": "⚙️ 系统设置",
            "misc_config": "🔧 杂项配置",
            "log_console": "📜 日志控制台"
        }
        
        self.content_title.configure(text=tab_names.get(tab_id, "未知页面"))
        self.current_tab = tab_id
        
        # 加载对应的标签页内容
        self.load_tab_content(tab_id)
        self.update_status(f"已切换到 {tab_names.get(tab_id, '未知页面')}")
        
    def load_tab_content(self, tab_id):
        """加载标签页内容"""
        # 这里将调用对应的标签页创建函数
        content_loaders = {
            "main_config": self.create_main_config_tab,
            "strategy_market": self.create_strategy_market_tab,
            "trading_records": self.create_trading_records_tab,
            "news": self.create_news_tab,
            "indicators": self.create_indicators_tab,
            "ai": self.create_ai_tab,
            "advanced_macd": self.create_advanced_macd_tab,
            "pin_strategy": self.create_pin_strategy_tab,
            "rsi_strategy": self.create_rsi_strategy_tab,
            "stop_profit_loss": self.create_stop_profit_loss_tab,
            "equal_position": self.create_equal_position_tab,
            "risk_control": self.create_risk_control_tab,
            "indicator_sync": self.create_indicator_sync_tab,
            "backtest": self.create_backtest_tab,
            "optimization": self.create_optimization_tab,
            "lstm_prediction": self.create_lstm_prediction_tab,
            "ai_assistant": self.create_ai_assistant_tab,
            "system_settings": self.create_system_settings_tab,
            "misc_config": self.create_misc_config_tab,
            "log_console": self.create_log_console_tab
        }
        
        loader = content_loaders.get(tab_id)
        if loader:
            try:
                loader()
            except Exception as e:
                self.show_error_content(f"加载 {tab_id} 失败: {e}")
        else:
            self.show_placeholder_content(tab_id)
            
    def show_placeholder_content(self, tab_id):
        """显示占位符内容"""
        placeholder = ctk.CTkLabel(
            self.content_frame,
            text=f"🚧 {tab_id} 页面正在开发中...",
            font=ctk.CTkFont(size=16)
        )
        placeholder.grid(row=0, column=0, padx=20, pady=50)
        
    def show_error_content(self, error_msg):
        """显示错误内容"""
        error_label = ctk.CTkLabel(
            self.content_frame,
            text=f"❌ {error_msg}",
            font=ctk.CTkFont(size=16),
            text_color="red"
        )
        error_label.grid(row=0, column=0, padx=20, pady=50)
        
    def update_status(self, message):
        """更新状态栏"""
        self.status_label.configure(text=message)
        
    def create_all_tabs(self):
        """创建所有标签页（占位符）"""
        # 默认显示主配置页面
        self.switch_tab("main_config")
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()

    # ===== 标签页创建函数 =====

    def create_main_config_tab(self):
        """创建主配置标签页"""
        # 主配置容器
        main_container = ctk.CTkFrame(self.content_frame)
        main_container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        main_container.grid_columnconfigure(1, weight=1)

        # API配置区域
        api_frame = ctk.CTkFrame(main_container)
        api_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=10, pady=10)
        api_frame.grid_columnconfigure(1, weight=1)

        # API配置标题
        api_title = ctk.CTkLabel(
            api_frame,
            text="🔑 API配置",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        api_title.grid(row=0, column=0, columnspan=2, padx=20, pady=(15, 10), sticky="w")

        # 交易所选择
        exchange_label = ctk.CTkLabel(api_frame, text="交易所:")
        exchange_label.grid(row=1, column=0, padx=20, pady=5, sticky="w")

        self.exchange_var = ctk.StringVar(value="OKX")
        exchange_combo = ctk.CTkComboBox(
            api_frame,
            values=["OKX", "Gate.io", "Binance"],
            variable=self.exchange_var,
            command=self.on_exchange_change
        )
        exchange_combo.grid(row=1, column=1, padx=20, pady=5, sticky="ew")

        # API Key
        api_key_label = ctk.CTkLabel(api_frame, text="API Key:")
        api_key_label.grid(row=2, column=0, padx=20, pady=5, sticky="w")

        self.api_key_var = ctk.StringVar()
        api_key_entry = ctk.CTkEntry(
            api_frame,
            textvariable=self.api_key_var,
            placeholder_text="请输入API Key",
            show="*"
        )
        api_key_entry.grid(row=2, column=1, padx=20, pady=5, sticky="ew")

        # Secret Key
        secret_key_label = ctk.CTkLabel(api_frame, text="Secret Key:")
        secret_key_label.grid(row=3, column=0, padx=20, pady=5, sticky="w")

        self.secret_key_var = ctk.StringVar()
        secret_key_entry = ctk.CTkEntry(
            api_frame,
            textvariable=self.secret_key_var,
            placeholder_text="请输入Secret Key",
            show="*"
        )
        secret_key_entry.grid(row=3, column=1, padx=20, pady=5, sticky="ew")

        # Passphrase (OKX需要)
        passphrase_label = ctk.CTkLabel(api_frame, text="Passphrase:")
        passphrase_label.grid(row=4, column=0, padx=20, pady=5, sticky="w")

        self.passphrase_var = ctk.StringVar()
        passphrase_entry = ctk.CTkEntry(
            api_frame,
            textvariable=self.passphrase_var,
            placeholder_text="请输入Passphrase (OKX必需)",
            show="*"
        )
        passphrase_entry.grid(row=4, column=1, padx=20, pady=5, sticky="ew")

        # 测试连接按钮
        test_button = ctk.CTkButton(
            api_frame,
            text="🔗 测试连接",
            command=self.test_api_connection,
            width=120
        )
        test_button.grid(row=5, column=0, padx=20, pady=15, sticky="w")

        # 保存配置按钮
        save_button = ctk.CTkButton(
            api_frame,
            text="💾 保存配置",
            command=self.save_api_config,
            width=120
        )
        save_button.grid(row=5, column=1, padx=20, pady=15, sticky="e")

        # 交易配置区域
        trading_frame = ctk.CTkFrame(main_container)
        trading_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=10, pady=10)
        trading_frame.grid_columnconfigure(1, weight=1)

        # 交易配置标题
        trading_title = ctk.CTkLabel(
            trading_frame,
            text="⚙️ 交易配置",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        trading_title.grid(row=0, column=0, columnspan=2, padx=20, pady=(15, 10), sticky="w")

        # 交易对
        symbol_label = ctk.CTkLabel(trading_frame, text="交易对:")
        symbol_label.grid(row=1, column=0, padx=20, pady=5, sticky="w")

        self.symbol_var = ctk.StringVar(value="BTC-USDT")
        symbol_entry = ctk.CTkEntry(
            trading_frame,
            textvariable=self.symbol_var,
            placeholder_text="例如: BTC-USDT"
        )
        symbol_entry.grid(row=1, column=1, padx=20, pady=5, sticky="ew")

        # 交易金额
        amount_label = ctk.CTkLabel(trading_frame, text="交易金额 (USDT):")
        amount_label.grid(row=2, column=0, padx=20, pady=5, sticky="w")

        self.amount_var = ctk.StringVar(value="100")
        amount_entry = ctk.CTkEntry(
            trading_frame,
            textvariable=self.amount_var,
            placeholder_text="例如: 100"
        )
        amount_entry.grid(row=2, column=1, padx=20, pady=5, sticky="ew")

        # 杠杆倍数
        leverage_label = ctk.CTkLabel(trading_frame, text="杠杆倍数:")
        leverage_label.grid(row=3, column=0, padx=20, pady=5, sticky="w")

        self.leverage_var = ctk.StringVar(value="1")
        leverage_combo = ctk.CTkComboBox(
            trading_frame,
            values=["1", "2", "3", "5", "10", "20"],
            variable=self.leverage_var
        )
        leverage_combo.grid(row=3, column=1, padx=20, pady=5, sticky="ew")

        # 交易模式
        mode_label = ctk.CTkLabel(trading_frame, text="交易模式:")
        mode_label.grid(row=4, column=0, padx=20, pady=5, sticky="w")

        self.mode_var = ctk.StringVar(value="现货")
        mode_combo = ctk.CTkComboBox(
            trading_frame,
            values=["现货", "合约"],
            variable=self.mode_var
        )
        mode_combo.grid(row=4, column=1, padx=20, pady=5, sticky="ew")

        # 状态显示区域
        status_frame = ctk.CTkFrame(main_container)
        status_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=10, pady=10)

        # 状态标题
        status_title = ctk.CTkLabel(
            status_frame,
            text="📊 连接状态",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        status_title.grid(row=0, column=0, padx=20, pady=(15, 10), sticky="w")

        # 连接状态显示
        self.connection_status_label = ctk.CTkLabel(
            status_frame,
            text="🔴 未连接",
            font=ctk.CTkFont(size=14)
        )
        self.connection_status_label.grid(row=1, column=0, padx=20, pady=5, sticky="w")

        # 账户信息显示
        self.account_info_label = ctk.CTkLabel(
            status_frame,
            text="💰 账户信息: 未获取",
            font=ctk.CTkFont(size=14)
        )
        self.account_info_label.grid(row=2, column=0, padx=20, pady=(5, 15), sticky="w")

    def create_strategy_market_tab(self):
        """创建策略赶集标签页"""
        # 策略赶集容器
        strategy_container = ctk.CTkFrame(self.content_frame)
        strategy_container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        strategy_container.grid_columnconfigure(0, weight=1)

        # 策略选择区域
        selection_frame = ctk.CTkFrame(strategy_container)
        selection_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        selection_frame.grid_columnconfigure(1, weight=1)

        # 标题
        title = ctk.CTkLabel(
            selection_frame,
            text="🏪 策略赶集 - 多策略组合交易",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title.grid(row=0, column=0, columnspan=2, padx=20, pady=(15, 10), sticky="w")

        # 策略列表
        strategies = [
            ("插针策略", "检测价格异常波动，捕捉反弹机会"),
            ("三重确认", "多指标确认，提高信号准确性"),
            ("布林带突破", "基于布林带的突破交易策略"),
            ("双金叉", "均线和MACD双重金叉信号"),
            ("RSI背离", "RSI指标背离信号交易"),
            ("成交量突破", "基于成交量的突破策略"),
            ("支撑阻力", "关键位置的支撑阻力交易"),
            ("趋势跟踪", "趋势确认后的跟踪策略")
        ]

        self.strategy_vars = {}
        for i, (name, desc) in enumerate(strategies):
            # 策略复选框
            var = ctk.BooleanVar()
            checkbox = ctk.CTkCheckBox(
                selection_frame,
                text=name,
                variable=var,
                command=lambda: self.update_strategy_selection()
            )
            checkbox.grid(row=i+1, column=0, padx=20, pady=5, sticky="w")
            self.strategy_vars[name] = var

            # 策略描述
            desc_label = ctk.CTkLabel(
                selection_frame,
                text=desc,
                font=ctk.CTkFont(size=11),
                text_color="gray"
            )
            desc_label.grid(row=i+1, column=1, padx=20, pady=5, sticky="w")

        # 控制按钮区域
        control_frame = ctk.CTkFrame(strategy_container)
        control_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)

        # 控制按钮
        start_button = ctk.CTkButton(
            control_frame,
            text="🚀 启动策略",
            command=self.start_strategies,
            width=120,
            fg_color="green"
        )
        start_button.grid(row=0, column=0, padx=20, pady=15)

        stop_button = ctk.CTkButton(
            control_frame,
            text="⏹️ 停止策略",
            command=self.stop_strategies,
            width=120,
            fg_color="red"
        )
        stop_button.grid(row=0, column=1, padx=20, pady=15)

        # 策略状态显示
        self.strategy_status_label = ctk.CTkLabel(
            control_frame,
            text="⏸️ 策略状态: 未启动",
            font=ctk.CTkFont(size=14)
        )
        self.strategy_status_label.grid(row=0, column=2, padx=20, pady=15, sticky="w")

    # ===== 事件处理函数 =====

    def on_exchange_change(self, value):
        """交易所切换事件"""
        self.update_status(f"已切换到 {value} 交易所")

    def test_api_connection(self):
        """测试API连接（防止并发）"""
        with self.test_connection_lock:
            if self.is_testing_connection:
                self.update_status("⚠️ 连接测试正在进行中，请稍候...")
                return

            self.is_testing_connection = True

        self.update_status("正在测试API连接...")
        self.connection_status_label.configure(text="🟡 连接测试中...")

        # 异步测试连接
        def test_connection():
            try:
                time.sleep(2)  # 模拟网络延迟
                # 这里应该调用实际的API测试
                success = True  # 模拟测试结果

                # 使用队列安全更新GUI
                def update_success():
                    self.connection_status_label.configure(text="🟢 连接成功")
                    self.account_info_label.configure(text="💰 账户信息: USDT余额 1000.00")
                    self.update_status("✅ API连接测试成功")

                def update_failure():
                    self.connection_status_label.configure(text="🔴 连接失败")
                    self.update_status("❌ API连接测试失败")

                if success:
                    self.root.after(0, update_success)
                else:
                    self.root.after(0, update_failure)

            except Exception as e:
                def update_error():
                    self.connection_status_label.configure(text="🔴 连接错误")
                    self.update_status(f"❌ 连接测试异常: {e}")
                self.root.after(0, update_error)
            finally:
                self.is_testing_connection = False

        threading.Thread(target=test_connection, daemon=True).start()

    def save_api_config(self):
        """保存API配置"""
        try:
            # 数据验证
            exchange = self.exchange_var.get().strip()
            api_key = self.api_key_var.get().strip()
            secret_key = self.secret_key_var.get().strip()
            symbol = self.symbol_var.get().strip()
            amount_str = self.amount_var.get().strip()
            leverage_str = self.leverage_var.get().strip()

            # 验证必填字段
            if not exchange:
                self.update_status("❌ 请选择交易所")
                return
            if not symbol:
                self.update_status("❌ 请输入交易对")
                return

            # 验证数值字段
            try:
                amount = float(amount_str) if amount_str else 0
                if amount <= 0:
                    self.update_status("❌ 交易金额必须大于0")
                    return
            except ValueError:
                self.update_status("❌ 交易金额格式错误")
                return

            try:
                leverage = int(leverage_str) if leverage_str else 1
                if leverage < 1 or leverage > 100:
                    self.update_status("❌ 杠杆倍数必须在1-100之间")
                    return
            except ValueError:
                self.update_status("❌ 杠杆倍数格式错误")
                return

            config = {
                "exchange": exchange,
                "api_key": api_key,
                "secret_key": secret_key,
                "passphrase": self.passphrase_var.get().strip(),
                "symbol": symbol,
                "amount": amount,
                "leverage": leverage,
                "mode": self.mode_var.get()
            }

            # 保存配置
            with open("modern_wmzc_config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            self.update_status("✅ 配置保存成功")

        except PermissionError:
            self.update_status("❌ 配置保存失败: 文件权限不足")
        except OSError as e:
            self.update_status(f"❌ 配置保存失败: 文件系统错误 {e}")
        except Exception as e:
            self.update_status(f"❌ 配置保存失败: {e}")

    def update_strategy_selection(self):
        """更新策略选择"""
        selected = [name for name, var in self.strategy_vars.items() if var.get()]
        self.update_status(f"已选择 {len(selected)} 个策略")

    def start_strategies(self):
        """启动策略"""
        selected = [name for name, var in self.strategy_vars.items() if var.get()]
        if not selected:
            self.update_status("请先选择至少一个策略")
            return

        self.strategy_status_label.configure(text="🟢 策略状态: 运行中")
        self.update_status(f"已启动 {len(selected)} 个策略")

    def stop_strategies(self):
        """停止策略"""
        self.strategy_status_label.configure(text="🔴 策略状态: 已停止")
        self.update_status("所有策略已停止")

    def create_trading_records_tab(self):
        """创建交易记录标签页"""
        # 交易记录容器
        records_container = ctk.CTkFrame(self.content_frame)
        records_container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        records_container.grid_columnconfigure(0, weight=1)

        # 标题和控制区域
        header_frame = ctk.CTkFrame(records_container)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        header_frame.grid_columnconfigure(1, weight=1)

        title = ctk.CTkLabel(
            header_frame,
            text="📈 交易记录",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 刷新按钮
        refresh_button = ctk.CTkButton(
            header_frame,
            text="🔄 刷新",
            command=self.refresh_trading_records,
            width=100
        )
        refresh_button.grid(row=0, column=2, padx=20, pady=15)

        # 统计信息区域
        stats_frame = ctk.CTkFrame(records_container)
        stats_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # 统计卡片
        self.create_stat_card(stats_frame, "总盈亏", "+1,234.56 USDT", "green", 0)
        self.create_stat_card(stats_frame, "今日盈亏", "+123.45 USDT", "green", 1)
        self.create_stat_card(stats_frame, "胜率", "68.5%", "blue", 2)
        self.create_stat_card(stats_frame, "交易次数", "156", "gray", 3)

        # 交易记录表格区域
        table_frame = ctk.CTkFrame(records_container)
        table_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=10)

        # 表格标题
        table_title = ctk.CTkLabel(
            table_frame,
            text="📋 最近交易记录",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        table_title.grid(row=0, column=0, padx=20, pady=(15, 10), sticky="w")

        # 创建表格（使用Treeview）
        self.create_trading_table(table_frame)

    def create_stat_card(self, parent, title, value, color, column):
        """创建统计卡片"""
        card = ctk.CTkFrame(parent)
        card.grid(row=0, column=column, padx=10, pady=10, sticky="ew")

        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        title_label.grid(row=0, column=0, padx=15, pady=(15, 5))

        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=color
        )
        value_label.grid(row=1, column=0, padx=15, pady=(5, 15))

    def create_trading_table(self, parent):
        """创建交易记录表格"""
        # 表格容器
        table_container = ctk.CTkFrame(parent)
        table_container.grid(row=1, column=0, sticky="ew", padx=20, pady=(0, 20))

        # 使用tkinter的Treeview创建表格
        columns = ("时间", "交易对", "方向", "数量", "价格", "盈亏", "状态")

        # 创建Treeview
        tree_frame = tk.Frame(table_container)
        tree_frame.pack(fill="both", expand=True, padx=10, pady=10)

        self.trading_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=10)

        # 设置列标题
        for col in columns:
            self.trading_tree.heading(col, text=col)
            self.trading_tree.column(col, width=100, anchor="center")

        # 添加滚动条
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.trading_tree.yview)
        self.trading_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.trading_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 添加示例数据
        self.populate_trading_table()

    def populate_trading_table(self):
        """填充交易记录表格"""
        # 示例数据
        sample_data = [
            ("2025-01-22 10:30:15", "BTC-USDT", "买入", "0.001", "42,500", "+12.50", "已完成"),
            ("2025-01-22 10:25:30", "ETH-USDT", "卖出", "0.05", "3,200", "-5.20", "已完成"),
            ("2025-01-22 10:20:45", "BTC-USDT", "买入", "0.002", "42,300", "+25.80", "已完成"),
            ("2025-01-22 10:15:12", "SOL-USDT", "买入", "1.0", "180", "+8.90", "已完成"),
            ("2025-01-22 10:10:33", "ADA-USDT", "卖出", "100", "0.85", "-2.15", "已完成"),
        ]

        for item in sample_data:
            self.trading_tree.insert("", "end", values=item)

    def refresh_trading_records(self):
        """刷新交易记录"""
        self.update_status("正在刷新交易记录...")
        # 这里将调用实际的数据刷新逻辑

    def create_news_tab(self):
        """创建新闻资讯标签页"""
        # 新闻容器
        news_container = ctk.CTkFrame(self.content_frame)
        news_container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        news_container.grid_columnconfigure(0, weight=1)

        # 标题和控制区域
        header_frame = ctk.CTkFrame(news_container)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        header_frame.grid_columnconfigure(1, weight=1)

        title = ctk.CTkLabel(
            header_frame,
            text="📰 新闻资讯",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 新闻源选择
        source_label = ctk.CTkLabel(header_frame, text="新闻源:")
        source_label.grid(row=0, column=1, padx=20, pady=15, sticky="e")

        self.news_source_var = ctk.StringVar(value="全部")
        source_combo = ctk.CTkComboBox(
            header_frame,
            values=["全部", "CoinDesk", "Cointelegraph", "金色财经", "币世界"],
            variable=self.news_source_var,
            command=self.filter_news
        )
        source_combo.grid(row=0, column=2, padx=20, pady=15)

        # 新闻列表区域
        news_list_frame = ctk.CTkScrollableFrame(news_container, height=400)
        news_list_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        news_list_frame.grid_columnconfigure(0, weight=1)

        # 添加示例新闻
        self.create_news_items(news_list_frame)

    def create_news_items(self, parent):
        """创建新闻条目"""
        news_items = [
            {
                "title": "比特币突破43000美元，创近期新高",
                "summary": "比特币价格在今日早盘突破43000美元关口，市场情绪乐观...",
                "time": "2小时前",
                "source": "CoinDesk",
                "sentiment": "positive"
            },
            {
                "title": "以太坊2.0质押量突破3000万ETH",
                "summary": "以太坊2.0网络的质押量持续增长，显示出投资者对网络的信心...",
                "time": "4小时前",
                "source": "Cointelegraph",
                "sentiment": "positive"
            },
            {
                "title": "美联储官员暗示可能调整利率政策",
                "summary": "美联储官员在最新讲话中暗示可能调整当前的利率政策...",
                "time": "6小时前",
                "source": "金色财经",
                "sentiment": "neutral"
            },
            {
                "title": "某交易所遭遇技术故障，交易暂停",
                "summary": "某知名加密货币交易所今日遭遇技术故障，导致交易暂停...",
                "time": "8小时前",
                "source": "币世界",
                "sentiment": "negative"
            }
        ]

        for i, news in enumerate(news_items):
            self.create_news_card(parent, news, i)

    def create_news_card(self, parent, news, row):
        """创建新闻卡片"""
        card = ctk.CTkFrame(parent)
        card.grid(row=row, column=0, sticky="ew", padx=10, pady=5)
        card.grid_columnconfigure(0, weight=1)

        # 新闻标题
        title_label = ctk.CTkLabel(
            card,
            text=news["title"],
            font=ctk.CTkFont(size=14, weight="bold"),
            anchor="w"
        )
        title_label.grid(row=0, column=0, padx=15, pady=(15, 5), sticky="ew")

        # 新闻摘要
        summary_label = ctk.CTkLabel(
            card,
            text=news["summary"],
            font=ctk.CTkFont(size=12),
            anchor="w",
            text_color="gray"
        )
        summary_label.grid(row=1, column=0, padx=15, pady=5, sticky="ew")

        # 新闻信息（时间、来源、情绪）
        info_frame = ctk.CTkFrame(card, fg_color="transparent")
        info_frame.grid(row=2, column=0, sticky="ew", padx=15, pady=(5, 15))

        time_label = ctk.CTkLabel(
            info_frame,
            text=news["time"],
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        time_label.grid(row=0, column=0, sticky="w")

        source_label = ctk.CTkLabel(
            info_frame,
            text=news["source"],
            font=ctk.CTkFont(size=10),
            text_color="blue"
        )
        source_label.grid(row=0, column=1, padx=20, sticky="w")

        # 情绪指示器
        sentiment_colors = {
            "positive": "green",
            "neutral": "gray",
            "negative": "red"
        }
        sentiment_texts = {
            "positive": "📈 利好",
            "neutral": "📊 中性",
            "negative": "📉 利空"
        }

        sentiment_label = ctk.CTkLabel(
            info_frame,
            text=sentiment_texts[news["sentiment"]],
            font=ctk.CTkFont(size=10),
            text_color=sentiment_colors[news["sentiment"]]
        )
        sentiment_label.grid(row=0, column=2, padx=20, sticky="w")

    def filter_news(self, source):
        """过滤新闻"""
        self.update_status(f"已切换到 {source} 新闻源")

    # ===== 辅助函数 =====

    def create_indicator_card(self, parent, title, value, color, column):
        """创建指标卡片"""
        card = ctk.CTkFrame(parent)
        card.grid(row=1, column=column, padx=10, pady=10, sticky="ew")

        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        title_label.grid(row=0, column=0, padx=15, pady=(15, 5))

        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=color
        )
        value_label.grid(row=1, column=0, padx=15, pady=(5, 15))

    def create_signal_card(self, parent, title, value, color, column):
        """创建信号卡片"""
        card = ctk.CTkFrame(parent)
        card.grid(row=1, column=column, padx=10, pady=10, sticky="ew")

        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        title_label.grid(row=0, column=0, padx=15, pady=(15, 5))

        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=color
        )
        value_label.grid(row=1, column=0, padx=15, pady=(5, 15))

    def create_status_card(self, parent, title, value, color, column):
        """创建状态卡片"""
        card = ctk.CTkFrame(parent)
        card.grid(row=1, column=column, padx=10, pady=10, sticky="ew")

        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        title_label.grid(row=0, column=0, padx=15, pady=(15, 5))

        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=color
        )
        value_label.grid(row=1, column=0, padx=15, pady=(5, 15))

    # ===== 事件处理函数 =====

    def on_indicator_exchange_change(self, value):
        """指标交易所切换事件"""
        self.update_status(f"指标数据已切换到 {value} 交易所")

    def save_indicator_config(self):
        """保存指标配置"""
        config = {
            "macd": {
                "fast": self.indicator_macd_fast_var.get(),
                "slow": self.indicator_macd_slow_var.get(),
                "signal": self.indicator_macd_signal_var.get()
            },
            "rsi": {
                "period": self.indicator_rsi_period_var.get(),
                "overbought": self.indicator_rsi_overbought_var.get(),
                "oversold": self.indicator_rsi_oversold_var.get()
            }
        }
        self.update_status("指标配置已保存")

    def refresh_indicator_data(self):
        """刷新指标数据"""
        self.update_status("正在刷新指标数据...")

    def start_ai_services(self):
        """启动AI服务"""
        self.ai_status_label.configure(text="🟢 AI服务运行中")
        self.update_status("AI服务已启动")

    def stop_ai_services(self):
        """停止AI服务"""
        self.ai_status_label.configure(text="🔴 AI服务已停止")
        self.update_status("AI服务已停止")

    def test_ai_connection(self):
        """测试AI连接（防止并发）"""
        with self.test_ai_connection_lock:
            if self.is_testing_ai_connection:
                self.update_status("⚠️ AI连接测试正在进行中，请稍候...")
                return

            self.is_testing_ai_connection = True

        self.update_status("正在测试AI服务连接...")

        # 异步测试AI连接
        def test_connection():
            try:
                def update_testing():
                    self.ai_status_label.configure(text="🟡 AI服务测试中...")
                self.root.after(0, update_testing)

                time.sleep(2)  # 模拟网络延迟

                # 模拟测试结果
                success = True

                def update_result():
                    if success:
                        self.ai_status_label.configure(text="🟢 AI服务连接正常")
                        self.update_status("✅ AI服务连接测试成功")
                    else:
                        self.ai_status_label.configure(text="🔴 AI服务连接失败")
                        self.update_status("❌ AI服务连接测试失败")

                self.root.after(0, update_result)

            except Exception as e:
                def update_error():
                    self.ai_status_label.configure(text="🔴 AI服务连接错误")
                    self.update_status(f"❌ AI连接测试异常: {e}")
                self.root.after(0, update_error)
            finally:
                self.is_testing_ai_connection = False

        threading.Thread(target=test_connection, daemon=True).start()

    def start_macd_strategy(self):
        """启动MACD策略"""
        self.macd_status_label.configure(text="🟢 MACD策略状态: 运行中")
        self.update_status("MACD策略已启动")

    def stop_macd_strategy(self):
        """停止MACD策略"""
        self.macd_status_label.configure(text="🔴 MACD策略状态: 已停止")
        self.update_status("MACD策略已停止")

    def start_pin_strategy(self):
        """启动插针策略"""
        self.pin_status_label.configure(text="🟢 插针策略状态: 运行中")
        self.update_status("插针策略已启动")

    def stop_pin_strategy(self):
        """停止插针策略"""
        self.pin_status_label.configure(text="🔴 插针策略状态: 已停止")
        self.update_status("插针策略已停止")

    def start_rsi_strategy(self):
        """启动RSI策略"""
        self.rsi_strategy_status_label.configure(text="🟢 RSI策略状态: 运行中")
        self.update_status("RSI策略已启动")

    def stop_rsi_strategy(self):
        """停止RSI策略"""
        self.rsi_strategy_status_label.configure(text="🔴 RSI策略状态: 已停止")
        self.update_status("RSI策略已停止")

    # ===== 动画效果函数 =====

    def fade_in_widget(self, widget, duration=300):
        """淡入动画效果"""
        if not hasattr(widget, '_alpha'):
            widget._alpha = 0.0

        def animate():
            if widget._alpha < 1.0:
                widget._alpha += 0.1
                # 这里可以设置透明度，但CustomTkinter可能不直接支持
                # 作为替代，我们使用位置动画
                self.root.after(30, animate)
            else:
                widget._alpha = 1.0

        animate()

    def slide_in_widget(self, widget, direction="left", duration=300):
        """滑入动画效果"""
        if direction == "left":
            start_x = -widget.winfo_reqwidth()
            end_x = 0
        else:
            start_x = widget.winfo_reqwidth()
            end_x = 0

        steps = duration // 20
        step_size = (end_x - start_x) / steps
        current_x = start_x

        def animate():
            nonlocal current_x
            if abs(current_x - end_x) > abs(step_size):
                current_x += step_size
                widget.place(x=current_x)
                self.root.after(20, animate)
            else:
                widget.place(x=end_x)

        animate()

    def animate_button_click(self, button):
        """按钮点击动画效果"""
        original_color = button.cget("fg_color")

        # 按下效果
        button.configure(fg_color="gray")

        def restore_color():
            button.configure(fg_color=original_color)

        self.root.after(100, restore_color)

    def smooth_scroll_to_top(self):
        """平滑滚动到顶部"""
        if hasattr(self.content_frame, '_yview'):
            current_pos = self.content_frame._yview[0]
            if current_pos > 0:
                new_pos = max(0, current_pos - 0.1)
                self.content_frame.yview_moveto(new_pos)
                self.root.after(20, self.smooth_scroll_to_top)

    # ===== 响应式布局函数 =====

    def on_window_resize(self, event):
        """窗口大小变化事件"""
        if event.widget == self.root:
            new_width = event.width
            new_height = event.height

            # 更新窗口尺寸记录
            self.window_width = new_width
            self.window_height = new_height

            # 响应式调整侧边栏
            self.adjust_sidebar_for_screen_size()

            # 调整内容区域
            self.adjust_content_area()

    def adjust_sidebar_for_screen_size(self):
        """根据屏幕大小调整侧边栏"""
        if self.window_width < 1200:
            # 小屏幕：折叠侧边栏
            if not self.is_sidebar_collapsed:
                self.collapse_sidebar()
        else:
            # 大屏幕：展开侧边栏
            if self.is_sidebar_collapsed:
                self.expand_sidebar()

    def collapse_sidebar(self):
        """折叠侧边栏"""
        self.is_sidebar_collapsed = True
        self.sidebar.configure(width=60)

        # 隐藏按钮文字，只显示图标
        for button in self.tab_buttons.values():
            current_text = button.cget("text")
            if " " in current_text:
                icon = current_text.split(" ")[0]
                button.configure(text=icon, width=40)

        # 隐藏标题
        if hasattr(self, 'title_label'):
            self.title_label.grid_remove()

        self.update_status("侧边栏已折叠")

    def expand_sidebar(self):
        """展开侧边栏"""
        self.is_sidebar_collapsed = False
        self.sidebar.configure(width=self.sidebar_width)

        # 恢复按钮文字
        tab_configs = [
            ("⚙️ 主配置", "main_config", "🔧"),
            ("🏪 策略赶集", "strategy_market", "🏪"),
            ("📈 交易记录", "trading_records", "📈"),
            ("📰 新闻资讯", "news", "📰"),
            ("📊 指标", "indicators", "📊"),
            ("🤖 AI", "ai", "🤖"),
            ("🎯 高级MACD", "advanced_macd", "🎯"),
            ("📉 插针策略", "pin_strategy", "📉"),
            ("📊 RSI策略", "rsi_strategy", "📊"),
            ("💰 止盈止损", "stop_profit_loss", "💰"),
            ("📈 等量加仓", "equal_position", "📈"),
            ("🏦 银行级风控", "risk_control", "🏦"),
            ("🔄 指标同步", "indicator_sync", "🔄"),
            ("📊 回测系统", "backtest", "📊"),
            ("🧪 参数优化", "optimization", "🧪"),
            ("🤖 LSTM预测", "lstm_prediction", "🤖"),
            ("🤖 AI助手", "ai_assistant", "🤖"),
            ("⚙️ 系统设置", "system_settings", "⚙️"),
            ("🔧 杂项配置", "misc_config", "🔧"),
            ("📜 日志控制台", "log_console", "📜")
        ]

        for display_name, tab_id, icon in tab_configs:
            if tab_id in self.tab_buttons:
                self.tab_buttons[tab_id].configure(
                    text=f"{icon} {display_name.split(' ', 1)[1]}",
                    width=240
                )

        self.update_status("侧边栏已展开")

    def adjust_content_area(self):
        """调整内容区域"""
        # 根据窗口大小调整内容区域的布局
        if self.window_width < 1200:
            # 小屏幕：紧凑布局
            self.content_frame.configure(corner_radius=5)
        else:
            # 大屏幕：正常布局
            self.content_frame.configure(corner_radius=10)

    def toggle_sidebar(self):
        """切换侧边栏状态"""
        if self.is_sidebar_collapsed:
            self.expand_sidebar()
        else:
            self.collapse_sidebar()

    def on_closing(self):
        """窗口关闭事件"""
        try:
            # 保存窗口状态
            window_config = {
                "width": self.window_width,
                "height": self.window_height,
                "sidebar_collapsed": self.is_sidebar_collapsed,
                "theme": self.theme_mode,
                "last_tab": self.current_tab
            }

            with open("modern_wmzc_window_state.json", "w", encoding="utf-8") as f:
                json.dump(window_config, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"保存窗口状态失败: {e}")

        # 关闭窗口
        self.root.destroy()

    def load_window_state(self):
        """加载窗口状态"""
        try:
            with open("modern_wmzc_window_state.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            self.window_width = config.get("width", 1400)
            self.window_height = config.get("height", 900)
            self.is_sidebar_collapsed = config.get("sidebar_collapsed", False)
            self.theme_mode = config.get("theme", "dark")
            last_tab = config.get("last_tab", "main_config")

            # 应用配置
            self.root.geometry(f"{self.window_width}x{self.window_height}")

            if self.theme_mode == "light":
                self.toggle_theme()

            if self.is_sidebar_collapsed:
                self.collapse_sidebar()

            # 切换到上次的标签页
            if last_tab:
                self.switch_tab(last_tab)

        except FileNotFoundError:
            # 首次运行，使用默认配置
            pass
        except Exception as e:
            print(f"加载窗口状态失败: {e}")

    # ===== 其他标签页占位符实现 =====

    def create_indicators_tab(self):
        """创建指标标签页"""
        # 指标容器
        indicators_container = ctk.CTkFrame(self.content_frame)
        indicators_container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        indicators_container.grid_columnconfigure(0, weight=1)

        # 标题区域
        header_frame = ctk.CTkFrame(indicators_container)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        header_frame.grid_columnconfigure(1, weight=1)

        title = ctk.CTkLabel(
            header_frame,
            text="📊 技术指标配置",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 交易所选择
        exchange_label = ctk.CTkLabel(header_frame, text="交易所:")
        exchange_label.grid(row=0, column=1, padx=20, pady=15, sticky="e")

        self.indicator_exchange_var = ctk.StringVar(value="OKX")
        exchange_combo = ctk.CTkComboBox(
            header_frame,
            values=["OKX", "Gate.io", "Binance"],
            variable=self.indicator_exchange_var,
            command=self.on_indicator_exchange_change
        )
        exchange_combo.grid(row=0, column=2, padx=20, pady=15)

        # 指标配置区域
        config_frame = ctk.CTkFrame(indicators_container)
        config_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        config_frame.grid_columnconfigure((0, 1), weight=1)

        # MACD配置
        macd_frame = ctk.CTkFrame(config_frame)
        macd_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        macd_frame.grid_columnconfigure(1, weight=1)

        macd_title = ctk.CTkLabel(
            macd_frame,
            text="📈 MACD指标",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        macd_title.grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # MACD参数
        ctk.CTkLabel(macd_frame, text="快线周期:").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.indicator_macd_fast_var = ctk.StringVar(value="12")
        ctk.CTkEntry(macd_frame, textvariable=self.indicator_macd_fast_var, width=80).grid(row=1, column=1, padx=15, pady=5, sticky="w")

        ctk.CTkLabel(macd_frame, text="慢线周期:").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.indicator_macd_slow_var = ctk.StringVar(value="26")
        ctk.CTkEntry(macd_frame, textvariable=self.indicator_macd_slow_var, width=80).grid(row=2, column=1, padx=15, pady=5, sticky="w")

        ctk.CTkLabel(macd_frame, text="信号线周期:").grid(row=3, column=0, padx=15, pady=5, sticky="w")
        self.indicator_macd_signal_var = ctk.StringVar(value="9")
        ctk.CTkEntry(macd_frame, textvariable=self.indicator_macd_signal_var, width=80).grid(row=3, column=1, padx=15, pady=5, sticky="w")

        # RSI配置
        rsi_frame = ctk.CTkFrame(config_frame)
        rsi_frame.grid(row=0, column=1, sticky="ew", padx=10, pady=10)
        rsi_frame.grid_columnconfigure(1, weight=1)

        rsi_title = ctk.CTkLabel(
            rsi_frame,
            text="📊 RSI指标",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        rsi_title.grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # RSI参数
        ctk.CTkLabel(rsi_frame, text="周期:").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.indicator_rsi_period_var = ctk.StringVar(value="14")
        ctk.CTkEntry(rsi_frame, textvariable=self.indicator_rsi_period_var, width=80).grid(row=1, column=1, padx=15, pady=5, sticky="w")

        ctk.CTkLabel(rsi_frame, text="超买线:").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.indicator_rsi_overbought_var = ctk.StringVar(value="70")
        ctk.CTkEntry(rsi_frame, textvariable=self.indicator_rsi_overbought_var, width=80).grid(row=2, column=1, padx=15, pady=5, sticky="w")

        ctk.CTkLabel(rsi_frame, text="超卖线:").grid(row=3, column=0, padx=15, pady=5, sticky="w")
        self.indicator_rsi_oversold_var = ctk.StringVar(value="30")
        ctk.CTkEntry(rsi_frame, textvariable=self.indicator_rsi_oversold_var, width=80).grid(row=3, column=1, padx=15, pady=5, sticky="w")

        # 实时数据显示区域
        data_frame = ctk.CTkFrame(indicators_container)
        data_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=10)
        data_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        data_title = ctk.CTkLabel(
            data_frame,
            text="📊 实时指标数据",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        data_title.grid(row=0, column=0, columnspan=4, padx=20, pady=(15, 10), sticky="w")

        # 指标数据卡片
        self.create_indicator_card(data_frame, "MACD", "0.0125", "green", 0)
        self.create_indicator_card(data_frame, "RSI", "65.8", "orange", 1)
        self.create_indicator_card(data_frame, "KDJ", "K:75.2", "blue", 2)
        self.create_indicator_card(data_frame, "BOLL", "上轨:43200", "gray", 3)

        # 控制按钮
        button_frame = ctk.CTkFrame(indicators_container)
        button_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=10)

        save_button = ctk.CTkButton(
            button_frame,
            text="💾 保存配置",
            command=self.save_indicator_config,
            width=120
        )
        save_button.grid(row=0, column=0, padx=20, pady=15)

        refresh_button = ctk.CTkButton(
            button_frame,
            text="🔄 刷新数据",
            command=self.refresh_indicator_data,
            width=120
        )
        refresh_button.grid(row=0, column=1, padx=20, pady=15)

    def create_ai_tab(self):
        """创建AI标签页"""
        # AI功能容器
        ai_container = ctk.CTkFrame(self.content_frame)
        ai_container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        ai_container.grid_columnconfigure(0, weight=1)

        # 标题区域
        header_frame = ctk.CTkFrame(ai_container)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(
            header_frame,
            text="🤖 AI功能总控",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # AI服务配置区域
        config_frame = ctk.CTkFrame(ai_container)
        config_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        config_frame.grid_columnconfigure(1, weight=1)

        config_title = ctk.CTkLabel(
            config_frame,
            text="🔧 AI服务配置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        config_title.grid(row=0, column=0, columnspan=2, padx=20, pady=(15, 10), sticky="w")

        # API密钥配置
        ctk.CTkLabel(config_frame, text="OpenAI API Key:").grid(row=1, column=0, padx=20, pady=5, sticky="w")
        self.openai_key_var = ctk.StringVar()
        ctk.CTkEntry(config_frame, textvariable=self.openai_key_var, show="*", placeholder_text="sk-...").grid(row=1, column=1, padx=20, pady=5, sticky="ew")

        ctk.CTkLabel(config_frame, text="Claude API Key:").grid(row=2, column=0, padx=20, pady=5, sticky="w")
        self.claude_key_var = ctk.StringVar()
        ctk.CTkEntry(config_frame, textvariable=self.claude_key_var, show="*", placeholder_text="sk-ant-...").grid(row=2, column=1, padx=20, pady=5, sticky="ew")

        ctk.CTkLabel(config_frame, text="Gemini API Key:").grid(row=3, column=0, padx=20, pady=5, sticky="w")
        self.gemini_key_var = ctk.StringVar()
        ctk.CTkEntry(config_frame, textvariable=self.gemini_key_var, show="*", placeholder_text="AI...").grid(row=3, column=1, padx=20, pady=5, sticky="ew")

        # AI功能开关区域
        features_frame = ctk.CTkFrame(ai_container)
        features_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=10)
        features_frame.grid_columnconfigure((0, 1), weight=1)

        features_title = ctk.CTkLabel(
            features_frame,
            text="⚡ AI功能开关",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        features_title.grid(row=0, column=0, columnspan=2, padx=20, pady=(15, 10), sticky="w")

        # AI功能复选框
        self.ai_features = {}
        ai_feature_list = [
            ("智能交易建议", "根据市场数据提供交易建议"),
            ("风险评估", "AI驱动的风险评估和预警"),
            ("新闻情绪分析", "分析新闻对市场的情绪影响"),
            ("价格预测", "基于机器学习的价格预测"),
            ("策略优化", "AI辅助的策略参数优化"),
            ("异常检测", "检测异常交易行为和市场波动")
        ]

        for i, (feature, desc) in enumerate(ai_feature_list):
            var = ctk.BooleanVar()
            checkbox = ctk.CTkCheckBox(
                features_frame,
                text=feature,
                variable=var
            )
            checkbox.grid(row=i+1, column=0, padx=20, pady=5, sticky="w")
            self.ai_features[feature] = var

            desc_label = ctk.CTkLabel(
                features_frame,
                text=desc,
                font=ctk.CTkFont(size=11),
                text_color="gray"
            )
            desc_label.grid(row=i+1, column=1, padx=20, pady=5, sticky="w")

        # AI状态显示区域
        status_frame = ctk.CTkFrame(ai_container)
        status_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=10)

        status_title = ctk.CTkLabel(
            status_frame,
            text="📊 AI服务状态",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.grid(row=0, column=0, padx=20, pady=(15, 10), sticky="w")

        self.ai_status_label = ctk.CTkLabel(
            status_frame,
            text="🔴 AI服务未启动",
            font=ctk.CTkFont(size=14)
        )
        self.ai_status_label.grid(row=1, column=0, padx=20, pady=5, sticky="w")

        # 控制按钮
        button_frame = ctk.CTkFrame(ai_container)
        button_frame.grid(row=4, column=0, sticky="ew", padx=10, pady=10)

        start_ai_button = ctk.CTkButton(
            button_frame,
            text="🚀 启动AI服务",
            command=self.start_ai_services,
            width=120,
            fg_color="green"
        )
        start_ai_button.grid(row=0, column=0, padx=20, pady=15)

        stop_ai_button = ctk.CTkButton(
            button_frame,
            text="⏹️ 停止AI服务",
            command=self.stop_ai_services,
            width=120,
            fg_color="red"
        )
        stop_ai_button.grid(row=0, column=1, padx=20, pady=15)

        test_ai_button = ctk.CTkButton(
            button_frame,
            text="🧪 测试AI连接",
            command=self.test_ai_connection,
            width=120
        )
        test_ai_button.grid(row=0, column=2, padx=20, pady=15)

    def create_advanced_macd_tab(self):
        """创建高级MACD标签页"""
        # 高级MACD容器
        macd_container = ctk.CTkFrame(self.content_frame)
        macd_container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        macd_container.grid_columnconfigure(0, weight=1)

        # 标题区域
        header_frame = ctk.CTkFrame(macd_container)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(
            header_frame,
            text="🎯 高级MACD策略",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # MACD参数配置区域
        params_frame = ctk.CTkFrame(macd_container)
        params_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        params_frame.grid_columnconfigure((0, 1, 2), weight=1)

        params_title = ctk.CTkLabel(
            params_frame,
            text="⚙️ MACD参数配置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        params_title.grid(row=0, column=0, columnspan=3, padx=20, pady=(15, 10), sticky="w")

        # 基础参数
        basic_frame = ctk.CTkFrame(params_frame)
        basic_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(basic_frame, text="基础参数", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(basic_frame, text="快线周期:").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.macd_fast_period_var = ctk.StringVar(value="12")
        ctk.CTkEntry(basic_frame, textvariable=self.macd_fast_period_var, width=80).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(basic_frame, text="慢线周期:").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.macd_slow_period_var = ctk.StringVar(value="26")
        ctk.CTkEntry(basic_frame, textvariable=self.macd_slow_period_var, width=80).grid(row=2, column=1, padx=15, pady=5)

        ctk.CTkLabel(basic_frame, text="信号线周期:").grid(row=3, column=0, padx=15, pady=5, sticky="w")
        self.macd_signal_period_var = ctk.StringVar(value="9")
        ctk.CTkEntry(basic_frame, textvariable=self.macd_signal_period_var, width=80).grid(row=3, column=1, padx=15, pady=(5, 15))

        # 高级参数
        advanced_frame = ctk.CTkFrame(params_frame)
        advanced_frame.grid(row=1, column=1, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(advanced_frame, text="高级参数", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(advanced_frame, text="背离检测:").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.macd_divergence_var = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(advanced_frame, text="", variable=self.macd_divergence_var).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(advanced_frame, text="多重确认:").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.macd_confirmation_var = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(advanced_frame, text="", variable=self.macd_confirmation_var).grid(row=2, column=1, padx=15, pady=5)

        ctk.CTkLabel(advanced_frame, text="动态阈值:").grid(row=3, column=0, padx=15, pady=5, sticky="w")
        self.macd_dynamic_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(advanced_frame, text="", variable=self.macd_dynamic_var).grid(row=3, column=1, padx=15, pady=(5, 15))

        # 交易参数
        trading_frame = ctk.CTkFrame(params_frame)
        trading_frame.grid(row=1, column=2, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(trading_frame, text="交易参数", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(trading_frame, text="止盈比例(%):").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.macd_take_profit_var = ctk.StringVar(value="2.0")
        ctk.CTkEntry(trading_frame, textvariable=self.macd_take_profit_var, width=80).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(trading_frame, text="止损比例(%):").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.macd_stop_loss_var = ctk.StringVar(value="1.0")
        ctk.CTkEntry(trading_frame, textvariable=self.macd_stop_loss_var, width=80).grid(row=2, column=1, padx=15, pady=5)

        ctk.CTkLabel(trading_frame, text="仓位比例(%):").grid(row=3, column=0, padx=15, pady=5, sticky="w")
        self.macd_position_var = ctk.StringVar(value="10")
        ctk.CTkEntry(trading_frame, textvariable=self.macd_position_var, width=80).grid(row=3, column=1, padx=15, pady=(5, 15))

        # MACD信号显示区域
        signal_frame = ctk.CTkFrame(macd_container)
        signal_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=10)
        signal_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        signal_title = ctk.CTkLabel(
            signal_frame,
            text="📊 MACD信号状态",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        signal_title.grid(row=0, column=0, columnspan=4, padx=20, pady=(15, 10), sticky="w")

        # 信号卡片
        self.create_signal_card(signal_frame, "MACD值", "0.0125", "green", 0)
        self.create_signal_card(signal_frame, "信号线", "0.0098", "blue", 1)
        self.create_signal_card(signal_frame, "柱状图", "0.0027", "orange", 2)
        self.create_signal_card(signal_frame, "趋势", "上涨", "green", 3)

        # 控制按钮区域
        control_frame = ctk.CTkFrame(macd_container)
        control_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=10)

        start_macd_button = ctk.CTkButton(
            control_frame,
            text="🚀 启动MACD策略",
            command=self.start_macd_strategy,
            width=140,
            fg_color="green"
        )
        start_macd_button.grid(row=0, column=0, padx=20, pady=15)

        stop_macd_button = ctk.CTkButton(
            control_frame,
            text="⏹️ 停止MACD策略",
            command=self.stop_macd_strategy,
            width=140,
            fg_color="red"
        )
        stop_macd_button.grid(row=0, column=1, padx=20, pady=15)

        self.macd_status_label = ctk.CTkLabel(
            control_frame,
            text="⏸️ MACD策略状态: 未启动",
            font=ctk.CTkFont(size=14)
        )
        self.macd_status_label.grid(row=0, column=2, padx=20, pady=15, sticky="w")

    def create_pin_strategy_tab(self):
        """创建插针策略标签页"""
        # 插针策略容器
        pin_container = ctk.CTkFrame(self.content_frame)
        pin_container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        pin_container.grid_columnconfigure(0, weight=1)

        # 标题区域
        header_frame = ctk.CTkFrame(pin_container)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(
            header_frame,
            text="📉 插针策略配置",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 插针检测参数区域
        detection_frame = ctk.CTkFrame(pin_container)
        detection_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        detection_frame.grid_columnconfigure((0, 1), weight=1)

        detection_title = ctk.CTkLabel(
            detection_frame,
            text="🔍 插针检测参数",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        detection_title.grid(row=0, column=0, columnspan=2, padx=20, pady=(15, 10), sticky="w")

        # 检测参数
        params_left = ctk.CTkFrame(detection_frame)
        params_left.grid(row=1, column=0, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(params_left, text="插针幅度阈值(%):").grid(row=0, column=0, padx=15, pady=5, sticky="w")
        self.pin_threshold_var = ctk.StringVar(value="3.0")
        ctk.CTkEntry(params_left, textvariable=self.pin_threshold_var, width=100).grid(row=0, column=1, padx=15, pady=5)

        ctk.CTkLabel(params_left, text="回调确认比例(%):").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.pin_rebound_var = ctk.StringVar(value="50")
        ctk.CTkEntry(params_left, textvariable=self.pin_rebound_var, width=100).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(params_left, text="时间窗口(分钟):").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.pin_timeframe_var = ctk.StringVar(value="15")
        ctk.CTkEntry(params_left, textvariable=self.pin_timeframe_var, width=100).grid(row=2, column=1, padx=15, pady=(5, 15))

        params_right = ctk.CTkFrame(detection_frame)
        params_right.grid(row=1, column=1, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(params_right, text="成交量确认:").grid(row=0, column=0, padx=15, pady=5, sticky="w")
        self.pin_volume_confirm_var = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(params_right, text="", variable=self.pin_volume_confirm_var).grid(row=0, column=1, padx=15, pady=5)

        ctk.CTkLabel(params_right, text="多重时间框架:").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.pin_multi_timeframe_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(params_right, text="", variable=self.pin_multi_timeframe_var).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(params_right, text="趋势过滤:").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.pin_trend_filter_var = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(params_right, text="", variable=self.pin_trend_filter_var).grid(row=2, column=1, padx=15, pady=(5, 15))

        # 交易设置区域
        trading_frame = ctk.CTkFrame(pin_container)
        trading_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=10)
        trading_frame.grid_columnconfigure((0, 1, 2), weight=1)

        trading_title = ctk.CTkLabel(
            trading_frame,
            text="💰 交易设置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        trading_title.grid(row=0, column=0, columnspan=3, padx=20, pady=(15, 10), sticky="w")

        # 风险管理
        risk_frame = ctk.CTkFrame(trading_frame)
        risk_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(risk_frame, text="风险管理", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(risk_frame, text="止盈比例(%):").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.pin_take_profit_var = ctk.StringVar(value="2.5")
        ctk.CTkEntry(risk_frame, textvariable=self.pin_take_profit_var, width=80).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(risk_frame, text="止损比例(%):").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.pin_stop_loss_var = ctk.StringVar(value="1.5")
        ctk.CTkEntry(risk_frame, textvariable=self.pin_stop_loss_var, width=80).grid(row=2, column=1, padx=15, pady=(5, 15))

        # 仓位管理
        position_frame = ctk.CTkFrame(trading_frame)
        position_frame.grid(row=1, column=1, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(position_frame, text="仓位管理", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(position_frame, text="单次仓位(%):").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.pin_position_size_var = ctk.StringVar(value="5")
        ctk.CTkEntry(position_frame, textvariable=self.pin_position_size_var, width=80).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(position_frame, text="最大仓位(%):").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.pin_max_position_var = ctk.StringVar(value="20")
        ctk.CTkEntry(position_frame, textvariable=self.pin_max_position_var, width=80).grid(row=2, column=1, padx=15, pady=(5, 15))

        # 时间设置
        time_frame = ctk.CTkFrame(trading_frame)
        time_frame.grid(row=1, column=2, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(time_frame, text="时间设置", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(time_frame, text="持仓时间(小时):").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.pin_hold_time_var = ctk.StringVar(value="24")
        ctk.CTkEntry(time_frame, textvariable=self.pin_hold_time_var, width=80).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(time_frame, text="冷却时间(分钟):").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.pin_cooldown_var = ctk.StringVar(value="30")
        ctk.CTkEntry(time_frame, textvariable=self.pin_cooldown_var, width=80).grid(row=2, column=1, padx=15, pady=(5, 15))

        # 插针检测状态显示
        status_frame = ctk.CTkFrame(pin_container)
        status_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=10)
        status_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        status_title = ctk.CTkLabel(
            status_frame,
            text="📊 插针检测状态",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.grid(row=0, column=0, columnspan=4, padx=20, pady=(15, 10), sticky="w")

        # 状态卡片
        self.create_status_card(status_frame, "检测到插针", "3次", "orange", 0)
        self.create_status_card(status_frame, "成功交易", "2次", "green", 1)
        self.create_status_card(status_frame, "当前盈亏", "+1.2%", "green", 2)
        self.create_status_card(status_frame, "策略状态", "运行中", "blue", 3)

        # 控制按钮
        control_frame = ctk.CTkFrame(pin_container)
        control_frame.grid(row=4, column=0, sticky="ew", padx=10, pady=10)

        start_pin_button = ctk.CTkButton(
            control_frame,
            text="🚀 启动插针策略",
            command=self.start_pin_strategy,
            width=140,
            fg_color="green"
        )
        start_pin_button.grid(row=0, column=0, padx=20, pady=15)

        stop_pin_button = ctk.CTkButton(
            control_frame,
            text="⏹️ 停止插针策略",
            command=self.stop_pin_strategy,
            width=140,
            fg_color="red"
        )
        stop_pin_button.grid(row=0, column=1, padx=20, pady=15)

        self.pin_status_label = ctk.CTkLabel(
            control_frame,
            text="⏸️ 插针策略状态: 未启动",
            font=ctk.CTkFont(size=14)
        )
        self.pin_status_label.grid(row=0, column=2, padx=20, pady=15, sticky="w")

    def create_rsi_strategy_tab(self):
        """创建RSI策略标签页"""
        # RSI策略容器
        rsi_container = ctk.CTkFrame(self.content_frame)
        rsi_container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        rsi_container.grid_columnconfigure(0, weight=1)

        # 标题和实时RSI显示
        header_frame = ctk.CTkFrame(rsi_container)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        header_frame.grid_columnconfigure(1, weight=1)

        title = ctk.CTkLabel(
            header_frame,
            text="📊 RSI策略配置",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 实时RSI值显示
        self.rsi_value_label = ctk.CTkLabel(
            header_frame,
            text="当前RSI: 65.8",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="orange"
        )
        self.rsi_value_label.grid(row=0, column=1, padx=20, pady=15, sticky="e")

        # RSI参数配置
        params_frame = ctk.CTkFrame(rsi_container)
        params_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        params_frame.grid_columnconfigure((0, 1, 2), weight=1)

        params_title = ctk.CTkLabel(
            params_frame,
            text="⚙️ RSI参数配置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        params_title.grid(row=0, column=0, columnspan=3, padx=20, pady=(15, 10), sticky="w")

        # 基础参数
        basic_frame = ctk.CTkFrame(params_frame)
        basic_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(basic_frame, text="基础参数", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(basic_frame, text="RSI周期:").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.rsi_strategy_period_var = ctk.StringVar(value="14")
        ctk.CTkEntry(basic_frame, textvariable=self.rsi_strategy_period_var, width=80).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(basic_frame, text="超买线:").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.rsi_strategy_overbought_var = ctk.StringVar(value="70")
        ctk.CTkEntry(basic_frame, textvariable=self.rsi_strategy_overbought_var, width=80).grid(row=2, column=1, padx=15, pady=5)

        ctk.CTkLabel(basic_frame, text="超卖线:").grid(row=3, column=0, padx=15, pady=5, sticky="w")
        self.rsi_strategy_oversold_var = ctk.StringVar(value="30")
        ctk.CTkEntry(basic_frame, textvariable=self.rsi_strategy_oversold_var, width=80).grid(row=3, column=1, padx=15, pady=(5, 15))

        # 高级设置
        advanced_frame = ctk.CTkFrame(params_frame)
        advanced_frame.grid(row=1, column=1, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(advanced_frame, text="高级设置", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(advanced_frame, text="背离检测:").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.rsi_divergence_var = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(advanced_frame, text="", variable=self.rsi_divergence_var).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(advanced_frame, text="多重确认:").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.rsi_confirmation_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(advanced_frame, text="", variable=self.rsi_confirmation_var).grid(row=2, column=1, padx=15, pady=5)

        ctk.CTkLabel(advanced_frame, text="动态阈值:").grid(row=3, column=0, padx=15, pady=5, sticky="w")
        self.rsi_dynamic_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(advanced_frame, text="", variable=self.rsi_dynamic_var).grid(row=3, column=1, padx=15, pady=(5, 15))

        # 交易设置
        trading_frame = ctk.CTkFrame(params_frame)
        trading_frame.grid(row=1, column=2, sticky="ew", padx=10, pady=10)

        ctk.CTkLabel(trading_frame, text="交易设置", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(trading_frame, text="仓位比例(%):").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.rsi_position_var = ctk.StringVar(value="8")
        ctk.CTkEntry(trading_frame, textvariable=self.rsi_position_var, width=80).grid(row=1, column=1, padx=15, pady=5)

        ctk.CTkLabel(trading_frame, text="止盈比例(%):").grid(row=2, column=0, padx=15, pady=5, sticky="w")
        self.rsi_take_profit_var = ctk.StringVar(value="3.0")
        ctk.CTkEntry(trading_frame, textvariable=self.rsi_take_profit_var, width=80).grid(row=2, column=1, padx=15, pady=5)

        ctk.CTkLabel(trading_frame, text="止损比例(%):").grid(row=3, column=0, padx=15, pady=5, sticky="w")
        self.rsi_stop_loss_var = ctk.StringVar(value="1.5")
        ctk.CTkEntry(trading_frame, textvariable=self.rsi_stop_loss_var, width=80).grid(row=3, column=1, padx=15, pady=(5, 15))

        # RSI信号状态显示
        signal_frame = ctk.CTkFrame(rsi_container)
        signal_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=10)
        signal_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        signal_title = ctk.CTkLabel(
            signal_frame,
            text="📊 RSI信号状态",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        signal_title.grid(row=0, column=0, columnspan=4, padx=20, pady=(15, 10), sticky="w")

        # 信号卡片
        self.create_signal_card(signal_frame, "RSI值", "65.8", "orange", 0)
        self.create_signal_card(signal_frame, "信号", "中性", "gray", 1)
        self.create_signal_card(signal_frame, "趋势", "震荡", "blue", 2)
        self.create_signal_card(signal_frame, "强度", "中等", "orange", 3)

        # 控制按钮
        control_frame = ctk.CTkFrame(rsi_container)
        control_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=10)

        start_rsi_button = ctk.CTkButton(
            control_frame,
            text="🚀 启动RSI策略",
            command=self.start_rsi_strategy,
            width=140,
            fg_color="green"
        )
        start_rsi_button.grid(row=0, column=0, padx=20, pady=15)

        stop_rsi_button = ctk.CTkButton(
            control_frame,
            text="⏹️ 停止RSI策略",
            command=self.stop_rsi_strategy,
            width=140,
            fg_color="red"
        )
        stop_rsi_button.grid(row=0, column=1, padx=20, pady=15)

        self.rsi_strategy_status_label = ctk.CTkLabel(
            control_frame,
            text="⏸️ RSI策略状态: 未启动",
            font=ctk.CTkFont(size=14)
        )
        self.rsi_strategy_status_label.grid(row=0, column=2, padx=20, pady=15, sticky="w")

    def create_stop_profit_loss_tab(self):
        """创建止盈止损标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        container.grid_columnconfigure(0, weight=1)

        # 标题
        title = ctk.CTkLabel(container, text="💰 止盈止损配置", font=ctk.CTkFont(size=18, weight="bold"))
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 配置区域
        config_frame = ctk.CTkFrame(container)
        config_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        config_frame.grid_columnconfigure((0, 1), weight=1)

        # 止盈设置
        profit_frame = ctk.CTkFrame(config_frame)
        profit_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        ctk.CTkLabel(profit_frame, text="止盈设置", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(profit_frame, text="止盈比例(%):").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.take_profit_var = ctk.StringVar(value="3.0")
        ctk.CTkEntry(profit_frame, textvariable=self.take_profit_var, width=100).grid(row=1, column=1, padx=15, pady=5)

        # 止损设置
        loss_frame = ctk.CTkFrame(config_frame)
        loss_frame.grid(row=0, column=1, sticky="ew", padx=10, pady=10)
        ctk.CTkLabel(loss_frame, text="止损设置", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10))
        ctk.CTkLabel(loss_frame, text="止损比例(%):").grid(row=1, column=0, padx=15, pady=5, sticky="w")
        self.stop_loss_var = ctk.StringVar(value="1.5")
        ctk.CTkEntry(loss_frame, textvariable=self.stop_loss_var, width=100).grid(row=1, column=1, padx=15, pady=5)

    def create_equal_position_tab(self):
        """创建等量加仓标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(container, text="📈 等量加仓策略", font=ctk.CTkFont(size=18, weight="bold"))
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 加仓参数
        params_frame = ctk.CTkFrame(container)
        params_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        ctk.CTkLabel(params_frame, text="加仓间隔(%):").grid(row=0, column=0, padx=20, pady=10, sticky="w")
        self.position_interval_var = ctk.StringVar(value="2.0")
        ctk.CTkEntry(params_frame, textvariable=self.position_interval_var, width=100).grid(row=0, column=1, padx=20, pady=10)

    def create_risk_control_tab(self):
        """创建银行级风控标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(container, text="🏦 银行级风控系统", font=ctk.CTkFont(size=18, weight="bold"))
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 风控参数
        risk_frame = ctk.CTkFrame(container)
        risk_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        ctk.CTkLabel(risk_frame, text="最大回撤(%):").grid(row=0, column=0, padx=20, pady=10, sticky="w")
        self.max_drawdown_var = ctk.StringVar(value="5.0")
        ctk.CTkEntry(risk_frame, textvariable=self.max_drawdown_var, width=100).grid(row=0, column=1, padx=20, pady=10)

    def create_indicator_sync_tab(self):
        """创建指标同步标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(container, text="🔄 指标数据同步", font=ctk.CTkFont(size=18, weight="bold"))
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 同步状态
        status_frame = ctk.CTkFrame(container)
        status_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        self.sync_status_label = ctk.CTkLabel(status_frame, text="🟢 同步状态: 正常", font=ctk.CTkFont(size=14))
        self.sync_status_label.grid(row=0, column=0, padx=20, pady=15)

    def create_backtest_tab(self):
        """创建回测系统标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(container, text="📊 策略回测系统", font=ctk.CTkFont(size=18, weight="bold"))
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 回测参数
        backtest_frame = ctk.CTkFrame(container)
        backtest_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        ctk.CTkLabel(backtest_frame, text="回测周期:").grid(row=0, column=0, padx=20, pady=10, sticky="w")
        self.backtest_period_var = ctk.StringVar(value="30天")
        ctk.CTkComboBox(backtest_frame, values=["7天", "30天", "90天", "1年"], variable=self.backtest_period_var).grid(row=0, column=1, padx=20, pady=10)

    def create_optimization_tab(self):
        """创建参数优化标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(container, text="🧪 参数优化系统", font=ctk.CTkFont(size=18, weight="bold"))
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 优化参数
        opt_frame = ctk.CTkFrame(container)
        opt_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        ctk.CTkLabel(opt_frame, text="优化目标:").grid(row=0, column=0, padx=20, pady=10, sticky="w")
        self.opt_target_var = ctk.StringVar(value="最大收益")
        ctk.CTkComboBox(opt_frame, values=["最大收益", "最小风险", "夏普比率"], variable=self.opt_target_var).grid(row=0, column=1, padx=20, pady=10)

    def create_lstm_prediction_tab(self):
        """创建LSTM预测标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(container, text="🤖 LSTM价格预测", font=ctk.CTkFont(size=18, weight="bold"))
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 预测结果
        pred_frame = ctk.CTkFrame(container)
        pred_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        self.prediction_label = ctk.CTkLabel(pred_frame, text="📈 预测价格: $42,850 (+1.2%)", font=ctk.CTkFont(size=14, weight="bold"), text_color="green")
        self.prediction_label.grid(row=0, column=0, padx=20, pady=15)

    def create_ai_assistant_tab(self):
        """创建AI助手标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        container.grid_columnconfigure(0, weight=1)

        title = ctk.CTkLabel(container, text="🤖 AI智能助手", font=ctk.CTkFont(size=18, weight="bold"))
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 对话区域
        chat_frame = ctk.CTkFrame(container)
        chat_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        chat_frame.grid_columnconfigure(0, weight=1)

        self.chat_display = ctk.CTkTextbox(chat_frame, height=300)
        self.chat_display.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        self.chat_display.insert("1.0", "🤖 AI助手: 您好！我是您的交易助手，有什么可以帮助您的吗？\n\n")

        # 输入区域
        input_frame = ctk.CTkFrame(container)
        input_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=10)
        input_frame.grid_columnconfigure(0, weight=1)

        self.chat_input = ctk.CTkEntry(input_frame, placeholder_text="输入您的问题...")
        self.chat_input.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        send_button = ctk.CTkButton(input_frame, text="发送", command=self.send_chat_message, width=80)
        send_button.grid(row=0, column=1, padx=10, pady=10)

    def create_system_settings_tab(self):
        """创建系统设置标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(container, text="⚙️ 系统设置", font=ctk.CTkFont(size=18, weight="bold"))
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 系统参数
        settings_frame = ctk.CTkFrame(container)
        settings_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        ctk.CTkLabel(settings_frame, text="日志级别:").grid(row=0, column=0, padx=20, pady=10, sticky="w")
        self.log_level_setting_var = ctk.StringVar(value="INFO")
        ctk.CTkComboBox(settings_frame, values=["DEBUG", "INFO", "WARNING", "ERROR"], variable=self.log_level_setting_var).grid(row=0, column=1, padx=20, pady=10)

    def create_misc_config_tab(self):
        """创建杂项配置标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        title = ctk.CTkLabel(container, text="🔧 杂项配置", font=ctk.CTkFont(size=18, weight="bold"))
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 实验功能
        exp_frame = ctk.CTkFrame(container)
        exp_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        self.experimental_features_var = ctk.BooleanVar()
        ctk.CTkCheckBox(exp_frame, text="启用实验功能", variable=self.experimental_features_var).grid(row=0, column=0, padx=20, pady=15)

    def create_log_console_tab(self):
        """创建日志控制台标签页"""
        # 日志控制台容器
        log_container = ctk.CTkFrame(self.content_frame)
        log_container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        log_container.grid_columnconfigure(0, weight=1)

        # 标题和控制区域
        header_frame = ctk.CTkFrame(log_container)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        header_frame.grid_columnconfigure(1, weight=1)

        title = ctk.CTkLabel(
            header_frame,
            text="📜 日志控制台",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 日志级别选择
        level_label = ctk.CTkLabel(header_frame, text="日志级别:")
        level_label.grid(row=0, column=1, padx=20, pady=15, sticky="e")

        self.log_level_var = ctk.StringVar(value="INFO")
        level_combo = ctk.CTkComboBox(
            header_frame,
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            variable=self.log_level_var,
            command=self.change_log_level
        )
        level_combo.grid(row=0, column=2, padx=20, pady=15)

        # 清空日志按钮
        clear_button = ctk.CTkButton(
            header_frame,
            text="🗑️ 清空",
            command=self.clear_logs,
            width=80
        )
        clear_button.grid(row=0, column=3, padx=20, pady=15)

        # 日志显示区域
        log_frame = ctk.CTkFrame(log_container)
        log_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)

        # 日志文本框
        self.log_text = ctk.CTkTextbox(log_frame, height=400, font=ctk.CTkFont(family="Consolas", size=11))
        self.log_text.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        # 添加示例日志
        self.add_sample_logs()

    def create_placeholder_tab(self, title, description):
        """创建占位符标签页"""
        container = ctk.CTkFrame(self.content_frame)
        container.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        # 占位符图标
        icon_label = ctk.CTkLabel(
            container,
            text="🚧",
            font=ctk.CTkFont(size=48)
        )
        icon_label.grid(row=0, column=0, padx=20, pady=(50, 20))

        # 标题
        title_label = ctk.CTkLabel(
            container,
            text=title,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=1, column=0, padx=20, pady=10)

        # 描述
        desc_label = ctk.CTkLabel(
            container,
            text=f"{description}\n\n此页面正在开发中，敬请期待...",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        desc_label.grid(row=2, column=0, padx=20, pady=(10, 50))

    def add_sample_logs(self):
        """添加示例日志"""
        sample_logs = [
            "[2025-01-22 10:30:15] [INFO] 系统启动成功",
            "[2025-01-22 10:30:16] [INFO] 加载配置文件: modern_wmzc_config.json",
            "[2025-01-22 10:30:17] [INFO] 连接到OKX交易所",
            "[2025-01-22 10:30:18] [WARNING] API密钥未配置",
            "[2025-01-22 10:30:19] [INFO] 启动策略监控",
            "[2025-01-22 10:30:20] [DEBUG] 获取BTC-USDT价格: 42500.00",
            "[2025-01-22 10:30:21] [INFO] 策略信号: 插针策略触发买入信号",
            "[2025-01-22 10:30:22] [ERROR] 下单失败: 余额不足",
            "[2025-01-22 10:30:23] [INFO] 更新持仓信息",
            "[2025-01-22 10:30:24] [DEBUG] 内存使用: 125.6MB"
        ]

        for log in sample_logs:
            self.log_text.insert("end", log + "\n")

    def change_log_level(self, level):
        """改变日志级别"""
        self.update_status(f"日志级别已设置为 {level}")

    def clear_logs(self):
        """清空日志"""
        self.log_text.delete("1.0", "end")
        self.update_status("日志已清空")

    def send_chat_message(self):
        """发送聊天消息"""
        message = self.chat_input.get().strip()
        if message:
            # 显示用户消息
            self.chat_display.insert("end", f"👤 用户: {message}\n\n")

            # 模拟AI回复
            ai_response = self.generate_ai_response(message)
            self.chat_display.insert("end", f"🤖 AI助手: {ai_response}\n\n")

            # 清空输入框
            self.chat_input.delete(0, "end")

            # 滚动到底部
            self.chat_display.see("end")

    def generate_ai_response(self, message):
        """生成AI回复（模拟）"""
        message_lower = message.lower()

        if "价格" in message_lower or "行情" in message_lower:
            return "根据当前市场分析，BTC价格在42,500附近震荡，建议关注支撑位42,000和阻力位43,000。"
        elif "策略" in message_lower:
            return "建议使用多策略组合：插针策略捕捉短期机会，MACD策略把握趋势，RSI策略识别超买超卖。"
        elif "风险" in message_lower:
            return "当前市场波动较大，建议控制仓位在总资金的20%以内，设置合理的止盈止损。"
        elif "新闻" in message_lower:
            return "最新消息显示市场情绪偏向乐观，但需要关注宏观经济政策变化对加密货币的影响。"
        else:
            return "我理解您的问题。作为您的交易助手，我建议您关注市场趋势，合理配置资产，控制风险。如需具体建议，请提供更多详细信息。"

# ===== 与原WMZC系统的集成接口 =====

class WMZCModernGUIAdapter:
    """WMZC现代GUI适配器 - 用于与原系统集成"""

    def __init__(self, original_wmzc_instance=None):
        self.original_wmzc = original_wmzc_instance
        self.modern_gui = ModernWMZCGUI()

        # 如果有原实例，建立连接
        if self.original_wmzc:
            self.modern_gui.wmzc_instance = self.original_wmzc
            self.setup_data_binding()

    def setup_data_binding(self):
        """设置数据绑定"""
        # 这里将实现现代GUI与原WMZC系统的数据绑定
        pass

    def run(self):
        """运行现代GUI"""
        self.modern_gui.run()

    def get_config(self):
        """获取配置"""
        if self.original_wmzc and hasattr(self.original_wmzc, 'config'):
            return self.original_wmzc.config
        return {}

    def update_status(self, message):
        """更新状态"""
        self.modern_gui.update_status(message)

if __name__ == "__main__":
    app = ModernWMZCGUI()
    app.run()
