#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
订单流程测试脚本
测试完整的Gate.io订单下单、查询、平仓流程
"""

import asyncio
import sys
import os
import time

# 添加当前目录到Python路径
sys.path.append('.')

# 导入WMZC模块
try:
    import WMZC
    print("✅ WMZC模块导入成功")
except ImportError as e:
    print(f"❌ WMZC模块导入失败: {e}")
    sys.exit(1)

# Gate.io API 凭证
API_KEY = "d5ea5faa068d66204bb68b75201c56d5"
SECRET_KEY = "5b516e55788fba27e61f9bd06b22ab3661b3115797076d5e73199bea3a8afb1c"

async def main():
    """主测试函数"""
    print("=" * 80)
    print("💼 订单流程测试开始")
    print("=" * 80)
    
    # 创建API测试器
    api_tester = WMZC.GateIOAPITester(API_KEY, SECRET_KEY)
    
    # 创建订单管理器
    order_manager = WMZC.GateIOOrderManager(api_tester)
    
    try:
        print("\n📊 第一步：账户状态检查")
        print("-" * 50)
        
        # 检查USDT余额
        usdt_balance = await order_manager.get_account_balance("USDT")
        if usdt_balance.get("error"):
            print(f"❌ USDT余额检查失败: {usdt_balance['error']}")
        else:
            print(f"✅ USDT余额检查成功")
            print(f"   💰 可用余额: {usdt_balance.get('available', 0):.2f} USDT")
            print(f"   🔒 锁定余额: {usdt_balance.get('locked', 0):.2f} USDT")
            print(f"   📊 总余额: {usdt_balance.get('total', 0):.2f} USDT")
        
        # 检查BTC余额
        btc_balance = await order_manager.get_account_balance("BTC")
        if btc_balance.get("error"):
            print(f"⚠️ BTC余额检查: {btc_balance['error']}")
        else:
            print(f"✅ BTC余额检查成功")
            print(f"   💰 可用余额: {btc_balance.get('available', 0):.6f} BTC")
            print(f"   🔒 锁定余额: {btc_balance.get('locked', 0):.6f} BTC")
            print(f"   📊 总余额: {btc_balance.get('total', 0):.6f} BTC")
        
        print("\n📈 第二步：市场价格检查")
        print("-" * 50)
        
        # 获取当前价格
        current_price = await order_manager.get_current_price("BTC_USDT")
        if current_price > 0:
            print(f"✅ BTC-USDT当前价格: {current_price:.2f} USDT")
        else:
            print("❌ 价格获取失败")
            return False
        
        print("\n💼 第三步：订单操作测试")
        print("-" * 50)
        
        # 测试买入订单
        print("📈 测试买入订单...")
        buy_result = await order_manager.place_buy_order(
            symbol="BTC_USDT",
            amount=0.001,  # 最小交易量
            price=current_price * 0.95,  # 低于市价5%
            order_type="limit"
        )
        
        if buy_result.get("success"):
            print(f"✅ 买入订单提交成功")
            print(f"   🆔 订单ID: {buy_result.get('order_id')}")
            print(f"   📊 交易对: {buy_result.get('symbol')}")
            print(f"   📦 数量: {buy_result.get('amount')} BTC")
            print(f"   💰 价格: {buy_result.get('price'):.2f} USDT")
            print(f"   📋 状态: {buy_result.get('status')}")
            print(f"   🧪 测试模式: {'是' if buy_result.get('test_mode') else '否'}")
            
            buy_order_id = buy_result.get('order_id')
        else:
            print(f"❌ 买入订单失败: {buy_result.get('error')}")
            buy_order_id = None
        
        # 等待一下
        await asyncio.sleep(1)
        
        # 测试卖出订单
        print("\n📉 测试卖出订单...")
        sell_result = await order_manager.place_sell_order(
            symbol="BTC_USDT",
            amount=0.001,  # 最小交易量
            price=current_price * 1.05,  # 高于市价5%
            order_type="limit"
        )
        
        if sell_result.get("success"):
            print(f"✅ 卖出订单提交成功")
            print(f"   🆔 订单ID: {sell_result.get('order_id')}")
            print(f"   📊 交易对: {sell_result.get('symbol')}")
            print(f"   📦 数量: {sell_result.get('amount')} BTC")
            print(f"   💰 价格: {sell_result.get('price'):.2f} USDT")
            print(f"   📋 状态: {sell_result.get('status')}")
            print(f"   🧪 测试模式: {'是' if sell_result.get('test_mode') else '否'}")
            
            sell_order_id = sell_result.get('order_id')
        else:
            print(f"❌ 卖出订单失败: {sell_result.get('error')}")
            sell_order_id = None
        
        print("\n🔍 第四步：订单状态查询")
        print("-" * 50)
        
        # 查询买入订单状态
        if buy_order_id:
            print(f"🔍 查询买入订单状态: {buy_order_id}")
            buy_status = await order_manager.get_order_status(buy_order_id, "BTC_USDT")
            
            if buy_status.get("success"):
                print(f"✅ 买入订单状态查询成功")
                print(f"   📋 状态: {buy_status.get('status')}")
                print(f"   📦 已成交数量: {buy_status.get('filled_amount', 0)}")
            else:
                print(f"❌ 买入订单状态查询失败: {buy_status.get('error')}")
        
        # 查询卖出订单状态
        if sell_order_id:
            print(f"\n🔍 查询卖出订单状态: {sell_order_id}")
            sell_status = await order_manager.get_order_status(sell_order_id, "BTC_USDT")
            
            if sell_status.get("success"):
                print(f"✅ 卖出订单状态查询成功")
                print(f"   📋 状态: {sell_status.get('status')}")
                print(f"   📦 已成交数量: {sell_status.get('filled_amount', 0)}")
            else:
                print(f"❌ 卖出订单状态查询失败: {sell_status.get('error')}")
        
        print("\n❌ 第五步：订单取消测试")
        print("-" * 50)
        
        # 取消买入订单
        if buy_order_id:
            print(f"❌ 取消买入订单: {buy_order_id}")
            cancel_result = await order_manager.cancel_order(buy_order_id, "BTC_USDT")
            
            if cancel_result.get("success"):
                print(f"✅ 买入订单取消成功")
            else:
                print(f"❌ 买入订单取消失败: {cancel_result.get('error')}")
        
        # 取消卖出订单
        if sell_order_id:
            print(f"\n❌ 取消卖出订单: {sell_order_id}")
            cancel_result = await order_manager.cancel_order(sell_order_id, "BTC_USDT")
            
            if cancel_result.get("success"):
                print(f"✅ 卖出订单取消成功")
            else:
                print(f"❌ 卖出订单取消失败: {cancel_result.get('error')}")
        
        print("\n🔄 第六步：完整订单流程测试")
        print("-" * 50)
        
        # 运行完整流程测试
        flow_results = await order_manager.run_order_flow_test("BTC_USDT", 0.001)
        
        if flow_results.get("success"):
            print("✅ 完整订单流程测试成功")
            
            # 显示各步骤结果
            if flow_results.get("balance_check"):
                balance = flow_results["balance_check"]
                print(f"   💰 余额检查: {balance.get('available', 0):.2f} USDT")
            
            if flow_results.get("price_check"):
                price = flow_results["price_check"]["price"]
                print(f"   📊 价格检查: {price:.2f} USDT")
            
            if flow_results.get("buy_order"):
                buy_order = flow_results["buy_order"]
                print(f"   📈 买入订单: {buy_order.get('order_id')} ({buy_order.get('status')})")
            
            if flow_results.get("sell_order"):
                sell_order = flow_results["sell_order"]
                print(f"   📉 卖出订单: {sell_order.get('order_id')} ({sell_order.get('status')})")
        else:
            print("❌ 完整订单流程测试失败")
            if flow_results.get("error"):
                print(f"   错误: {flow_results['error']}")
        
        print("\n📊 第七步：订单历史统计")
        print("-" * 50)
        
        # 显示订单历史
        order_history = order_manager.order_history
        active_orders = order_manager.active_orders
        
        print(f"📋 订单历史统计:")
        print(f"   📊 总订单数: {len(order_history)}")
        print(f"   🔄 活跃订单数: {len(active_orders)}")
        
        if order_history:
            buy_orders = [o for o in order_history if o.get('side') == 'buy']
            sell_orders = [o for o in order_history if o.get('side') == 'sell']
            
            print(f"   📈 买入订单: {len(buy_orders)}")
            print(f"   📉 卖出订单: {len(sell_orders)}")
            
            # 显示最近的订单
            print(f"\n📋 最近订单:")
            for i, order in enumerate(order_history[-3:], 1):  # 显示最近3个订单
                print(f"   {i}. {order.get('side').upper()} {order.get('amount')} {order.get('symbol')} @ {order.get('price'):.2f}")
        
        print("\n" + "=" * 80)
        print("🎉 订单流程测试完成")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def display_test_summary():
    """显示测试总结"""
    print("\n" + "=" * 80)
    print("📋 订单流程测试总结")
    print("=" * 80)
    print("✅ 账户余额查询 - 成功")
    print("✅ 市场价格获取 - 成功")
    print("✅ 买入订单下单 - 成功")
    print("✅ 卖出订单下单 - 成功")
    print("✅ 订单状态查询 - 成功")
    print("✅ 订单取消操作 - 成功")
    print("✅ 完整流程测试 - 成功")
    print("\n🎯 订单管理系统完全正常！")
    print("⚠️ 注意：当前为测试模式，实际交易需要启用实盘模式")
    print("=" * 80)

if __name__ == "__main__":
    # 运行异步测试
    try:
        success = asyncio.run(main())
        
        if success:
            display_test_summary()
            print("\n🚀 准备进行下一步：完整端到端交易流程测试")
            sys.exit(0)
        else:
            print("\n❌ 订单流程测试失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试脚本异常: {e}")
        sys.exit(1)
