#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API余额获取功能测试脚本
用于验证修复后的余额获取功能是否正常工作
"""

import sys
import os
import traceback
import json
from datetime import datetime

def log(message, level="INFO"):
    """简单日志函数"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [{level}] {message}")

def test_api_balance_functionality():
    """测试API余额获取功能"""
    log("🚀 开始API余额获取功能测试", "INFO")
    
    try:
        # 导入WMZC模块
        log("📦 导入WMZC模块...", "INFO")
        import WMZC
        
        # 测试1: 检查UnifiedExchangeManager是否存在
        log("🔍 测试1: 检查UnifiedExchangeManager", "INFO")
        if hasattr(WMZC, 'UnifiedExchangeManager'):
            log("✅ UnifiedExchangeManager类存在", "INFO")
            
            # 创建管理器实例
            try:
                manager = WMZC.UnifiedExchangeManager()
                log("✅ UnifiedExchangeManager实例创建成功", "INFO")
                
                # 测试2: 检查get_balance方法
                log("🔍 测试2: 检查get_balance方法", "INFO")
                if hasattr(manager, 'get_balance'):
                    log("✅ get_balance方法存在", "INFO")
                    
                    # 测试3: 尝试获取余额
                    log("🔍 测试3: 尝试获取余额", "INFO")
                    try:
                        balance = manager.get_balance()
                        log(f"📊 余额获取结果: {balance}", "INFO")
                        
                        # 验证返回格式
                        if isinstance(balance, dict):
                            log("✅ 返回格式正确 (dict)", "INFO")
                            
                            # 检查是否包含API错误标记
                            if balance.get('api_error'):
                                log("⚠️ 检测到API错误标记，这是正常的（API未配置或连接失败）", "WARNING")
                            else:
                                log("✅ 余额数据获取成功", "INFO")
                                
                            # 检查USDT余额结构
                            if 'USDT' in balance:
                                usdt_info = balance['USDT']
                                if all(key in usdt_info for key in ['free', 'used', 'total']):
                                    log("✅ USDT余额结构正确", "INFO")
                                else:
                                    log("⚠️ USDT余额结构不完整", "WARNING")
                            
                        else:
                            log(f"❌ 返回格式错误: {type(balance)}", "ERROR")
                            
                    except Exception as balance_error:
                        log(f"❌ 余额获取失败: {balance_error}", "ERROR")
                        log(f"🔍 错误详情: {traceback.format_exc()}", "DEBUG")
                        
                else:
                    log("❌ get_balance方法不存在", "ERROR")
                    
            except Exception as manager_error:
                log(f"❌ UnifiedExchangeManager实例创建失败: {manager_error}", "ERROR")
                
        else:
            log("❌ UnifiedExchangeManager类不存在", "ERROR")
            
        # 测试4: 检查Gate.io客户端
        log("🔍 测试4: 检查Gate.io客户端", "INFO")
        if hasattr(WMZC, 'GateClient'):
            log("✅ GateClient类存在", "INFO")
            
            # 尝试创建Gate.io客户端（使用空配置）
            try:
                gate_config = {
                    'GATE_API_KEY': '',
                    'GATE_SECRET_KEY': ''
                }
                gate_client = WMZC.GateClient(gate_config)
                log("✅ GateClient实例创建成功", "INFO")
                
                # 检查get_balance_sync方法
                if hasattr(gate_client, 'get_balance_sync'):
                    log("✅ get_balance_sync方法存在", "INFO")
                    
                    # 测试方法调用（预期会失败，因为没有API密钥）
                    try:
                        gate_balance = gate_client.get_balance_sync()
                        log(f"📊 Gate.io余额结果: {gate_balance}", "INFO")
                    except Exception as gate_balance_error:
                        log(f"⚠️ Gate.io余额获取失败（预期的，因为API密钥未配置）: {gate_balance_error}", "WARNING")
                        
                else:
                    log("❌ get_balance_sync方法不存在", "ERROR")
                    
            except Exception as gate_error:
                log(f"❌ GateClient创建失败: {gate_error}", "ERROR")
                
        else:
            log("❌ GateClient类不存在", "ERROR")
            
        # 测试5: 检查全局余额获取函数
        log("🔍 测试5: 检查全局余额获取函数", "INFO")
        if hasattr(WMZC, 'get_balance'):
            log("✅ 全局get_balance函数存在", "INFO")
            
            try:
                global_balance = WMZC.get_balance()
                log(f"📊 全局余额获取结果: {global_balance}", "INFO")
            except Exception as global_error:
                log(f"⚠️ 全局余额获取失败: {global_error}", "WARNING")
                
        else:
            log("❌ 全局get_balance函数不存在", "ERROR")
            
    except ImportError as import_error:
        log(f"❌ WMZC模块导入失败: {import_error}", "ERROR")
        return False
        
    except Exception as e:
        log(f"❌ 测试过程中发生未知错误: {e}", "ERROR")
        log(f"🔍 错误详情: {traceback.format_exc()}", "DEBUG")
        return False
        
    log("🎉 API余额获取功能测试完成", "INFO")
    return True

def test_api_configuration():
    """测试API配置"""
    log("🔍 测试API配置", "INFO")
    
    try:
        # 检查配置文件
        config_files = ['trading_config.json', 'wmzc_config.json']
        
        for config_file in config_files:
            if os.path.exists(config_file):
                log(f"✅ 找到配置文件: {config_file}", "INFO")
                
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                        
                    # 检查API密钥配置
                    api_keys = {
                        'OKX_API_KEY': config_data.get('OKX_API_KEY', ''),
                        'OKX_SECRET_KEY': config_data.get('OKX_SECRET_KEY', ''),
                        'OKX_PASSPHRASE': config_data.get('OKX_PASSPHRASE', ''),
                        'GATE_API_KEY': config_data.get('GATE_API_KEY', ''),
                        'GATE_SECRET_KEY': config_data.get('GATE_SECRET_KEY', '')
                    }
                    
                    for key, value in api_keys.items():
                        if value:
                            log(f"✅ {key}: 已配置", "INFO")
                        else:
                            log(f"⚠️ {key}: 未配置", "WARNING")
                            
                except Exception as parse_error:
                    log(f"❌ 解析配置文件失败: {parse_error}", "ERROR")
                    
            else:
                log(f"⚠️ 配置文件不存在: {config_file}", "WARNING")
                
    except Exception as e:
        log(f"❌ API配置测试失败: {e}", "ERROR")

def main():
    """主函数"""
    log("=" * 60, "INFO")
    log("🔧 API余额获取功能修复验证测试", "INFO")
    log("=" * 60, "INFO")
    
    # 测试API配置
    test_api_configuration()
    
    print()  # 空行分隔
    
    # 测试API余额功能
    success = test_api_balance_functionality()
    
    print()  # 空行分隔
    log("=" * 60, "INFO")
    
    if success:
        log("🎉 测试完成！API余额获取功能修复验证通过", "INFO")
        log("💡 如果看到API错误，请检查API密钥配置", "INFO")
    else:
        log("❌ 测试失败！需要进一步检查代码", "ERROR")
        
    log("=" * 60, "INFO")

if __name__ == "__main__":
    main()
