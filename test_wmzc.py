#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的WMZC模块测试
"""

print("🔧 开始测试WMZC模块...")

try:
    print("📦 正在导入WMZC模块...")
    import WMZC
    print("✅ WMZC模块导入成功！")
    
    # 测试基本功能
    print("🧪 测试基本功能...")
    
    # 检查关键函数是否存在
    functions_to_check = [
        '_safe_dataframe_check',
        '_ensure_config_defaults',
        'load_config_from_file',
        'calculate_macd',
        'calculate_rsi',
        'calculate_kdj'
    ]
    
    for func_name in functions_to_check:
        if hasattr(WMZC, func_name):
            print(f"   ✅ {func_name} 函数存在")
        else:
            print(f"   ❌ {func_name} 函数缺失")
    
    # 检查关键类是否存在
    classes_to_check = [
        'TechnicalIndicatorCalculator',
        'TradingApp'
    ]
    
    for class_name in classes_to_check:
        if hasattr(WMZC, class_name):
            print(f"   ✅ {class_name} 类存在")
        else:
            print(f"   ❌ {class_name} 类缺失")
    
    print("🎉 WMZC模块测试完成！")
    
except SyntaxError as e:
    print(f"❌ 语法错误: {e}")
    print(f"   文件: {e.filename}")
    print(f"   行号: {e.lineno}")
    print(f"   错误: {e.msg}")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()

print("测试结束")
