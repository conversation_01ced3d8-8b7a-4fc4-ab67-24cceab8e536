
🔧 WMZC系统闪退问题故障排除指南

🚨 常见闪退原因:

1. 📡 API认证失败 (403 Forbidden)
   原因: API密钥错误、IP白名单未设置、权限不足
   解决: 检查API密钥配置，确保IP在白名单中

2. 📊 技术指标计算错误
   原因: DataFrame布尔值判断异常
   解决: 使用修复后的技术指标计算函数

3. 💾 内存不足或资源耗尽
   原因: 长时间运行导致内存泄漏
   解决: 定期重启系统，优化内存使用

4. 🔌 网络连接问题
   原因: 网络不稳定导致API调用失败
   解决: 检查网络连接，使用稳定的网络环境

🔧 解决步骤:

1. 🔒 使用安全模式启动
   python safe_startup.py
   
2. 📋 检查系统日志
   查看控制台输出，找到具体错误信息
   
3. 🔧 修复API配置
   - 确保使用真实的API密钥
   - 检查IP白名单设置
   - 验证API权限
   
4. 🛡️ 应用修复补丁
   - 使用技术指标修复脚本
   - 启用异常保护机制
   
5. 🔄 逐步启用功能
   - 先在安全模式下测试
   - 逐步启用各项功能
   - 确认系统稳定后恢复正常模式

💡 预防措施:

1. 定期备份配置文件
2. 使用测试模式验证新配置
3. 监控系统资源使用情况
4. 保持网络连接稳定
5. 及时更新系统补丁

🆘 紧急恢复:

如果系统持续闪退:
1. python safe_startup.py restore  # 恢复备份配置
2. 删除可能损坏的配置文件
3. 重新配置API密钥
4. 在安全模式下重新启动
