#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC系统全面Bug检测器
100%理解系统后的主动Bug发现和修复工具
"""

import ast
import re
import os
import sys
from collections import defaultdict, Counter
from typing import List, Dict, Set, Tuple

class BugDetectionResult:
    def __init__(self, bug_id: str, severity: str, category: str, description: str, 
                 location: str, code_snippet: str, fix_suggestion: str, impact: str):
        self.bug_id = bug_id
        self.severity = severity  # 🔴致命 🟠高危 🟡中危 🟢低危
        self.category = category
        self.description = description
        self.location = location
        self.code_snippet = code_snippet
        self.fix_suggestion = fix_suggestion
        self.impact = impact

class WMZCBugDetector:
    """WMZC系统Bug检测器"""
    
    def __init__(self, file_path: str = "WMZC.py"):
        self.file_path = file_path
        self.bugs = []
        self.code_lines = []
        self.imports = set()
        self.classes = {}
        self.functions = {}
        self.variables = set()
        
    def load_and_parse_code(self):
        """加载和解析代码"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                self.code_lines = content.split('\n')
            
            # 解析AST
            try:
                self.ast_tree = ast.parse(content)
                self._extract_ast_info()
            except SyntaxError as e:
                self.add_bug("SYNTAX_001", "🔴致命", "语法错误", 
                           f"Python语法错误: {e}", f"第{e.lineno}行", 
                           self.code_lines[e.lineno-1] if e.lineno <= len(self.code_lines) else "",
                           "修复语法错误", "系统无法启动")
            
            print(f"✅ 成功加载 {len(self.code_lines)} 行代码")
            
        except Exception as e:
            print(f"❌ 加载代码失败: {e}")
            
    def _extract_ast_info(self):
        """从AST提取信息"""
        for node in ast.walk(self.ast_tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    self.imports.add(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    self.imports.add(node.module)
            elif isinstance(node, ast.ClassDef):
                self.classes[node.name] = node.lineno
            elif isinstance(node, ast.FunctionDef):
                self.functions[node.name] = node.lineno
            elif isinstance(node, ast.Name):
                self.variables.add(node.id)
    
    def add_bug(self, bug_id: str, severity: str, category: str, description: str,
                location: str, code_snippet: str, fix_suggestion: str, impact: str):
        """添加Bug记录"""
        bug = BugDetectionResult(bug_id, severity, category, description,
                               location, code_snippet, fix_suggestion, impact)
        self.bugs.append(bug)
    
    def detect_import_issues(self):
        """检测导入问题"""
        print("🔍 检测导入问题...")
        
        # 检测重复导入
        import_lines = {}
        for i, line in enumerate(self.code_lines, 1):
            line = line.strip()
            if line.startswith('import ') or line.startswith('from '):
                # 提取导入的模块名
                if line.startswith('import '):
                    module = line.split('import ')[1].split(' as ')[0].split(',')[0].strip()
                elif line.startswith('from '):
                    module = line.split('from ')[1].split(' import')[0].strip()
                
                if module in import_lines:
                    self.add_bug(f"IMPORT_{len(self.bugs)+1:03d}", "🟡中危", "重复导入",
                               f"模块 '{module}' 被重复导入",
                               f"第{i}行 (首次导入: 第{import_lines[module]}行)",
                               line, f"删除重复的导入语句", "代码冗余，可能导致混淆")
                else:
                    import_lines[module] = i
        
        # 检测未使用的导入
        unused_imports = []
        for module in self.imports:
            if module not in ['time', 'os', 'sys', 'json']:  # 排除常用模块
                module_used = False
                for line in self.code_lines:
                    if module in line and not line.strip().startswith(('import', 'from')):
                        module_used = True
                        break
                if not module_used:
                    unused_imports.append(module)
        
        for module in unused_imports[:5]:  # 限制报告数量
            self.add_bug(f"IMPORT_{len(self.bugs)+1:03d}", "🟢低危", "未使用导入",
                       f"导入的模块 '{module}' 未被使用",
                       "导入区域", f"import {module}",
                       f"删除未使用的导入: import {module}", "代码冗余")
    
    def detect_duplicate_functions(self):
        """检测重复函数定义"""
        print("🔍 检测重复函数...")
        
        function_definitions = defaultdict(list)
        
        for i, line in enumerate(self.code_lines, 1):
            line = line.strip()
            if line.startswith('def '):
                func_name = line.split('def ')[1].split('(')[0].strip()
                function_definitions[func_name].append((i, line))
        
        for func_name, definitions in function_definitions.items():
            if len(definitions) > 1:
                locations = [f"第{line_num}行" for line_num, _ in definitions]
                self.add_bug(f"DUPLICATE_{len(self.bugs)+1:03d}", "🟠高危", "重复函数",
                           f"函数 '{func_name}' 被定义了 {len(definitions)} 次",
                           ", ".join(locations),
                           definitions[0][1],
                           f"保留最完整的 '{func_name}' 定义，删除其他重复定义",
                           "可能导致函数行为不一致")
    
    def detect_async_issues(self):
        """检测异步编程问题"""
        print("🔍 检测异步编程问题...")
        
        for i, line in enumerate(self.code_lines, 1):
            line_stripped = line.strip()
            
            # 检测同步阻塞调用
            if 'time.sleep(' in line and 'async def' in ''.join(self.code_lines[max(0, i-10):i]):
                self.add_bug(f"ASYNC_{len(self.bugs)+1:03d}", "🟠高危", "异步阻塞",
                           "在异步函数中使用了阻塞的 time.sleep()",
                           f"第{i}行", line_stripped,
                           "使用 await asyncio.sleep() 替代 time.sleep()",
                           "阻塞事件循环，影响性能")
            
            # 检测未使用await的异步调用
            if re.search(r'(?<!await\s)async\s+def\s+\w+\s*\(', line):
                # 这是异步函数定义，检查调用
                pass
            
            # 检测threading在异步环境中的使用
            if 'threading.Thread' in line and 'async def' in ''.join(self.code_lines[max(0, i-10):i]):
                self.add_bug(f"ASYNC_{len(self.bugs)+1:03d}", "🟡中危", "异步架构违规",
                           "在异步环境中使用了threading.Thread",
                           f"第{i}行", line_stripped,
                           "使用 asyncio.create_task() 或 asyncio.gather()",
                           "违反异步编程原则")
    
    def detect_exception_handling_issues(self):
        """检测异常处理问题"""
        print("🔍 检测异常处理问题...")
        
        bare_except_count = 0
        for i, line in enumerate(self.code_lines, 1):
            line_stripped = line.strip()
            
            # 检测裸露的except
            if line_stripped == 'except:' or line_stripped.startswith('except:'):
                bare_except_count += 1
                self.add_bug(f"EXCEPT_{len(self.bugs)+1:03d}", "🟡中危", "裸露异常处理",
                           "使用了裸露的 except: 语句",
                           f"第{i}行", line_stripped,
                           "指定具体的异常类型，如 except Exception as e:",
                           "可能掩盖重要错误")
            
            # 检测空的异常处理
            if line_stripped.startswith('except') and i < len(self.code_lines):
                next_line = self.code_lines[i].strip() if i < len(self.code_lines) else ""
                if next_line == 'pass':
                    self.add_bug(f"EXCEPT_{len(self.bugs)+1:03d}", "🟡中危", "空异常处理",
                               "异常处理块为空（只有pass）",
                               f"第{i}-{i+1}行", f"{line_stripped}\n    {next_line}",
                               "添加适当的错误处理逻辑或日志记录",
                               "错误被静默忽略")
    
    def detect_memory_issues(self):
        """检测内存问题"""
        print("🔍 检测内存问题...")
        
        for i, line in enumerate(self.code_lines, 1):
            line_stripped = line.strip()
            
            # 检测大列表创建
            if re.search(r'\[\s*.*\s*for\s+.*\s+in\s+range\s*\(\s*\d{4,}\s*\)', line):
                self.add_bug(f"MEMORY_{len(self.bugs)+1:03d}", "🟡中危", "内存使用",
                           "创建了大型列表推导式",
                           f"第{i}行", line_stripped,
                           "考虑使用生成器表达式或分批处理",
                           "可能消耗大量内存")
            
            # 检测全局变量过多
            if line_stripped.startswith('global '):
                global_vars = line_stripped.replace('global ', '').split(',')
                if len(global_vars) > 3:
                    self.add_bug(f"MEMORY_{len(self.bugs)+1:03d}", "🟢低危", "全局变量",
                               f"单行声明了 {len(global_vars)} 个全局变量",
                               f"第{i}行", line_stripped,
                               "减少全局变量使用，考虑使用类或配置对象",
                               "增加代码复杂度")
    
    def detect_security_issues(self):
        """检测安全问题"""
        print("🔍 检测安全问题...")
        
        for i, line in enumerate(self.code_lines, 1):
            line_stripped = line.strip()
            
            # 检测硬编码密钥
            if re.search(r'["\'][a-zA-Z0-9]{20,}["\']', line) and any(keyword in line.lower() 
                        for keyword in ['key', 'secret', 'token', 'password']):
                self.add_bug(f"SECURITY_{len(self.bugs)+1:03d}", "🔴致命", "硬编码密钥",
                           "发现可能的硬编码API密钥或密码",
                           f"第{i}行", "***敏感信息已隐藏***",
                           "使用环境变量或配置文件存储敏感信息",
                           "安全风险，密钥可能泄露")
            
            # 检测eval使用
            if 'eval(' in line:
                self.add_bug(f"SECURITY_{len(self.bugs)+1:03d}", "🔴致命", "代码注入风险",
                           "使用了危险的 eval() 函数",
                           f"第{i}行", line_stripped,
                           "使用更安全的替代方案，如 ast.literal_eval()",
                           "代码注入安全风险")
    
    def detect_performance_issues(self):
        """检测性能问题"""
        print("🔍 检测性能问题...")
        
        for i, line in enumerate(self.code_lines, 1):
            line_stripped = line.strip()
            
            # 检测循环中的重复计算
            if 'for ' in line and 'in range(' in line:
                # 检查循环体中是否有重复计算
                loop_body_start = i
                loop_body_end = i + 20  # 检查接下来20行
                
                for j in range(loop_body_start, min(loop_body_end, len(self.code_lines))):
                    if 'len(' in self.code_lines[j] and 'for' not in self.code_lines[j]:
                        self.add_bug(f"PERF_{len(self.bugs)+1:03d}", "🟢低危", "性能优化",
                                   "循环中可能存在重复的len()计算",
                                   f"第{i}-{j+1}行", line_stripped,
                                   "将len()结果缓存到变量中",
                                   "轻微性能影响")
                        break
    
    def detect_code_quality_issues(self):
        """检测代码质量问题"""
        print("🔍 检测代码质量问题...")
        
        # 检测过长的行
        for i, line in enumerate(self.code_lines, 1):
            if len(line) > 120:
                self.add_bug(f"QUALITY_{len(self.bugs)+1:03d}", "🟢低危", "代码格式",
                           f"代码行过长 ({len(line)} 字符)",
                           f"第{i}行", line[:50] + "...",
                           "将长行拆分为多行",
                           "影响代码可读性")
        
        # 检测过深的嵌套
        max_indent = 0
        for i, line in enumerate(self.code_lines, 1):
            indent_level = (len(line) - len(line.lstrip())) // 4
            if indent_level > max_indent:
                max_indent = indent_level
            
            if indent_level > 6:
                self.add_bug(f"QUALITY_{len(self.bugs)+1:03d}", "🟡中危", "代码复杂度",
                           f"嵌套层级过深 ({indent_level} 层)",
                           f"第{i}行", line.strip(),
                           "重构代码，减少嵌套层级",
                           "影响代码可维护性")
    
    def run_comprehensive_detection(self):
        """运行全面检测"""
        print("🚀 开始WMZC系统全面Bug检测...")
        
        self.load_and_parse_code()
        
        # 运行各种检测
        self.detect_import_issues()
        self.detect_duplicate_functions()
        self.detect_async_issues()
        self.detect_exception_handling_issues()
        self.detect_memory_issues()
        self.detect_security_issues()
        self.detect_performance_issues()
        self.detect_code_quality_issues()
        
        return self.bugs
    
    def generate_report(self):
        """生成检测报告"""
        if not self.bugs:
            print("🎉 未发现任何Bug！代码质量优秀！")
            return
        
        # 按严重程度分类
        severity_counts = Counter(bug.severity for bug in self.bugs)
        
        print(f"\n📊 Bug检测报告")
        print("=" * 80)
        print(f"总计发现 {len(self.bugs)} 个问题:")
        for severity, count in severity_counts.items():
            print(f"  {severity}: {count} 个")
        
        print("\n🔍 详细问题列表:")
        print("=" * 80)
        
        # 按严重程度排序
        severity_order = {"🔴致命": 0, "🟠高危": 1, "🟡中危": 2, "🟢低危": 3}
        sorted_bugs = sorted(self.bugs, key=lambda x: severity_order.get(x.severity, 4))
        
        for i, bug in enumerate(sorted_bugs, 1):
            print(f"\n{i}. {bug.severity} {bug.bug_id}: {bug.description}")
            print(f"   📍 位置: {bug.location}")
            print(f"   📝 代码: {bug.code_snippet[:100]}...")
            print(f"   🔧 建议: {bug.fix_suggestion}")
            print(f"   💥 影响: {bug.impact}")
    
    def generate_fix_script(self):
        """生成修复脚本"""
        critical_bugs = [bug for bug in self.bugs if bug.severity in ["🔴致命", "🟠高危"]]
        
        if not critical_bugs:
            print("✅ 无需生成修复脚本，没有严重Bug")
            return
        
        print(f"\n🔧 生成修复脚本 (处理 {len(critical_bugs)} 个严重问题)")
        
        fix_script = []
        fix_script.append("#!/usr/bin/env python3")
        fix_script.append("# WMZC系统自动修复脚本")
        fix_script.append("# 此脚本修复检测到的严重Bug")
        fix_script.append("")
        
        for bug in critical_bugs:
            fix_script.append(f"# 修复 {bug.bug_id}: {bug.description}")
            fix_script.append(f"# 位置: {bug.location}")
            fix_script.append(f"# 建议: {bug.fix_suggestion}")
            fix_script.append("")
        
        with open("wmzc_auto_fix.py", "w", encoding="utf-8") as f:
            f.write("\n".join(fix_script))
        
        print("💾 修复脚本已保存到: wmzc_auto_fix.py")

def main():
    """主函数"""
    detector = WMZCBugDetector()
    bugs = detector.run_comprehensive_detection()
    detector.generate_report()
    detector.generate_fix_script()
    
    return len(bugs)

if __name__ == "__main__":
    bug_count = main()
    sys.exit(0 if bug_count == 0 else 1)
