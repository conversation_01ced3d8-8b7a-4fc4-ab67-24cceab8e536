#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 WMZC交易日志测试工具
验证K线数据获取和技术指标计算日志是否正常显示
"""

import time
import asyncio
import threading
from datetime import datetime

def test_trading_logs():
    """测试交易日志功能"""
    print("🧪 WMZC交易日志测试工具")
    print("=" * 60)
    
    try:
        # 1. 导入WMZC模块
        print("📦 导入WMZC模块...")
        import WMZC
        
        # 2. 检查全局app变量
        if hasattr(WMZC, 'app') and WMZC.app:
            print("✅ 找到WMZC应用实例")
            app = WMZC.app
        else:
            print("❌ 未找到WMZC应用实例")
            return False
        
        # 3. 测试日志函数
        print("\n🔍 测试日志函数...")
        
        # 测试全局log函数
        if hasattr(WMZC, 'log'):
            print("✅ 找到全局log函数")
            WMZC.log("🧪 测试全局log函数", "INFO")
            time.sleep(1)
        else:
            print("❌ 未找到全局log函数")
        
        # 测试直接GUI日志
        if hasattr(app, 'add_log_message'):
            print("✅ 找到GUI日志函数")
            app.add_log_message("🧪 测试GUI日志函数", "INFO")
            time.sleep(1)
        else:
            print("❌ 未找到GUI日志函数")
        
        # 4. 模拟K线数据获取日志
        print("\n📡 模拟K线数据获取日志...")
        simulate_kline_logs(app)
        
        # 5. 模拟技术指标计算日志
        print("\n📈 模拟技术指标计算日志...")
        simulate_indicator_logs(app)
        
        # 6. 模拟交易信号日志
        print("\n🚨 模拟交易信号日志...")
        simulate_signal_logs(app)
        
        # 7. 启动持续日志测试
        print("\n🔄 启动持续日志测试...")
        start_continuous_test(app)
        
        return True
        
    except ImportError:
        print("❌ 无法导入WMZC模块，请确保WMZC系统正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def simulate_kline_logs(app):
    """模拟K线数据获取日志"""
    kline_logs = [
        ("📡 开始获取K线数据: BTC-USDT-SWAP 1m (limit=100)", "INFO"),
        ("🔗 发送API请求: GET /api/v5/market/candles", "INFO"),
        ("📋 请求参数: {'instId': 'BTC-USDT-SWAP', 'bar': '1m', 'limit': '100'}", "DEBUG"),
        ("📊 收到原始K线数据: 100 条", "INFO"),
        ("💰 最新价格: $98,456.78 (开盘: $98,234.56)", "INFO"),
        ("📊 价格区间: $98,123.45 - $98,567.89", "INFO"),
        ("📈 成交量: 1,234.56", "INFO"),
        ("✅ K线数据获取成功: 100 条", "INFO")
    ]
    
    for message, level in kline_logs:
        timestamp = datetime.now().strftime('%H:%M:%S')
        formatted_msg = f"[{timestamp}] {level} - {message}"
        app.add_log_message(formatted_msg, level)
        time.sleep(0.5)

def simulate_indicator_logs(app):
    """模拟技术指标计算日志"""
    indicator_logs = [
        ("🚀 开始技术指标计算测试", "INFO"),
        ("📊 目标交易对: BTC-USDT-SWAP", "INFO"),
        ("✅ K线数据获取完成，开始计算技术指标...", "INFO"),
        ("📈 计算MACD指标...", "INFO"),
        ("✅ MACD 计算成功: MACD=0.123456, Signal=0.098765, Hist=0.024691", "INFO"),
        ("📈 计算KDJ随机指标...", "INFO"),
        ("✅ KDJ 计算成功: K=45.23, D=42.18, J=51.33", "INFO"),
        ("📈 KDJ金叉信号: K > D", "INFO"),
        ("📈 计算RSI相对强弱指标...", "INFO"),
        ("✅ RSI 计算成功: 52.34", "INFO"),
        ("📊 RSI处于正常区间", "INFO"),
        ("🎉 所有技术指标计算成功！", "INFO"),
        ("💡 可以开始生成交易信号", "INFO")
    ]
    
    for message, level in indicator_logs:
        timestamp = datetime.now().strftime('%H:%M:%S')
        formatted_msg = f"[{timestamp}] {level} - {message}"
        app.add_log_message(formatted_msg, level)
        time.sleep(0.8)

def simulate_signal_logs(app):
    """模拟交易信号日志"""
    signal_logs = [
        ("🔍 分析市场信号...", "INFO"),
        ("📊 MACD分析: DIF(0.123) > DEA(0.098) - 金叉形成", "INFO"),
        ("📊 KDJ分析: K(45.2) > D(42.1) - 金叉确认", "INFO"),
        ("📊 RSI分析: RSI(52.3) - 中性区间", "INFO"),
        ("🚨 检测到多重金叉信号", "WARNING"),
        ("🎯 生成买入信号: MACD+KDJ双金叉", "WARNING"),
        ("💡 建议操作: 考虑建立多头仓位", "INFO"),
        ("📈 信号强度: 85%", "INFO"),
        ("⏰ 信号有效期: 5分钟", "INFO")
    ]
    
    for message, level in signal_logs:
        timestamp = datetime.now().strftime('%H:%M:%S')
        formatted_msg = f"[{timestamp}] {level} - {message}"
        app.add_log_message(formatted_msg, level)
        time.sleep(1.0)

def start_continuous_test(app):
    """启动持续日志测试"""
    def continuous_worker():
        counter = 0
        while True:
            try:
                counter += 1
                timestamp = datetime.now().strftime('%H:%M:%S')
                
                # 每10秒模拟一次K线更新
                if counter % 10 == 0:
                    import random
                    price = random.uniform(95000, 105000)
                    message = f"📡 K线数据更新 - 当前价格: ${price:,.2f}"
                    formatted_msg = f"[{timestamp}] INFO - {message}"
                    app.add_log_message(formatted_msg, "INFO")
                
                # 每15秒模拟一次指标更新
                if counter % 15 == 0:
                    import random
                    macd = random.uniform(-2, 2)
                    rsi = random.uniform(30, 70)
                    message = f"📈 指标更新 - MACD: {macd:.6f}, RSI: {rsi:.2f}"
                    formatted_msg = f"[{timestamp}] INFO - {message}"
                    app.add_log_message(formatted_msg, "INFO")
                
                # 每30秒模拟一次信号检测
                if counter % 30 == 0:
                    import random
                    signals = [
                        "🚨 MACD金叉信号检测",
                        "⚠️ RSI超买警告",
                        "📈 KDJ金叉确认",
                        "🎯 多重信号汇聚",
                        "📊 市场趋势分析完成"
                    ]
                    signal = random.choice(signals)
                    level = "WARNING" if "警告" in signal or "🚨" in signal else "INFO"
                    formatted_msg = f"[{timestamp}] {level} - {signal}"
                    app.add_log_message(formatted_msg, level)
                
                time.sleep(1)
                
            except Exception as e:
                print(f"持续测试异常: {e}")
                time.sleep(5)
    
    # 启动后台线程
    test_thread = threading.Thread(target=continuous_worker, daemon=True)
    test_thread.start()
    
    print("✅ 持续日志测试已启动")
    print("💡 现在您应该在WMZC日志控制台中看到持续的交易日志")
    print("💡 包括K线获取、技术指标计算和交易信号等")

async def test_async_functions():
    """测试异步函数的日志"""
    try:
        import WMZC
        
        # 查找技术指标计算器
        if hasattr(WMZC, 'TechnicalIndicatorCalculator'):
            print("🧪 测试异步技术指标计算...")
            
            # 创建计算器实例（需要API测试器）
            if hasattr(WMZC, 'GateIOAPITester'):
                api_tester = WMZC.GateIOAPITester()
                calculator = WMZC.TechnicalIndicatorCalculator(api_tester)
                
                # 运行异步测试
                result = await calculator.run_comprehensive_indicator_test("BTC_USDT")
                print(f"✅ 异步测试完成: {result.get('success_rate', 'N/A')}")
            else:
                print("⚠️ 未找到GateIOAPITester类")
        else:
            print("⚠️ 未找到TechnicalIndicatorCalculator类")
            
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")

def main():
    """主函数"""
    print("🧪 WMZC交易日志测试工具")
    print("=" * 60)
    
    try:
        # 1. 基础日志测试
        success = test_trading_logs()
        
        if success:
            print("\n✅ 基础日志测试成功")
            
            # 2. 异步函数测试
            print("\n🔄 运行异步函数测试...")
            try:
                asyncio.run(test_async_functions())
            except Exception as e:
                print(f"⚠️ 异步测试跳过: {e}")
            
            print("\n🎉 交易日志测试完成！")
            print("💡 现在您应该在WMZC日志控制台中看到:")
            print("  📡 K线数据获取日志")
            print("  🔢 技术指标计算日志")
            print("  🚨 交易信号生成日志")
            print("  💰 市场数据更新日志")
            
            print("\n🔄 持续日志测试正在运行...")
            print("💡 按 Ctrl+C 停止测试")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 测试已停止")
        else:
            print("\n❌ 基础日志测试失败")
            return False
        
        return True
        
    except KeyboardInterrupt:
        print("\n🛑 测试被中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
