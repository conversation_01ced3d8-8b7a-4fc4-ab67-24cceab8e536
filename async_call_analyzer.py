#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步调用分析器
深入分析420个中等级别BUG，区分真正的问题和误报
"""

import json
import re
import ast
from typing import List, Dict, Any

class AsyncCallAnalyzer:
    """异步调用分析器"""
    
    def __init__(self):
        self.real_bugs = []
        self.false_positives = []
        self.uncertain_cases = []
    
    def analyze_async_calls(self):
        """分析异步调用BUG"""
        print("🔍 开始深入分析420个异步调用BUG...")
        
        # 读取改进版检测报告
        with open('improved_bug_detection_report.json', 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        # 读取WMZC.py文件
        with open('WMZC.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # 分析每个BUG
        for bug in report['bugs']:
            if bug['type'] == '异步调用错误':
                line_num = bug['line']
                if line_num <= len(lines):
                    line_content = lines[line_num - 1]
                    analysis_result = self._analyze_single_call(line_content, lines, line_num)
                    
                    bug_info = {
                        'line': line_num,
                        'content': line_content.strip(),
                        'analysis': analysis_result
                    }
                    
                    if analysis_result['category'] == 'real_bug':
                        self.real_bugs.append(bug_info)
                    elif analysis_result['category'] == 'false_positive':
                        self.false_positives.append(bug_info)
                    else:
                        self.uncertain_cases.append(bug_info)
        
        self._generate_analysis_report()
    
    def _analyze_single_call(self, line: str, lines: List[str], line_num: int) -> Dict[str, Any]:
        """分析单个异步调用"""
        line_clean = line.strip()
        
        # 分析模式1: 明确的异步方法调用
        if self._is_clear_async_method(line_clean):
            return {
                'category': 'real_bug',
                'reason': '明确的异步方法调用缺少await',
                'confidence': 'high'
            }
        
        # 分析模式2: 可能的异步调用但不确定
        if self._is_potential_async_call(line_clean):
            return {
                'category': 'uncertain',
                'reason': '可能是异步调用，需要进一步确认',
                'confidence': 'medium'
            }
        
        # 分析模式3: 明确的同步调用（误报）
        if self._is_clear_sync_call(line_clean):
            return {
                'category': 'false_positive',
                'reason': '明确的同步方法调用',
                'confidence': 'high'
            }
        
        # 分析模式4: 检查上下文
        context_analysis = self._analyze_context(lines, line_num)
        
        return {
            'category': context_analysis['category'],
            'reason': context_analysis['reason'],
            'confidence': context_analysis['confidence']
        }
    
    def _is_clear_async_method(self, line: str) -> bool:
        """检测明确的异步方法调用"""
        # 明确的异步方法模式
        clear_async_patterns = [
            r'\w+\.fetch_\w+\s*\(',  # fetch_开头的方法
            r'\w+\.async_\w+\s*\(',  # async_开头的方法
            r'\w+\.\w+_async\s*\(',  # _async结尾的方法
            r'asyncio\.\w+\s*\(',    # asyncio模块的方法
            r'\w+\.send_async\s*\(', # send_async方法
            r'\w+\.execute_async\s*\(', # execute_async方法
        ]
        
        for pattern in clear_async_patterns:
            if re.search(pattern, line):
                return True
        
        return False
    
    def _is_potential_async_call(self, line: str) -> bool:
        """检测可能的异步调用"""
        # 可能的异步方法模式
        potential_patterns = [
            r'\w+\.get_\w+\s*\(',    # get_开头可能是异步
            r'\w+\.post_\w+\s*\(',   # post_开头可能是异步
            r'\w+\.send_\w+\s*\(',   # send_开头可能是异步
            r'\w+\.process_\w+\s*\(', # process_开头可能是异步
        ]
        
        for pattern in potential_patterns:
            if re.search(pattern, line):
                return True
        
        return False
    
    def _is_clear_sync_call(self, line: str) -> bool:
        """检测明确的同步调用"""
        # 明确的同步方法模式
        sync_patterns = [
            r'\w+\.append\s*\(',     # list.append
            r'\w+\.update\s*\(',     # dict.update
            r'\w+\.get\s*\(\s*["\']', # dict.get with string key
            r'\w+\.format\s*\(',     # string.format
            r'\w+\.join\s*\(',       # string.join
            r'\w+\.split\s*\(',      # string.split
            r'\w+\.strip\s*\(',      # string.strip
            r'\w+\.replace\s*\(',    # string.replace
            r'len\s*\(',             # len()
            r'str\s*\(',             # str()
            r'int\s*\(',             # int()
            r'float\s*\(',           # float()
            r'print\s*\(',           # print()
            r'log\s*\(',             # log()
        ]
        
        for pattern in sync_patterns:
            if re.search(pattern, line):
                return True
        
        return False
    
    def _analyze_context(self, lines: List[str], line_num: int) -> Dict[str, Any]:
        """分析上下文来判断调用类型"""
        # 获取上下文
        context_start = max(0, line_num - 5)
        context_end = min(len(lines), line_num + 3)
        context_lines = lines[context_start:context_end]
        context = ' '.join(context_lines)
        
        # 检查是否在异步函数中
        in_async_function = False
        for i in range(line_num - 1, -1, -1):
            line = lines[i].strip()
            if line.startswith('async def '):
                in_async_function = True
                break
            elif line.startswith('def ') and not line.startswith('def async'):
                break
        
        # 如果不在异步函数中，很可能是误报
        if not in_async_function:
            return {
                'category': 'false_positive',
                'reason': '不在异步函数中',
                'confidence': 'high'
            }
        
        # 检查是否有异步相关的关键词
        async_keywords = ['await', 'async', 'asyncio', 'coroutine']
        has_async_context = any(keyword in context.lower() for keyword in async_keywords)
        
        if has_async_context:
            return {
                'category': 'uncertain',
                'reason': '在异步上下文中，需要进一步确认',
                'confidence': 'medium'
            }
        else:
            return {
                'category': 'false_positive',
                'reason': '缺乏异步上下文',
                'confidence': 'medium'
            }
    
    def _generate_analysis_report(self):
        """生成分析报告"""
        total_bugs = len(self.real_bugs) + len(self.false_positives) + len(self.uncertain_cases)
        
        print(f"\n📊 异步调用BUG深度分析报告")
        print(f"总计分析: {total_bugs}个BUG")
        print(f"🔴 真正的BUG: {len(self.real_bugs)}个")
        print(f"❌ 误报: {len(self.false_positives)}个")
        print(f"❓ 需要进一步确认: {len(self.uncertain_cases)}个")
        
        # 显示真正的BUG
        if self.real_bugs:
            print(f"\n🔴 真正需要修复的BUG ({len(self.real_bugs)}个):")
            for i, bug in enumerate(self.real_bugs[:10], 1):  # 只显示前10个
                print(f"{i}. 行{bug['line']}: {bug['content']}")
                print(f"   原因: {bug['analysis']['reason']}")
        
        # 显示不确定的案例
        if self.uncertain_cases:
            print(f"\n❓ 需要进一步确认的案例 ({len(self.uncertain_cases)}个，显示前5个):")
            for i, bug in enumerate(self.uncertain_cases[:5], 1):
                print(f"{i}. 行{bug['line']}: {bug['content']}")
                print(f"   原因: {bug['analysis']['reason']}")
        
        # 保存详细分析结果
        analysis_result = {
            'total_analyzed': total_bugs,
            'real_bugs_count': len(self.real_bugs),
            'false_positives_count': len(self.false_positives),
            'uncertain_cases_count': len(self.uncertain_cases),
            'real_bugs': self.real_bugs,
            'false_positives': self.false_positives,
            'uncertain_cases': self.uncertain_cases
        }
        
        with open('async_call_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细分析报告已保存到: async_call_analysis_report.json")
        
        # 计算精确度
        if total_bugs > 0:
            false_positive_rate = len(self.false_positives) / total_bugs * 100
            real_bug_rate = len(self.real_bugs) / total_bugs * 100
            print(f"\n📈 分析统计:")
            print(f"误报率: {false_positive_rate:.1f}%")
            print(f"真实BUG率: {real_bug_rate:.1f}%")
            print(f"需确认率: {(100 - false_positive_rate - real_bug_rate):.1f}%")

if __name__ == "__main__":
    analyzer = AsyncCallAnalyzer()
    analyzer.analyze_async_calls()
