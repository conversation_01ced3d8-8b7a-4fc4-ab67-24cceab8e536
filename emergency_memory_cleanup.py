#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚨 紧急内存清理工具
系统内存使用率97.7%，立即执行紧急清理
"""

import gc
import os
import sys
import psutil
import time

def emergency_memory_cleanup():
    """紧急内存清理"""
    print("🚨 紧急内存清理启动")
    print("=" * 50)
    
    process = psutil.Process()
    system_memory = psutil.virtual_memory()
    
    print(f"📊 系统内存使用率: {system_memory.percent:.1f}%")
    print(f"📊 进程内存使用: {process.memory_info().rss / 1024 / 1024:.1f} MB")
    
    if system_memory.percent > 95:
        print("🚨 系统内存严重不足，执行紧急清理...")
        
        # 1. 强制垃圾回收
        print("🗑️ 执行强制垃圾回收...")
        for i in range(5):
            collected = gc.collect()
            print(f"  回合 {i+1}: 回收 {collected} 个对象")
            if collected == 0:
                break
        
        # 2. 清理Python缓存
        print("🧹 清理Python缓存...")
        try:
            import sys
            # 清理模块缓存
            modules_to_remove = []
            for module_name in sys.modules:
                if module_name.startswith('__pycache__'):
                    modules_to_remove.append(module_name)
            
            for module_name in modules_to_remove:
                del sys.modules[module_name]
            
            print(f"  清理了 {len(modules_to_remove)} 个缓存模块")
        except Exception as e:
            print(f"  缓存清理失败: {e}")
        
        # 3. 清理大型对象
        print("📊 清理大型对象...")
        cleared_count = 0
        for obj in gc.get_objects():
            try:
                # 清理大型列表
                if isinstance(obj, list) and len(obj) > 1000:
                    obj.clear()
                    cleared_count += 1
                # 清理大型字典
                elif isinstance(obj, dict) and len(obj) > 1000:
                    obj.clear()
                    cleared_count += 1
                # 清理DataFrame
                elif hasattr(obj, 'memory_usage') and hasattr(obj, 'drop'):
                    try:
                        memory_usage = obj.memory_usage(deep=True).sum()
                        if memory_usage > 10 * 1024 * 1024:  # 大于10MB
                            obj.drop(obj.index, inplace=True)
                            cleared_count += 1
                    except:
                        pass
            except:
                continue
        
        print(f"  清理了 {cleared_count} 个大型对象")
        
        # 4. 最终垃圾回收
        final_collected = gc.collect()
        print(f"🗑️ 最终垃圾回收: {final_collected} 个对象")
        
        # 5. 检查清理效果
        after_memory = psutil.virtual_memory()
        after_process = psutil.Process().memory_info().rss
        
        print(f"\n📊 清理后状态:")
        print(f"  系统内存使用率: {after_memory.percent:.1f}%")
        print(f"  进程内存使用: {after_process / 1024 / 1024:.1f} MB")
        
        if after_memory.percent < 90:
            print("✅ 紧急清理成功，系统内存已释放")
            return True
        else:
            print("⚠️ 清理效果有限，建议重启系统")
            return False
    
    else:
        print("✅ 系统内存使用正常，无需紧急清理")
        return True

def force_wmzc_cleanup():
    """强制WMZC清理"""
    print("\n🔧 强制WMZC清理...")
    
    try:
        # 尝试导入WMZC并清理
        import WMZC
        
        # 清理全局列表
        global_lists = ['signal_log', 'trading_records']
        for list_name in global_lists:
            if list_name in globals():
                obj = globals()[list_name]
                if isinstance(obj, list):
                    size_before = len(obj)
                    obj.clear()
                    print(f"  ✅ 清理 {list_name}: {size_before} -> 0")
        
        # 清理WMZC对象中的列表
        wmzc_lists = ['audit_trail', 'alert_history', 'connection_errors', 'memory_snapshots']
        for list_name in wmzc_lists:
            if hasattr(WMZC, list_name):
                obj = getattr(WMZC, list_name)
                if hasattr(obj, 'clear'):
                    obj.clear()
                    print(f"  ✅ 清理 WMZC.{list_name}")
                elif isinstance(obj, list):
                    size_before = len(obj)
                    obj.clear()
                    print(f"  ✅ 清理 WMZC.{list_name}: {size_before} -> 0")
        
        # 清理缓存
        cache_objects = ['smart_cache_manager', 'indicator_cache', 'kline_cache', 
                        'kdj_cache', 'macd_cache', 'rsi_cache']
        for cache_name in cache_objects:
            if hasattr(WMZC, cache_name):
                cache_obj = getattr(WMZC, cache_name)
                if hasattr(cache_obj, 'clear'):
                    cache_obj.clear()
                    print(f"  ✅ 清理缓存 {cache_name}")
        
        print("✅ WMZC强制清理完成")
        return True
        
    except ImportError:
        print("⚠️ 无法导入WMZC模块")
        return False
    except Exception as e:
        print(f"❌ WMZC清理失败: {e}")
        return False

def main():
    """主函数"""
    print("🚨 紧急内存清理工具")
    print("=" * 50)
    
    try:
        # 1. 系统级紧急清理
        system_ok = emergency_memory_cleanup()
        
        # 2. WMZC强制清理
        wmzc_ok = force_wmzc_cleanup()
        
        # 3. 最终状态检查
        final_memory = psutil.virtual_memory()
        final_process = psutil.Process().memory_info().rss
        
        print(f"\n📋 最终状态:")
        print(f"  系统内存使用率: {final_memory.percent:.1f}%")
        print(f"  进程内存使用: {final_process / 1024 / 1024:.1f} MB")
        
        if final_memory.percent < 85:
            print("🎉 紧急清理成功！系统内存已恢复正常")
            print("💡 建议启动内存守护者持续监控")
            return True
        elif final_memory.percent < 95:
            print("⚠️ 部分清理成功，但仍需关注内存使用")
            print("💡 建议重启WMZC系统以完全释放内存")
            return True
        else:
            print("🚨 清理效果有限，强烈建议重启系统")
            print("💡 系统可能存在严重的内存泄漏")
            return False
    
    except Exception as e:
        print(f"❌ 紧急清理异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
