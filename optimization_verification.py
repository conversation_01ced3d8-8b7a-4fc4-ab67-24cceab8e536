#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC优化效果验证脚本
验证代码质量优化、性能优化和监控完善的效果
"""

import time
import asyncio
import psutil
import gc
from typing import Dict, Any
import json

def verify_optimization_modules():
    """验证优化模块是否正常工作"""
    print("🔍 验证优化模块...")
    
    verification_results = {
        'trading_loop_modules': False,
        'performance_optimizer': False,
        'monitoring_system': False,
        'integration_status': False
    }
    
    # 1. 验证交易循环模块
    try:
        from trading_loop_modules import TradingLoopManager, trading_loop_manager
        print("✅ 交易循环模块加载成功")
        verification_results['trading_loop_modules'] = True
        
        # 测试基本功能
        manager = TradingLoopManager()
        print(f"  - 交易循环管理器创建成功: {type(manager).__name__}")
        
    except Exception as e:
        print(f"❌ 交易循环模块验证失败: {e}")
    
    # 2. 验证性能优化器
    try:
        from performance_optimizer import PerformanceOptimizer, performance_optimizer
        print("✅ 性能优化器模块加载成功")
        verification_results['performance_optimizer'] = True
        
        # 测试缓存功能
        cache_manager = performance_optimizer.cache_manager
        cache_manager.set("test_cache", "test_key", "test_value", ttl=60)
        cached_value = cache_manager.get("test_cache", "test_key")
        
        if cached_value == "test_value":
            print("  - 缓存功能测试通过")
        else:
            print("  - 缓存功能测试失败")
        
        # 测试内存监控
        memory_stats = performance_optimizer.memory_monitor.get_memory_stats()
        print(f"  - 内存监控: {memory_stats.percent:.1f}% 使用率")
        
    except Exception as e:
        print(f"❌ 性能优化器验证失败: {e}")
    
    # 3. 验证监控系统
    try:
        from monitoring_system import MonitoringSystem, monitoring_system
        print("✅ 监控系统模块加载成功")
        verification_results['monitoring_system'] = True
        
        # 测试日志功能
        monitoring_system.log("测试日志消息", "INFO")
        
        # 测试指标记录
        monitoring_system.record_metric("test_metric", 100.0, "count")
        
        # 获取统计信息
        log_stats = monitoring_system.logger.get_stats()
        print(f"  - 日志统计: {log_stats}")
        
    except Exception as e:
        print(f"❌ 监控系统验证失败: {e}")
    
    # 4. 验证WMZC集成
    try:
        import WMZC
        if hasattr(WMZC, 'OPTIMIZATION_MODULES_AVAILABLE'):
            if WMZC.OPTIMIZATION_MODULES_AVAILABLE:
                print("✅ WMZC优化模块集成成功")
                verification_results['integration_status'] = True
            else:
                print("⚠️ WMZC优化模块集成失败")
        else:
            print("⚠️ WMZC未包含优化模块标志")
    except Exception as e:
        print(f"❌ WMZC集成验证失败: {e}")
    
    return verification_results

def performance_benchmark():
    """性能基准测试"""
    print("\n📊 性能基准测试...")
    
    # 内存使用测试
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"初始内存使用: {initial_memory:.1f} MB")
    
    # 缓存性能测试
    try:
        from performance_optimizer import performance_optimizer
        
        # 缓存写入测试
        start_time = time.time()
        for i in range(1000):
            performance_optimizer.cache_manager.set("benchmark", f"key_{i}", f"value_{i}")
        write_time = time.time() - start_time
        
        # 缓存读取测试
        start_time = time.time()
        hit_count = 0
        for i in range(1000):
            value = performance_optimizer.cache_manager.get("benchmark", f"key_{i}")
            if value:
                hit_count += 1
        read_time = time.time() - start_time
        
        print(f"缓存写入性能: {1000/write_time:.0f} ops/sec")
        print(f"缓存读取性能: {1000/read_time:.0f} ops/sec")
        print(f"缓存命中率: {hit_count/1000*100:.1f}%")
        
        # 获取缓存统计
        cache_stats = performance_optimizer.cache_manager.get_stats()
        print(f"缓存内存使用: {cache_stats.memory_usage_mb:.2f} MB")
        
    except Exception as e:
        print(f"❌ 缓存性能测试失败: {e}")
    
    # 内存清理测试
    gc.collect()
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"最终内存使用: {final_memory:.1f} MB")
    print(f"内存变化: {final_memory - initial_memory:+.1f} MB")

async def async_performance_test():
    """异步性能测试"""
    print("\n⚡ 异步性能测试...")
    
    try:
        from performance_optimizer import profile_performance
        
        @profile_performance("test_async_function")
        async def test_async_function():
            await asyncio.sleep(0.1)
            return "test_result"
        
        # 执行多次异步函数
        start_time = time.time()
        tasks = [test_async_function() for _ in range(10)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        print(f"异步函数执行: {len(results)} 次")
        print(f"总耗时: {total_time:.3f} 秒")
        print(f"平均耗时: {total_time/len(results)*1000:.1f} ms/次")
        
        # 获取性能统计
        from performance_optimizer import performance_optimizer
        stats = performance_optimizer.profiler.get_profile_stats("test_async_function")
        if stats:
            print(f"性能分析: 平均 {stats['avg']*1000:.1f} ms, 最大 {stats['max']*1000:.1f} ms")
        
    except Exception as e:
        print(f"❌ 异步性能测试失败: {e}")

def monitoring_system_test():
    """监控系统测试"""
    print("\n📊 监控系统测试...")
    
    try:
        from monitoring_system import monitoring_system
        
        # 启动监控系统
        monitoring_system.start()
        
        # 记录各种日志
        monitoring_system.log("测试信息日志", "INFO")
        monitoring_system.log("测试警告日志", "WARNING")
        monitoring_system.log("测试错误日志", "ERROR")
        
        # 记录性能指标
        for i in range(10):
            monitoring_system.record_metric("test_latency", i * 10, "ms")
            monitoring_system.record_metric("test_throughput", 100 - i, "ops/sec")
        
        # 等待一段时间让监控系统处理
        time.sleep(2)
        
        # 获取仪表板数据
        dashboard_data = monitoring_system.get_dashboard_data()
        
        print("监控系统状态:")
        print(f"  - 健康状态: {dashboard_data['health']['overall']}")
        print(f"  - 日志统计: {dashboard_data['log_stats']}")
        print(f"  - 最近日志数量: {len(dashboard_data['recent_logs'])}")
        
        # 停止监控系统
        monitoring_system.stop()
        
    except Exception as e:
        print(f"❌ 监控系统测试失败: {e}")

def generate_optimization_report():
    """生成优化报告"""
    print("\n📋 生成优化报告...")
    
    report = {
        'timestamp': time.time(),
        'verification_results': {},
        'performance_metrics': {},
        'recommendations': []
    }
    
    # 运行验证
    verification_results = verify_optimization_modules()
    report['verification_results'] = verification_results
    
    # 计算优化成功率
    success_count = sum(verification_results.values())
    total_count = len(verification_results)
    success_rate = (success_count / total_count) * 100
    
    report['optimization_success_rate'] = success_rate
    
    # 生成建议
    if success_rate >= 90:
        report['status'] = "优秀"
        report['recommendations'].append("✅ 优化系统运行良好，建议继续监控")
    elif success_rate >= 70:
        report['status'] = "良好"
        report['recommendations'].append("👍 优化系统基本正常，建议修复失败的模块")
    else:
        report['status'] = "需要改进"
        report['recommendations'].append("⚠️ 优化系统存在问题，需要重新检查模块")
    
    # 保存报告
    try:
        with open('optimization_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print("📄 优化报告已保存到 optimization_report.json")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
    
    return report

async def main():
    """主函数"""
    print("🎯 WMZC优化效果验证")
    print("=" * 50)
    
    # 1. 模块验证
    verification_results = verify_optimization_modules()
    
    # 2. 性能基准测试
    performance_benchmark()
    
    # 3. 异步性能测试
    await async_performance_test()
    
    # 4. 监控系统测试
    monitoring_system_test()
    
    # 5. 生成报告
    report = generate_optimization_report()
    
    print("\n🎉 验证完成!")
    print(f"优化成功率: {report['optimization_success_rate']:.1f}%")
    print(f"系统状态: {report['status']}")
    
    for recommendation in report['recommendations']:
        print(f"建议: {recommendation}")

if __name__ == "__main__":
    asyncio.run(main())
