#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC智能修复脚本
修复之前修复脚本造成的问题，确保代码语法正确
"""

import re
import ast

def fix_commented_function_definitions():
    """修复被错误注释的函数定义"""
    print("🔧 修复被错误注释的函数定义...")
    
    with open("WMZC.py", "r", encoding="utf-8") as f:
        lines = f.readlines()
    
    fixes = 0
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # 查找被注释的函数定义
        if "# 🔧 修复：注释重复函数定义 - def " in line:
            # 提取函数定义
            func_def = line.split(" - ")[1].strip()
            
            # 检查下一行是否是函数体
            if i + 1 < len(lines) and lines[i + 1].strip().startswith('"""'):
                # 恢复函数定义
                lines[i] = func_def + "\n"
                fixes += 1
            else:
                # 完全删除这行
                lines[i] = ""
                fixes += 1
        
        i += 1
    
    # 保存修复后的文件
    with open("WMZC.py", "w", encoding="utf-8") as f:
        f.writelines(lines)
    
    print(f"✅ 修复了 {fixes} 个被错误注释的函数定义")

def fix_indentation_issues():
    """修复缩进问题"""
    print("🔧 修复缩进问题...")
    
    with open("WMZC.py", "r", encoding="utf-8") as f:
        lines = f.readlines()
    
    fixes = 0
    for i, line in enumerate(lines):
        # 修复孤立的文档字符串
        if line.strip().startswith('"""') and i > 0:
            prev_line = lines[i-1].strip()
            if not prev_line.endswith(':') and not prev_line.startswith('def ') and not prev_line.startswith('class '):
                # 这可能是孤立的文档字符串，注释掉
                lines[i] = "# " + line
                fixes += 1
    
    # 保存修复后的文件
    with open("WMZC.py", "w", encoding="utf-8") as f:
        f.writelines(lines)
    
    print(f"✅ 修复了 {fixes} 个缩进问题")

def validate_syntax():
    """验证Python语法"""
    print("🔍 验证Python语法...")
    
    try:
        with open("WMZC.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        print("✅ Python语法验证通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: 第{e.lineno}行 - {e.msg}")
        print(f"   问题代码: {e.text}")
        return False
    except Exception as e:
        print(f"❌ 验证异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始智能修复...")
    
    # 1. 修复被错误注释的函数定义
    fix_commented_function_definitions()
    
    # 2. 修复缩进问题
    fix_indentation_issues()
    
    # 3. 验证语法
    if validate_syntax():
        print("🎉 智能修复完成，语法正确！")
        return True
    else:
        print("❌ 修复后仍有语法问题")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
