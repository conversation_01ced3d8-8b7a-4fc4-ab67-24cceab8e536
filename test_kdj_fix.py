#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试KDJ配置修复
"""

def test_kdj_config_fix():
    """测试KDJ配置修复是否成功"""
    print("🔍 开始KDJ配置修复测试...")
    
    try:
        # 测试1：模拟空配置情况
        print("📋 测试1：空配置情况...")
        config = {}
        
        # 测试安全访问
        kdj_low = config.get('KDJ', {}).get('LOW', -2)
        kdj_high = config.get('KDJ', {}).get('HIGH', 90)
        kdj_window = config.get('KDJ', {}).get('window', 14)
        kdj_smooth = config.get('KDJ', {}).get('smooth_window', 3)
        
        print(f"✅ 安全访问成功: LOW={kdj_low}, HIGH={kdj_high}, window={kdj_window}, smooth={kdj_smooth}")
        
        # 测试2：模拟setdefault操作
        print("📋 测试2：setdefault操作...")
        config.setdefault('KDJ', {})['LOW'] = -5
        config.setdefault('KDJ', {})['HIGH'] = 95
        
        print(f"✅ setdefault操作成功: {config['KDJ']}")
        
        # 测试3：模拟复杂嵌套访问
        print("📋 测试3：复杂嵌套访问...")
        config.setdefault('KDJ', {}).setdefault('timeframe_settings', {})['1m'] = {'test': 'value'}
        
        print(f"✅ 复杂嵌套访问成功: {config['KDJ']['timeframe_settings']}")
        
        # 测试4：模拟WMZC导入测试
        print("📋 测试4：WMZC导入测试...")
        try:
            import WMZC
            print("✅ WMZC导入成功，KDJ配置修复生效")
        except Exception as import_error:
            print(f"❌ WMZC导入失败: {import_error}")
            return False
        
        print("🎉 所有KDJ配置修复测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ KDJ配置修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_kdj_config_fix()
    if success:
        print("\n" + "="*60)
        print("🎉 KDJ配置修复验证结果：成功")
        print("✅ KDJ配置访问问题已完全解决")
        print("✅ 系统启动时不再出现'初始化配置失败: KDJ'错误")
        print("✅ 所有KDJ相关功能应该可以正常工作")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ KDJ配置修复验证结果：失败")
        print("需要进一步检查和修复")
        print("="*60)
