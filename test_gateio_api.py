#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gate.io API 端到端测试脚本
使用真实API凭证进行完整的交易流程测试
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.append('.')

# 导入WMZC模块
try:
    import WMZC
    print("✅ WMZC模块导入成功")
except ImportError as e:
    print(f"❌ WMZC模块导入失败: {e}")
    sys.exit(1)

# Gate.io API 凭证
API_KEY = "d5ea5faa068d66204bb68b75201c56d5"
SECRET_KEY = "5b516e55788fba27e61f9bd06b22ab3661b3115797076d5e73199bea3a8afb1c"

async def main():
    """主测试函数"""
    print("=" * 80)
    print("🚀 Gate.io API 端到端测试开始")
    print("=" * 80)
    
    # 创建API测试器
    tester = WMZC.GateIOAPITester(API_KEY, SECRET_KEY)
    
    try:
        # 运行完整测试
        results = await tester.run_comprehensive_test()
        
        # 显示详细结果
        print("\n" + "=" * 80)
        print("📊 测试结果详情")
        print("=" * 80)
        
        # API连接测试
        api_conn = results.get("api_connection", {})
        print(f"🔗 API连接: {'✅ 成功' if api_conn.get('connection') else '❌ 失败'}")
        print(f"🔐 API认证: {'✅ 成功' if api_conn.get('auth') else '❌ 失败'}")
        
        if api_conn.get('account_info'):
            print(f"💰 账户信息: 获取到 {len(api_conn['account_info'])} 个币种余额")
        
        # 交易对测试
        trading_pair = results.get("trading_pair", {})
        print(f"📈 BTC-USDT交易对: {'✅ 可用' if trading_pair.get('available') else '❌ 不可用'}")
        
        if trading_pair.get('symbol_info'):
            symbol_info = trading_pair['symbol_info']
            print(f"   - 交易状态: {symbol_info.get('trade_status', 'unknown')}")
            print(f"   - 最小交易量: {symbol_info.get('min_base_amount', 'unknown')}")
            print(f"   - 价格精度: {symbol_info.get('precision', 'unknown')}")
        
        # K线数据测试
        kline_data = results.get("kline_data", {})
        print(f"📊 K线数据: {'✅ 成功' if kline_data.get('success') else '❌ 失败'}")
        
        if kline_data.get('latest_kline'):
            latest = kline_data['latest_kline']
            print(f"   - 最新K线: 时间={latest[0]}, 开盘={latest[5]}, 收盘={latest[2]}")
            print(f"   - 数据条数: {kline_data.get('data_count', 0)}")
        
        # 订单测试
        order_test = results.get("order_test", {})
        print(f"📋 订单测试: {'✅ 通过' if order_test.get('params_valid') else '❌ 失败'}")
        print(f"   - 测试模式: {'是' if order_test.get('test_mode') else '否'}")
        
        # 总体结果
        print("\n" + "=" * 80)
        overall_success = results.get("overall_success", False)
        success_rate = results.get("success_rate", "0/5")
        
        if overall_success:
            print("🎉 Gate.io API测试完全成功！")
            print("✅ 系统已准备好进行实盘交易")
        else:
            print(f"⚠️ Gate.io API测试部分成功: {success_rate}")
            print("🔧 需要修复失败的组件")
        
        print("=" * 80)
        
        return results
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 运行异步测试
    try:
        results = asyncio.run(main())
        
        if results and results.get("overall_success"):
            print("\n🚀 准备进行下一步：技术指标计算测试")
            sys.exit(0)
        else:
            print("\n❌ API测试失败，请检查网络连接和API凭证")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试脚本异常: {e}")
        sys.exit(1)
