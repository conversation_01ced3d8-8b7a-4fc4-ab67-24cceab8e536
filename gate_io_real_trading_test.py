#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 Gate.io真实交易测试（启用版）
⚠️ 警告：此脚本将执行真实交易，使用真实资金！
"""

import json
import time
import hmac
import hashlib
import urllib.request
import urllib.parse
import urllib.error
from datetime import datetime

def log_test(message, level="INFO"):
    """测试日志函数"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [{level}] {message}")

class GateIORealTradingTest:
    """Gate.io真实交易测试器"""
    
    def __init__(self):
        self.api_key = "d5ea5faa068d66204bb68b75201c56d5"
        self.secret_key = "5b516e55788fba27e61f9bd06b22ab3661b3115797076d5e73199bea3a8afb1c"
        self.base_url = "https://api.gateio.ws/api/v4"
        self.test_symbol = "BTC_USDT"
        self.test_results = []
        
        # 🚨 真实交易设置
        self.enable_real_trading = True  # 启用真实交易
        self.max_test_amount_usdt = 10.0  # 最大测试金额（USDT）
        self.min_balance_required = 15.0  # 最小余额要求
        
    def generate_signature(self, method, url, query_string, payload):
        """生成Gate.io API签名"""
        try:
            t = str(int(time.time()))
            m = hashlib.sha512()
            m.update((payload or "").encode('utf-8'))
            hashed_payload = m.hexdigest()
            s = '%s\n%s\n%s\n%s\n%s' % (method, url, query_string or "", hashed_payload, t)
            sign = hmac.new(self.secret_key.encode('utf-8'), s.encode('utf-8'), hashlib.sha512).hexdigest()
            return {'KEY': self.api_key, 'Timestamp': t, 'SIGN': sign}
        except Exception as e:
            log_test(f"❌ 签名生成失败: {e}", "ERROR")
            return None
    
    def make_authenticated_request(self, method, endpoint, params=None, data=None):
        """发送需要认证的API请求"""
        try:
            url = f"{self.base_url}{endpoint}"
            query_string = urllib.parse.urlencode(params) if params else ""
            payload = json.dumps(data) if data else ""
            
            # 生成签名
            auth_headers = self.generate_signature(method.upper(), endpoint, query_string, payload)
            if not auth_headers:
                return None
            
            headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'User-Agent': 'WMZC-Gate.io-RealTest/1.0'
            }
            headers.update(auth_headers)
            
            if method.upper() == 'GET':
                if params:
                    url = f"{url}?{query_string}"
                req = urllib.request.Request(url, headers=headers)
            elif method.upper() == 'POST':
                if params:
                    url = f"{url}?{query_string}"
                req = urllib.request.Request(url, data=payload.encode('utf-8'), headers=headers)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            with urllib.request.urlopen(req, timeout=15) as response:
                data = response.read().decode('utf-8')
                return json.loads(data)
                
        except urllib.error.HTTPError as e:
            log_test(f"❌ 认证请求HTTP错误: {e.code} - {e.reason}", "ERROR")
            if e.code == 401:
                log_test("💡 API认证失败，请检查API密钥和权限", "WARNING")
            return None
        except Exception as e:
            log_test(f"❌ 认证请求失败: {e}", "ERROR")
            return None
    
    def execute_real_trading_test(self):
        """执行真实交易测试"""
        log_test("=" * 80, "INFO")
        log_test("🚨 Gate.io真实交易测试开始", "WARNING")
        log_test("⚠️ 警告：此测试将使用真实资金进行交易！", "WARNING")
        log_test("=" * 80, "INFO")
        
        # 安全确认
        log_test("🛡️ 安全检查...", "INFO")
        log_test(f"   最大测试金额: {self.max_test_amount_usdt} USDT", "INFO")
        log_test(f"   最小余额要求: {self.min_balance_required} USDT", "INFO")
        log_test("", "INFO")
        
        try:
            # 第1步：账户验证
            if not self.verify_account():
                log_test("❌ 账户验证失败，终止测试", "ERROR")
                return
            
            # 第2步：获取市场数据
            if not self.get_market_data():
                log_test("❌ 市场数据获取失败，终止测试", "ERROR")
                return
            
            # 第3步：执行买入测试
            buy_result = self.execute_buy_test()
            if not buy_result:
                log_test("❌ 买入测试失败，终止测试", "ERROR")
                return
            
            # 第4步：立即执行平仓测试
            self.execute_close_test(buy_result)
            
            # 生成最终报告
            self.generate_final_report()
            
        except Exception as e:
            log_test(f"❌ 真实交易测试异常: {e}", "ERROR")
            import traceback
            traceback.print_exc()
    
    def verify_account(self):
        """验证账户"""
        log_test("📋 第1步：账户验证", "INFO")
        log_test("-" * 50, "INFO")
        
        try:
            # 获取账户余额
            balance_data = self.make_authenticated_request('GET', '/spot/accounts')
            
            if not balance_data:
                log_test("❌ 无法获取账户信息", "ERROR")
                self.test_results.append("❌ 账户信息获取失败")
                return False
            
            usdt_balance = 0
            btc_balance = 0
            for account in balance_data:
                if account.get('currency') == 'USDT':
                    usdt_balance = float(account.get('available', 0))
                elif account.get('currency') == 'BTC':
                    btc_balance = float(account.get('available', 0))
            
            log_test(f"💰 当前余额: USDT={usdt_balance:.2f}, BTC={btc_balance:.6f}", "INFO")
            self.usdt_balance = usdt_balance
            self.btc_balance = btc_balance
            
            # 检查余额是否足够
            if usdt_balance < self.min_balance_required:
                log_test(f"❌ USDT余额不足，需要至少{self.min_balance_required} USDT", "ERROR")
                self.test_results.append(f"❌ USDT余额不足: {usdt_balance}")
                return False
            
            log_test("✅ 账户验证通过", "INFO")
            self.test_results.append("✅ 账户验证通过")
            return True
            
        except Exception as e:
            log_test(f"❌ 账户验证失败: {e}", "ERROR")
            self.test_results.append(f"❌ 账户验证失败: {e}")
            return False
    
    def get_market_data(self):
        """获取市场数据"""
        log_test("📋 第2步：获取市场数据", "INFO")
        log_test("-" * 50, "INFO")
        
        try:
            # 获取价格数据
            data = self.make_request('GET', '/spot/tickers', {'currency_pair': self.test_symbol})
            
            if data and len(data) > 0:
                ticker = data[0]
                self.current_price = float(ticker.get('last', 0))
                volume = float(ticker.get('base_volume', 0))
                change_percentage = float(ticker.get('change_percentage', 0))
                
                log_test(f"💰 当前价格: ${self.current_price:.2f}", "INFO")
                log_test(f"📊 24h涨跌: {change_percentage:.2f}%", "INFO")
                log_test(f"📈 24h成交量: {volume:.2f} BTC", "INFO")
                
                self.test_results.append(f"✅ 价格数据: ${self.current_price:.2f}")
                return True
            else:
                log_test("❌ 价格数据获取失败", "ERROR")
                self.test_results.append("❌ 价格数据获取失败")
                return False
                
        except Exception as e:
            log_test(f"❌ 市场数据获取失败: {e}", "ERROR")
            self.test_results.append(f"❌ 市场数据获取失败: {e}")
            return False
    
    def make_request(self, method, endpoint, params=None):
        """发送公开API请求"""
        try:
            url = f"{self.base_url}{endpoint}"
            
            if params:
                query_string = urllib.parse.urlencode(params)
                url = f"{url}?{query_string}"
            
            headers = {
                'Accept': 'application/json',
                'User-Agent': 'WMZC-Gate.io-RealTest/1.0'
            }
            
            req = urllib.request.Request(url, headers=headers)
            
            with urllib.request.urlopen(req, timeout=10) as response:
                data = response.read().decode('utf-8')
                return json.loads(data)
                
        except Exception as e:
            log_test(f"❌ API请求失败: {e}", "ERROR")
            return None
    
    def execute_buy_test(self):
        """执行买入测试"""
        log_test("📋 第3步：执行买入测试", "INFO")
        log_test("-" * 50, "INFO")
        
        try:
            # 计算测试金额
            test_usdt_amount = min(self.max_test_amount_usdt, self.usdt_balance * 0.1)
            test_btc_amount = test_usdt_amount / self.current_price
            
            # 获取最小交易量
            pair_data = self.make_request('GET', '/spot/currency_pairs', {'currency_pair': self.test_symbol})
            if pair_data and len(pair_data) > 0:
                min_amount = float(pair_data[0].get('min_base_amount', 0.001))
                if test_btc_amount < min_amount:
                    test_btc_amount = min_amount
                    test_usdt_amount = test_btc_amount * self.current_price
            
            buy_params = {
                'currency_pair': self.test_symbol,
                'side': 'buy',
                'amount': f"{test_btc_amount:.6f}",
                'type': 'market'
            }
            
            log_test(f"🛒 买入参数: {buy_params}", "INFO")
            log_test(f"💰 预计花费: {test_usdt_amount:.2f} USDT", "INFO")
            
            # 执行买入
            result = self.make_authenticated_request('POST', '/spot/orders', data=buy_params)
            
            if result:
                order_id = result.get('id')
                status = result.get('status')
                log_test(f"✅ 买入订单提交成功: 订单ID {order_id}", "INFO")
                
                # 等待订单执行
                time.sleep(3)
                
                # 检查订单状态
                order_status = self.check_order_status(order_id)
                if order_status:
                    actual_status = order_status.get('status')
                    filled_amount = float(order_status.get('filled_amount', 0))
                    
                    log_test(f"📊 订单状态: {actual_status}, 成交数量: {filled_amount:.6f} BTC", "INFO")
                    
                    if actual_status == 'closed' and filled_amount > 0:
                        log_test("✅ 买入订单完全成交", "INFO")
                        self.test_results.append("✅ 买入测试成功")
                        
                        return {
                            'success': True,
                            'order_id': order_id,
                            'side': 'buy',
                            'amount': f"{filled_amount:.6f}",
                            'symbol': self.test_symbol
                        }
                    else:
                        log_test(f"⚠️ 买入订单状态异常: {actual_status}", "WARNING")
                        self.test_results.append(f"⚠️ 买入状态异常: {actual_status}")
                        return None
                else:
                    log_test("❌ 无法获取订单状态", "ERROR")
                    self.test_results.append("❌ 订单状态检查失败")
                    return None
            else:
                log_test("❌ 买入订单提交失败", "ERROR")
                self.test_results.append("❌ 买入订单提交失败")
                return None
                
        except Exception as e:
            log_test(f"❌ 买入测试异常: {e}", "ERROR")
            self.test_results.append(f"❌ 买入测试异常: {e}")
            return None
    
    def check_order_status(self, order_id):
        """检查订单状态"""
        try:
            result = self.make_authenticated_request('GET', f'/spot/orders/{order_id}')
            return result
        except Exception as e:
            log_test(f"❌ 检查订单状态失败: {e}", "ERROR")
            return None
    
    def execute_close_test(self, buy_result):
        """执行平仓测试"""
        log_test("📋 第4步：执行平仓测试", "INFO")
        log_test("-" * 50, "INFO")
        
        try:
            if not buy_result or not buy_result.get('success'):
                log_test("❌ 买入结果无效，无法执行平仓", "ERROR")
                self.test_results.append("❌ 平仓测试跳过（买入失败）")
                return
            
            amount = buy_result.get('amount')
            symbol = buy_result.get('symbol')
            
            log_test(f"🔄 准备平仓: {amount} BTC", "INFO")
            
            # 构建平仓订单
            close_params = {
                'currency_pair': symbol,
                'side': 'sell',
                'amount': amount,
                'type': 'market'
            }
            
            log_test(f"📋 平仓参数: {close_params}", "INFO")
            
            # 执行平仓
            result = self.make_authenticated_request('POST', '/spot/orders', data=close_params)
            
            if result:
                order_id = result.get('id')
                log_test(f"✅ 平仓订单提交成功: 订单ID {order_id}", "INFO")
                
                # 等待订单执行
                time.sleep(3)
                
                # 检查订单状态
                order_status = self.check_order_status(order_id)
                if order_status:
                    actual_status = order_status.get('status')
                    filled_amount = float(order_status.get('filled_amount', 0))
                    
                    log_test(f"📊 平仓状态: {actual_status}, 成交数量: {filled_amount:.6f} BTC", "INFO")
                    
                    if actual_status == 'closed' and filled_amount > 0:
                        log_test("✅ 平仓订单完全成交", "INFO")
                        self.test_results.append("✅ 平仓测试成功")
                        
                        # 检查最终余额
                        time.sleep(2)
                        self.check_final_balance()
                    else:
                        log_test(f"⚠️ 平仓订单状态异常: {actual_status}", "WARNING")
                        self.test_results.append(f"⚠️ 平仓状态异常: {actual_status}")
                else:
                    log_test("❌ 无法获取平仓订单状态", "ERROR")
                    self.test_results.append("❌ 平仓订单状态检查失败")
            else:
                log_test("❌ 平仓订单提交失败", "ERROR")
                self.test_results.append("❌ 平仓订单提交失败")
                
        except Exception as e:
            log_test(f"❌ 平仓测试异常: {e}", "ERROR")
            self.test_results.append(f"❌ 平仓测试异常: {e}")
    
    def check_final_balance(self):
        """检查最终余额"""
        try:
            balance_data = self.make_authenticated_request('GET', '/spot/accounts')
            
            if balance_data:
                final_usdt = 0
                final_btc = 0
                for account in balance_data:
                    if account.get('currency') == 'USDT':
                        final_usdt = float(account.get('available', 0))
                    elif account.get('currency') == 'BTC':
                        final_btc = float(account.get('available', 0))
                
                usdt_change = final_usdt - self.usdt_balance
                btc_change = final_btc - self.btc_balance
                
                log_test(f"📊 最终余额: USDT={final_usdt:.2f} (变化: {usdt_change:+.2f})", "INFO")
                log_test(f"📊 最终余额: BTC={final_btc:.6f} (变化: {btc_change:+.6f})", "INFO")
                
                self.test_results.append(f"✅ 余额变化: USDT{usdt_change:+.2f}, BTC{btc_change:+.6f}")
            else:
                log_test("❌ 无法获取最终余额", "ERROR")
                self.test_results.append("❌ 最终余额检查失败")
                
        except Exception as e:
            log_test(f"❌ 最终余额检查异常: {e}", "ERROR")
            self.test_results.append(f"❌ 最终余额检查异常: {e}")
    
    def generate_final_report(self):
        """生成最终报告"""
        log_test("=" * 80, "INFO")
        log_test("📊 Gate.io真实交易测试报告", "INFO")
        log_test("=" * 80, "INFO")
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r.startswith("✅")])
        failed_tests = len([r for r in self.test_results if r.startswith("❌")])
        warning_tests = len([r for r in self.test_results if r.startswith("⚠️")])
        
        log_test(f"📈 测试统计:", "INFO")
        log_test(f"   总测试项: {total_tests}", "INFO")
        log_test(f"   成功: {passed_tests}", "INFO")
        log_test(f"   失败: {failed_tests}", "INFO")
        log_test(f"   警告: {warning_tests}", "INFO")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        log_test(f"   成功率: {success_rate:.1f}%", "INFO")
        
        log_test("", "INFO")
        log_test("📋 详细测试结果:", "INFO")
        for i, result in enumerate(self.test_results, 1):
            log_test(f"   {i:2d}. {result}", "INFO")
        
        log_test("", "INFO")
        if success_rate >= 80:
            log_test("🎉 测试总体评价: 优秀", "INFO")
            log_test("✅ Gate.io真实交易流程完全正常", "INFO")
        elif success_rate >= 60:
            log_test("👍 测试总体评价: 良好", "INFO")
            log_test("⚠️ Gate.io真实交易流程基本正常", "WARNING")
        else:
            log_test("❌ 测试总体评价: 需要改进", "ERROR")
            log_test("❌ Gate.io真实交易流程存在问题", "ERROR")
        
        log_test("=" * 80, "INFO")

if __name__ == "__main__":
    log_test("🚨 警告：即将开始真实交易测试！", "WARNING")
    log_test("⚠️ 此测试将使用真实资金进行买入和卖出操作", "WARNING")
    log_test("💡 请确保您了解风险并同意继续", "INFO")
    log_test("", "INFO")
    
    # 执行真实交易测试
    tester = GateIORealTradingTest()
    tester.execute_real_trading_test()
