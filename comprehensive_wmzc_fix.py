#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC交易系统综合修复工具
解决配置持久化、配置警告、系统闪退、配置分散等问题
"""

import os
import json
import shutil
import traceback
from datetime import datetime
from pathlib import Path

class ComprehensiveWMZCFixer:
    """WMZC综合修复器"""
    
    def __init__(self):
        self.current_dir = os.getcwd()
        self.wmzc_config_dir = os.path.expanduser("~/.wmzc_trading")
        self.windows_config_dir = os.path.join(os.environ.get('APPDATA', ''), 'WMZC')
        
        self.config_files = [
            'trading_config.json',
            'wmzc_config.json',
            'user_settings.json',
            'misc_optimization_config.json',
            'ai_config.json'
        ]
        
        self.test_api_patterns = [
            'da636867-490f-4e3e-81b2-870841afb860',
            'C15B6EE0CF3FFDEE5834865D3839325E',
            'Mx123456@',
            'test_final_api_key',
            'test_api_key'
        ]
        
        self.fixes_applied = []
        
    def run_comprehensive_fix(self):
        """运行综合修复"""
        print("🔧 WMZC交易系统综合修复")
        print("=" * 60)
        
        try:
            # 1. 创建备份
            self.create_comprehensive_backup()
            
            # 2. 修复配置持久化问题
            self.fix_config_persistence()
            
            # 3. 清理配置警告问题
            self.clean_config_warnings()
            
            # 4. 修复系统闪退问题
            self.fix_system_crashes()
            
            # 5. 统一配置文件管理
            self.unify_config_management()
            
            # 6. 验证修复效果
            self.verify_fixes()
            
            # 7. 生成修复报告
            self.generate_fix_report()
            
            return len(self.fixes_applied) > 0
            
        except Exception as e:
            print(f"❌ 综合修复过程中发生异常: {e}")
            traceback.print_exc()
            return False
    
    def create_comprehensive_backup(self):
        """创建综合备份"""
        print("\n💾 创建综合备份...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = f'wmzc_comprehensive_backup_{timestamp}'
        
        try:
            os.makedirs(backup_dir, exist_ok=True)
            
            # 备份当前目录的配置文件
            for config_file in self.config_files:
                if os.path.exists(config_file):
                    shutil.copy2(config_file, os.path.join(backup_dir, f"current_{config_file}"))
                    print(f"  ✅ 已备份: {config_file}")
            
            # 备份WMZC配置目录
            if os.path.exists(self.wmzc_config_dir):
                wmzc_backup_dir = os.path.join(backup_dir, 'wmzc_trading')
                shutil.copytree(self.wmzc_config_dir, wmzc_backup_dir, dirs_exist_ok=True)
                print(f"  ✅ 已备份WMZC配置目录")
            
            # 备份WMZC.py的关键部分
            if os.path.exists('WMZC.py'):
                shutil.copy2('WMZC.py', os.path.join(backup_dir, 'WMZC.py.backup'))
                print(f"  ✅ 已备份WMZC.py")
            
            self.fixes_applied.append(f"创建综合备份: {backup_dir}")
            
        except Exception as e:
            print(f"  ❌ 创建备份失败: {e}")
    
    def fix_config_persistence(self):
        """修复配置持久化问题"""
        print("\n🔧 修复配置持久化问题...")
        
        # 修复1: 检查并修复WMZC.py中的_auto_init_config函数
        if os.path.exists('WMZC.py'):
            try:
                with open('WMZC.py', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否已经修复
                if '🔒 检测到用户配置，跳过自动初始化' in content:
                    print("  ✅ _auto_init_config函数已修复")
                else:
                    print("  ⚠️ _auto_init_config函数需要修复")
                    print("  💡 请手动检查WMZC.py第29785-29839行的修复")
                
            except Exception as e:
                print(f"  ❌ 检查WMZC.py失败: {e}")
        
        # 修复2: 创建统一的配置管理器
        self.create_unified_config_manager()
        
        self.fixes_applied.append("配置持久化问题修复")
    
    def clean_config_warnings(self):
        """清理配置警告问题"""
        print("\n🧹 清理配置警告问题...")
        
        # 清理当前目录的配置文件
        self.clean_directory_configs(self.current_dir, "当前目录")
        
        # 清理WMZC配置目录
        if os.path.exists(self.wmzc_config_dir):
            self.clean_directory_configs(self.wmzc_config_dir, "WMZC配置目录")
        else:
            # 创建干净的WMZC配置目录
            self.create_clean_wmzc_directory()
        
        self.fixes_applied.append("配置警告问题清理")
    
    def clean_directory_configs(self, directory, dir_name):
        """清理指定目录的配置文件"""
        print(f"  🧹 清理{dir_name}: {directory}")
        
        cleaned_count = 0
        
        for config_file in self.config_files:
            file_path = os.path.join(directory, config_file)
            
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 检查并清理测试API
                    has_test_api = False
                    for key, value in config.items():
                        if isinstance(value, str):
                            for pattern in self.test_api_patterns:
                                if pattern in value:
                                    config[key] = ""
                                    has_test_api = True
                    
                    if has_test_api:
                        # 添加清理标记
                        config['_TEST_API_CLEANED'] = datetime.now().isoformat()
                        config['_CONFIG_PROTECTED'] = True
                        
                        # 保存清理后的配置
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(config, f, indent=2, ensure_ascii=False)
                        
                        print(f"    ✅ 已清理: {config_file}")
                        cleaned_count += 1
                    else:
                        print(f"    ✅ 已干净: {config_file}")
                        
                except Exception as e:
                    print(f"    ❌ 清理 {config_file} 失败: {e}")
        
        print(f"  📊 {dir_name}清理结果: {cleaned_count} 个文件")
    
    def create_clean_wmzc_directory(self):
        """创建干净的WMZC配置目录"""
        print("  🔧 创建干净的WMZC配置目录...")
        
        try:
            os.makedirs(self.wmzc_config_dir, exist_ok=True)
            
            # 创建干净的配置模板
            clean_config = {
                "API_KEY": "",
                "API_SECRET": "",
                "PASSPHRASE": "",
                "OKX_API_KEY": "",
                "OKX_SECRET_KEY": "",
                "OKX_PASSPHRASE": "",
                "EXCHANGE": "OKX",
                "SYMBOL": "BTC-USDT-SWAP",
                "TIMEFRAME": "1m",
                "ORDER_USDT_AMOUNT": 10,
                "LEVERAGE": 3,
                "RISK_PERCENT": 1.0,
                "TEST_MODE": True,
                "ENABLE_TRADING": False,
                "_CONFIG_CLEANED": datetime.now().isoformat(),
                "_CONFIG_PROTECTED": True
            }
            
            # 创建所有配置文件
            for config_file in self.config_files:
                file_path = os.path.join(self.wmzc_config_dir, config_file)
                
                # 为wmzc_config.json使用特殊格式
                if config_file == 'wmzc_config.json':
                    wmzc_config = {
                        "exchange_selection": "OKX",
                        "okx_api_key": "",
                        "okx_secret_key": "",
                        "okx_passphrase": "",
                        "default_symbol": "BTC-USDT-SWAP",
                        "default_timeframe": "1m",
                        "_CONFIG_CLEANED": datetime.now().isoformat(),
                        "_CONFIG_PROTECTED": True
                    }
                    config_to_save = wmzc_config
                else:
                    config_to_save = clean_config
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_to_save, f, indent=2, ensure_ascii=False)
                
                print(f"    ✅ 已创建: {config_file}")
            
        except Exception as e:
            print(f"  ❌ 创建WMZC配置目录失败: {e}")
    
    def fix_system_crashes(self):
        """修复系统闪退问题"""
        print("\n🛡️ 修复系统闪退问题...")
        
        # 创建技术指标安全计算模块
        safe_indicator_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ 安全技术指标计算模块
防止DataFrame布尔值判断错误导致的系统崩溃
"""

import pandas as pd
import numpy as np
import logging

def safe_dataframe_value(df, default=0):
    """安全获取DataFrame的值"""
    try:
        if isinstance(df, pd.DataFrame):
            if df.empty:
                return default
            return df.iloc[-1].iloc[0] if hasattr(df.iloc[-1], 'iloc') else df.iloc[-1]
        elif isinstance(df, pd.Series):
            if df.empty:
                return default
            return df.iloc[-1]
        else:
            return df if df is not None else default
    except Exception as e:
        logging.warning(f"安全获取DataFrame值失败: {e}")
        return default

def safe_macd_signal(macd_value, threshold=0):
    """安全的MACD信号判断"""
    try:
        value = safe_dataframe_value(macd_value, 0)
        if value > threshold:
            return "BUY"
        elif value < -threshold:
            return "SELL"
        else:
            return "HOLD"
    except Exception as e:
        logging.error(f"MACD信号判断失败: {e}")
        return "HOLD"

def safe_kdj_signal(k_value, d_value=None, oversold=20, overbought=80):
    """安全的KDJ信号判断"""
    try:
        k = safe_dataframe_value(k_value, 50)
        if k < oversold:
            return "BUY"
        elif k > overbought:
            return "SELL"
        else:
            return "HOLD"
    except Exception as e:
        logging.error(f"KDJ信号判断失败: {e}")
        return "HOLD"

def safe_rsi_signal(rsi_value, oversold=30, overbought=70):
    """安全的RSI信号判断"""
    try:
        rsi = safe_dataframe_value(rsi_value, 50)
        if rsi < oversold:
            return "BUY"
        elif rsi > overbought:
            return "SELL"
        else:
            return "HOLD"
    except Exception as e:
        logging.error(f"RSI信号判断失败: {e}")
        return "HOLD"

def safe_api_call(func):
    """安全API调用装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if "403" in str(e) or "Forbidden" in str(e):
                logging.warning(f"API认证失败: {e}")
                return None
            else:
                logging.error(f"API调用失败: {e}")
                return None
    return wrapper

# 导出安全函数
__all__ = [
    'safe_dataframe_value',
    'safe_macd_signal',
    'safe_kdj_signal', 
    'safe_rsi_signal',
    'safe_api_call'
]
'''
        
        try:
            with open('safe_indicators.py', 'w', encoding='utf-8') as f:
                f.write(safe_indicator_code)
            print("  ✅ 安全技术指标模块已创建: safe_indicators.py")
            self.fixes_applied.append("系统闪退问题修复")
        except Exception as e:
            print(f"  ❌ 创建安全指标模块失败: {e}")
    
    def unify_config_management(self):
        """统一配置文件管理"""
        print("\n🔄 统一配置文件管理...")
        
        # 创建统一配置管理器
        self.create_unified_config_manager()
        
        # 创建配置同步脚本
        self.create_config_sync_script()
        
        self.fixes_applied.append("配置文件管理统一")
    
    def create_unified_config_manager(self):
        """创建统一配置管理器"""
        config_manager_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC统一配置管理器
解决配置文件分散和不一致问题
"""

import os
import json
from datetime import datetime

class UnifiedConfigManager:
    """统一配置管理器"""
    
    def __init__(self):
        self.current_dir = os.getcwd()
        self.wmzc_config_dir = os.path.expanduser("~/.wmzc_trading")
        
        self.config_files = {
            'main': 'trading_config.json',
            'wmzc': 'wmzc_config.json',
            'user': 'user_settings.json',
            'misc': 'misc_optimization_config.json',
            'ai': 'ai_config.json'
        }
        
        self.master_config = None
    
    def load_master_config(self):
        """加载主配置"""
        master_file = os.path.join(self.current_dir, 'trading_config.json')
        
        if os.path.exists(master_file):
            try:
                with open(master_file, 'r', encoding='utf-8') as f:
                    self.master_config = json.load(f)
                return self.master_config
            except Exception as e:
                print(f"加载主配置失败: {e}")
                return {}
        return {}
    
    def sync_all_configs(self):
        """同步所有配置文件"""
        if not self.master_config:
            self.load_master_config()
        
        if not self.master_config:
            print("主配置为空，无法同步")
            return False
        
        sync_count = 0
        
        # 同步到当前目录
        for config_name, config_file in self.config_files.items():
            if self.sync_config_file(self.current_dir, config_file):
                sync_count += 1
        
        # 同步到WMZC配置目录
        os.makedirs(self.wmzc_config_dir, exist_ok=True)
        for config_name, config_file in self.config_files.items():
            if self.sync_config_file(self.wmzc_config_dir, config_file):
                sync_count += 1
        
        print(f"配置同步完成: {sync_count} 个文件")
        return sync_count > 0
    
    def sync_config_file(self, directory, config_file):
        """同步单个配置文件"""
        file_path = os.path.join(directory, config_file)
        
        try:
            # 读取现有配置
            existing_config = {}
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
            
            # 合并配置
            if config_file == 'wmzc_config.json':
                # WMZC特殊格式
                merged_config = existing_config.copy()
                merged_config.update({
                    'okx_api_key': self.master_config.get('API_KEY', ''),
                    'okx_secret_key': self.master_config.get('API_SECRET', ''),
                    'okx_passphrase': self.master_config.get('PASSPHRASE', ''),
                    'exchange_selection': self.master_config.get('EXCHANGE', 'OKX'),
                    'default_symbol': self.master_config.get('SYMBOL', 'BTC-USDT-SWAP'),
                    'default_timeframe': self.master_config.get('TIMEFRAME', '1m'),
                    '_LAST_SYNC': datetime.now().isoformat()
                })
            else:
                # 标准格式
                merged_config = existing_config.copy()
                merged_config.update(self.master_config)
                merged_config['_LAST_SYNC'] = datetime.now().isoformat()
            
            # 保存配置
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(merged_config, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"同步 {config_file} 失败: {e}")
            return False

# 全局配置管理器实例
unified_config_manager = UnifiedConfigManager()

def sync_configs():
    """同步配置的便捷函数"""
    return unified_config_manager.sync_all_configs()

if __name__ == "__main__":
    sync_configs()
'''
        
        try:
            with open('unified_config_manager.py', 'w', encoding='utf-8') as f:
                f.write(config_manager_code)
            print("  ✅ 统一配置管理器已创建: unified_config_manager.py")
        except Exception as e:
            print(f"  ❌ 创建统一配置管理器失败: {e}")
    
    def create_config_sync_script(self):
        """创建配置同步脚本"""
        sync_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 WMZC配置同步脚本
一键同步所有配置文件
"""

from unified_config_manager import sync_configs

def main():
    print("🔄 开始同步WMZC配置文件...")
    
    success = sync_configs()
    
    if success:
        print("✅ 配置同步完成！")
    else:
        print("❌ 配置同步失败！")

if __name__ == "__main__":
    main()
'''
        
        try:
            with open('sync_configs.py', 'w', encoding='utf-8') as f:
                f.write(sync_script)
            print("  ✅ 配置同步脚本已创建: sync_configs.py")
        except Exception as e:
            print(f"  ❌ 创建配置同步脚本失败: {e}")
    
    def verify_fixes(self):
        """验证修复效果"""
        print("\n✅ 验证修复效果...")
        
        verification_results = {
            'config_files_clean': 0,
            'wmzc_directory_exists': False,
            'safe_modules_created': 0,
            'unified_manager_created': False
        }
        
        # 验证配置文件清理
        for config_file in self.config_files:
            if self.verify_config_clean(config_file):
                verification_results['config_files_clean'] += 1
        
        # 验证WMZC目录
        verification_results['wmzc_directory_exists'] = os.path.exists(self.wmzc_config_dir)
        
        # 验证安全模块
        safe_modules = ['safe_indicators.py', 'unified_config_manager.py', 'sync_configs.py']
        for module in safe_modules:
            if os.path.exists(module):
                verification_results['safe_modules_created'] += 1
        
        # 验证统一管理器
        verification_results['unified_manager_created'] = os.path.exists('unified_config_manager.py')
        
        # 显示验证结果
        print(f"  📊 验证结果:")
        print(f"    干净配置文件: {verification_results['config_files_clean']}/{len(self.config_files)}")
        print(f"    WMZC目录存在: {'✅' if verification_results['wmzc_directory_exists'] else '❌'}")
        print(f"    安全模块创建: {verification_results['safe_modules_created']}/3")
        print(f"    统一管理器: {'✅' if verification_results['unified_manager_created'] else '❌'}")
        
        return verification_results
    
    def verify_config_clean(self, config_file):
        """验证配置文件是否干净"""
        file_path = os.path.join(self.current_dir, config_file)
        
        if not os.path.exists(file_path):
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查是否包含测试API
            for key, value in config.items():
                if isinstance(value, str):
                    for pattern in self.test_api_patterns:
                        if pattern in value:
                            return False
            
            return True
            
        except Exception:
            return False
    
    def generate_fix_report(self):
        """生成修复报告"""
        print("\n📊 生成修复报告...")
        
        report = {
            'fix_time': datetime.now().isoformat(),
            'fixes_applied': self.fixes_applied,
            'verification_results': self.verify_fixes(),
            'next_steps': [
                '重新启动WMZC系统',
                '在主配置页面填写真实API密钥',
                '运行 python sync_configs.py 同步配置',
                '验证系统稳定性'
            ]
        }
        
        try:
            with open('wmzc_fix_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print("  ✅ 修复报告已保存: wmzc_fix_report.json")
        except Exception as e:
            print(f"  ❌ 保存修复报告失败: {e}")

def main():
    """主函数"""
    print("🔧 WMZC交易系统综合修复工具")
    print("=" * 60)
    
    fixer = ComprehensiveWMZCFixer()
    
    try:
        success = fixer.run_comprehensive_fix()
        
        print("\n" + "=" * 60)
        if success:
            print(f"🎉 综合修复完成！共应用了 {len(fixer.fixes_applied)} 个修复")
            print("\n💡 修复内容:")
            for i, fix in enumerate(fixer.fixes_applied, 1):
                print(f"  {i}. ✅ {fix}")
            
            print("\n🚀 下一步操作:")
            print("  1. 重新启动WMZC系统")
            print("  2. 在主配置页面填写真实API密钥")
            print("  3. 运行 python sync_configs.py 同步配置")
            print("  4. 验证系统稳定性和配置持久化")
            
            print("\n🛡️ 预防措施:")
            print("  • 定期运行配置同步脚本")
            print("  • 使用安全技术指标模块")
            print("  • 监控配置文件一致性")
            print("  • 定期备份配置文件")
            
        else:
            print("❌ 综合修复失败")
            print("💡 请检查错误信息并手动修复")
        
        return success
        
    except Exception as e:
        print(f"❌ 修复过程中发生异常: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
