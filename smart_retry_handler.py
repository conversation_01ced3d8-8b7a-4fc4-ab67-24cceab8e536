#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能重试处理器 - 为WMZC量化交易系统优化错误处理和重试机制
支持不同错误类型的智能重试策略，提升系统稳定性和可靠性
"""

import asyncio
import time
import logging
import random
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Callable, Any, Union
from enum import Enum
import traceback
import inspect

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    SERVER_ERROR = "server_error"
    AUTHENTICATION_ERROR = "auth_error"
    VALIDATION_ERROR = "validation_error"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"


class BackoffStrategy(Enum):
    """退避策略枚举"""
    FIXED = "fixed"
    LINEAR = "linear"
    EXPONENTIAL = "exponential"
    EXPONENTIAL_JITTER = "exponential_jitter"
    FIBONACCI = "fibonacci"


@dataclass
class RetryConfig:
    """重试配置"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_strategy: BackoffStrategy = BackoffStrategy.EXPONENTIAL
    jitter: bool = True
    timeout: Optional[float] = None
    retry_on_exceptions: List[type] = field(default_factory=list)
    stop_on_exceptions: List[type] = field(default_factory=list)


@dataclass
class RetryAttempt:
    """重试尝试记录"""
    attempt_number: int
    error: Exception
    delay: float
    timestamp: float
    duration: float = 0.0


@dataclass
class RetryResult:
    """重试结果"""
    success: bool
    result: Any = None
    error: Optional[Exception] = None
    total_attempts: int = 0
    total_duration: float = 0.0
    attempts: List[RetryAttempt] = field(default_factory=list)


class SmartRetryHandler:
    """
    智能重试处理器
    
    功能特性：
    1. 支持多种退避策略（固定、线性、指数、斐波那契）
    2. 智能错误分类和处理
    3. 可配置的重试条件
    4. 详细的重试统计和监控
    5. 异步和同步函数支持
    6. 上下文管理器支持
    """
    
    def __init__(self):
        # 预定义的错误类型配置
        self.error_configs = {
            ErrorType.NETWORK_ERROR: RetryConfig(
                max_retries=5,
                base_delay=1.0,
                max_delay=30.0,
                backoff_strategy=BackoffStrategy.EXPONENTIAL_JITTER,
                timeout=30.0
            ),
            ErrorType.RATE_LIMIT_ERROR: RetryConfig(
                max_retries=10,
                base_delay=2.0,
                max_delay=120.0,
                backoff_strategy=BackoffStrategy.LINEAR,
                timeout=60.0
            ),
            ErrorType.SERVER_ERROR: RetryConfig(
                max_retries=3,
                base_delay=5.0,
                max_delay=60.0,
                backoff_strategy=BackoffStrategy.EXPONENTIAL,
                timeout=45.0
            ),
            ErrorType.AUTHENTICATION_ERROR: RetryConfig(
                max_retries=1,
                base_delay=1.0,
                max_delay=5.0,
                backoff_strategy=BackoffStrategy.FIXED
            ),
            ErrorType.VALIDATION_ERROR: RetryConfig(
                max_retries=0,  # 验证错误不重试
                base_delay=0.0,
                max_delay=0.0
            ),
            ErrorType.TIMEOUT_ERROR: RetryConfig(
                max_retries=3,
                base_delay=2.0,
                max_delay=30.0,
                backoff_strategy=BackoffStrategy.EXPONENTIAL
            ),
            ErrorType.UNKNOWN_ERROR: RetryConfig(
                max_retries=2,
                base_delay=1.0,
                max_delay=10.0,
                backoff_strategy=BackoffStrategy.EXPONENTIAL
            )
        }
        
        # 错误分类器
        self.error_classifiers = {
            # 网络错误
            'ConnectionError': ErrorType.NETWORK_ERROR,
            'TimeoutError': ErrorType.TIMEOUT_ERROR,
            'ConnectTimeout': ErrorType.TIMEOUT_ERROR,
            'ReadTimeout': ErrorType.TIMEOUT_ERROR,
            'HTTPError': ErrorType.NETWORK_ERROR,
            
            # 限频错误
            'RateLimitError': ErrorType.RATE_LIMIT_ERROR,
            'TooManyRequests': ErrorType.RATE_LIMIT_ERROR,
            
            # 服务器错误
            'InternalServerError': ErrorType.SERVER_ERROR,
            'BadGateway': ErrorType.SERVER_ERROR,
            'ServiceUnavailable': ErrorType.SERVER_ERROR,
            'GatewayTimeout': ErrorType.SERVER_ERROR,
            
            # 认证错误
            'AuthenticationError': ErrorType.AUTHENTICATION_ERROR,
            'Unauthorized': ErrorType.AUTHENTICATION_ERROR,
            'Forbidden': ErrorType.AUTHENTICATION_ERROR,
            
            # 验证错误
            'ValidationError': ErrorType.VALIDATION_ERROR,
            'ValueError': ErrorType.VALIDATION_ERROR,
            'TypeError': ErrorType.VALIDATION_ERROR,
        }
        
        # 统计信息
        self.stats = {
            'total_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'total_retries': 0,
            'error_type_counts': {error_type.value: 0 for error_type in ErrorType},
            'avg_retry_count': 0.0,
            'avg_success_time': 0.0,
            'max_retry_count': 0
        }
        
        logger.info("✅ 智能重试处理器初始化完成")

    def classify_error(self, error: Exception) -> ErrorType:
        """分类错误类型"""
        error_name = type(error).__name__
        error_message = str(error).lower()
        
        # 首先检查错误类型名称
        if error_name in self.error_classifiers:
            return self.error_classifiers[error_name]
        
        # 然后检查错误消息中的关键词
        if any(keyword in error_message for keyword in ['rate limit', 'too many requests', '429']):
            return ErrorType.RATE_LIMIT_ERROR
        elif any(keyword in error_message for keyword in ['timeout', 'timed out']):
            return ErrorType.TIMEOUT_ERROR
        elif any(keyword in error_message for keyword in ['connection', 'network', 'unreachable']):
            return ErrorType.NETWORK_ERROR
        elif any(keyword in error_message for keyword in ['server error', '500', '502', '503', '504']):
            return ErrorType.SERVER_ERROR
        elif any(keyword in error_message for keyword in ['unauthorized', 'forbidden', '401', '403']):
            return ErrorType.AUTHENTICATION_ERROR
        elif any(keyword in error_message for keyword in ['invalid', 'validation', 'bad request', '400']):
            return ErrorType.VALIDATION_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR

    def get_retry_config(self, error_type: ErrorType) -> RetryConfig:
        """获取重试配置"""
        return self.error_configs.get(error_type, self.error_configs[ErrorType.UNKNOWN_ERROR])

    def calculate_delay(self, attempt: int, config: RetryConfig) -> float:
        """计算延迟时间"""
        if config.backoff_strategy == BackoffStrategy.FIXED:
            delay = config.base_delay
        elif config.backoff_strategy == BackoffStrategy.LINEAR:
            delay = config.base_delay * attempt
        elif config.backoff_strategy == BackoffStrategy.EXPONENTIAL:
            delay = config.base_delay * (2 ** (attempt - 1))
        elif config.backoff_strategy == BackoffStrategy.EXPONENTIAL_JITTER:
            base_delay = config.base_delay * (2 ** (attempt - 1))
            jitter = random.uniform(0.1, 0.3) * base_delay
            delay = base_delay + jitter
        elif config.backoff_strategy == BackoffStrategy.FIBONACCI:
            delay = config.base_delay * self._fibonacci(attempt)
        else:
            delay = config.base_delay
        
        # 应用最大延迟限制
        delay = min(delay, config.max_delay)
        
        # 添加随机抖动（如果启用）
        if config.jitter and config.backoff_strategy != BackoffStrategy.EXPONENTIAL_JITTER:
            jitter = random.uniform(-0.1, 0.1) * delay
            delay = max(0.1, delay + jitter)
        
        return delay

    def _fibonacci(self, n: int) -> int:
        """计算斐波那契数列"""
        if n <= 1:
            return 1
        elif n == 2:
            return 1
        else:
            a, b = 1, 1
            for _ in range(2, n):
                a, b = b, a + b
            return b

    async def execute_with_retry(
        self,
        func: Callable,
        *args,
        error_type: Optional[ErrorType] = None,
        custom_config: Optional[RetryConfig] = None,
        **kwargs
    ) -> RetryResult:
        """
        执行函数并在失败时重试
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            error_type: 预期的错误类型（可选）
            custom_config: 自定义重试配置（可选）
            **kwargs: 函数关键字参数
            
        Returns:
            RetryResult: 重试结果
        """
        start_time = time.time()
        attempts = []
        
        self.stats['total_calls'] += 1
        
        # 确定重试配置
        if custom_config:
            config = custom_config
        elif error_type:
            config = self.get_retry_config(error_type)
        else:
            config = self.error_configs[ErrorType.UNKNOWN_ERROR]
        
        for attempt in range(config.max_retries + 1):
            attempt_start = time.time()
            
            try:
                # 执行函数
                if inspect.iscoroutinefunction(func):
                    if config.timeout:
                        result = await asyncio.wait_for(func(*args, **kwargs), timeout=config.timeout)
                    else:
                        result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 成功执行
                total_duration = time.time() - start_time
                self.stats['successful_calls'] += 1
                self.stats['total_retries'] += attempt
                
                if attempt > self.stats['max_retry_count']:
                    self.stats['max_retry_count'] = attempt
                
                # 更新平均成功时间
                if self.stats['successful_calls'] == 1:
                    self.stats['avg_success_time'] = total_duration
                else:
                    self.stats['avg_success_time'] = (
                        (self.stats['avg_success_time'] * (self.stats['successful_calls'] - 1) + total_duration)
                        / self.stats['successful_calls']
                    )
                
                logger.info(f"✅ 函数执行成功 (尝试次数: {attempt + 1}, 耗时: {total_duration:.2f}s)")
                
                return RetryResult(
                    success=True,
                    result=result,
                    total_attempts=attempt + 1,
                    total_duration=total_duration,
                    attempts=attempts
                )
                
            except Exception as error:
                attempt_duration = time.time() - attempt_start
                
                # 分类错误
                classified_error_type = self.classify_error(error)
                self.stats['error_type_counts'][classified_error_type.value] += 1
                
                # 记录尝试
                if attempt < config.max_retries:
                    delay = self.calculate_delay(attempt + 1, config)
                    attempts.append(RetryAttempt(
                        attempt_number=attempt + 1,
                        error=error,
                        delay=delay,
                        timestamp=time.time(),
                        duration=attempt_duration
                    ))
                    
                    logger.warning(f"⚠️ 函数执行失败 (尝试 {attempt + 1}/{config.max_retries + 1}): {error}")
                    logger.info(f"🔄 {delay:.2f}s后重试...")
                    
                    await asyncio.sleep(delay)
                else:
                    # 最后一次尝试失败
                    attempts.append(RetryAttempt(
                        attempt_number=attempt + 1,
                        error=error,
                        delay=0,
                        timestamp=time.time(),
                        duration=attempt_duration
                    ))
                    
                    total_duration = time.time() - start_time
                    self.stats['failed_calls'] += 1
                    self.stats['total_retries'] += attempt + 1
                    
                    logger.error(f"❌ 函数执行最终失败 (总尝试次数: {attempt + 1}, 总耗时: {total_duration:.2f}s): {error}")
                    
                    return RetryResult(
                        success=False,
                        error=error,
                        total_attempts=attempt + 1,
                        total_duration=total_duration,
                        attempts=attempts
                    )

    def execute_with_retry_sync(
        self,
        func: Callable,
        *args,
        error_type: Optional[ErrorType] = None,
        custom_config: Optional[RetryConfig] = None,
        **kwargs
    ) -> RetryResult:
        """
        同步版本的重试执行
        """
        # 如果在异步环境中，使用异步版本
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 在异步环境中，创建任务
                return asyncio.create_task(
                    self.execute_with_retry(func, *args, error_type=error_type, 
                                          custom_config=custom_config, **kwargs)
                )
        except RuntimeError:
            pass
        
        # 在同步环境中运行
        return asyncio.run(
            self.execute_with_retry(func, *args, error_type=error_type, 
                                  custom_config=custom_config, **kwargs)
        )

    def get_stats(self) -> Dict:
        """获取统计信息"""
        total_calls = self.stats['total_calls']
        if total_calls > 0:
            self.stats['avg_retry_count'] = self.stats['total_retries'] / total_calls
        
        return {
            **self.stats,
            'success_rate': (self.stats['successful_calls'] / total_calls * 100) if total_calls > 0 else 0.0,
            'failure_rate': (self.stats['failed_calls'] / total_calls * 100) if total_calls > 0 else 0.0
        }

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'total_retries': 0,
            'error_type_counts': {error_type.value: 0 for error_type in ErrorType},
            'avg_retry_count': 0.0,
            'avg_success_time': 0.0,
            'max_retry_count': 0
        }
        logger.info("📊 智能重试处理器统计已重置")

    def update_error_config(self, error_type: ErrorType, config: RetryConfig):
        """更新错误类型配置"""
        self.error_configs[error_type] = config
        logger.info(f"🔧 已更新 {error_type.value} 的重试配置")

    def add_error_classifier(self, error_name: str, error_type: ErrorType):
        """添加错误分类器"""
        self.error_classifiers[error_name] = error_type
        logger.info(f"🔧 已添加错误分类器: {error_name} -> {error_type.value}")


# 全局实例
smart_retry_handler = SmartRetryHandler()


# 装饰器
def retry_on_error(
    error_type: Optional[ErrorType] = None,
    custom_config: Optional[RetryConfig] = None
):
    """重试装饰器"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            result = await smart_retry_handler.execute_with_retry(
                func, *args, error_type=error_type, custom_config=custom_config, **kwargs
            )
            if result.success:
                return result.result
            else:
                raise result.error
        
        def sync_wrapper(*args, **kwargs):
            result = smart_retry_handler.execute_with_retry_sync(
                func, *args, error_type=error_type, custom_config=custom_config, **kwargs
            )
            if result.success:
                return result.result
            else:
                raise result.error
        
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


if __name__ == "__main__":
    # 测试代码
    async def test_smart_retry_handler():
        """测试智能重试处理器"""
        print("🧪 开始测试智能重试处理器...")
        
        handler = SmartRetryHandler()
        
        # 测试函数：模拟网络错误
        async def flaky_network_function():
            import random
            if random.random() < 0.7:  # 70%概率失败
                raise ConnectionError("网络连接失败")
            return "网络请求成功"
        
        # 测试重试机制
        result = await handler.execute_with_retry(
            flaky_network_function,
            error_type=ErrorType.NETWORK_ERROR
        )
        
        print(f"📊 重试结果:")
        print(f"成功: {result.success}")
        print(f"结果: {result.result}")
        print(f"总尝试次数: {result.total_attempts}")
        print(f"总耗时: {result.total_duration:.2f}s")
        print(f"重试记录: {len(result.attempts)}次")
        
        # 获取统计信息
        stats = handler.get_stats()
        print(f"\n📈 处理器统计:")
        print(f"总调用次数: {stats['total_calls']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        print(f"平均重试次数: {stats['avg_retry_count']:.1f}")
        print(f"平均成功时间: {stats['avg_success_time']:.2f}s")
    
    # 运行测试
    asyncio.run(test_smart_retry_handler())
