#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 配置验证脚本
验证API配置是否正确保存和加载
"""

import os
import json

def verify_config():
    """验证配置"""
    print("🔍 开始验证配置...")
    
    issues = []
    
    # 检查配置文件
    config_files = ['trading_config.json', 'wmzc_config.json']
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                print(f"\n📄 {config_file}:")
                
                # 检查API配置
                if config_file == 'trading_config.json':
                    api_key = config.get('API_KEY', '')
                    api_secret = config.get('API_SECRET', '')
                    passphrase = config.get('PASSPHRASE', '')
                else:
                    api_key = config.get('okx_api_key', '')
                    api_secret = config.get('okx_secret_key', '')
                    passphrase = config.get('okx_passphrase', '')
                
                if api_key and api_secret and passphrase:
                    print(f"  ✅ API配置完整: {api_key[:15]}...")
                else:
                    print(f"  ❌ API配置不完整")
                    issues.append(f"{config_file} API配置不完整")
                
                # 检查保护标记
                if config.get('_CONFIG_PROTECTED'):
                    print(f"  ✅ 配置保护已启用")
                else:
                    print(f"  ❌ 配置保护未启用")
                    issues.append(f"{config_file} 配置保护未启用")
                
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")
                issues.append(f"读取 {config_file} 失败: {e}")
        else:
            print(f"\n📄 {config_file}: ❌ 文件不存在")
            issues.append(f"{config_file} 文件不存在")
    
    if issues:
        print(f"\n❌ 发现 {len(issues)} 个问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        return False
    else:
        print(f"\n✅ 配置验证通过！")
        return True

if __name__ == "__main__":
    verify_config()
