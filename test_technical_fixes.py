#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试技术修复的脚本
"""

import asyncio
import threading
import time
import pandas as pd

def test_technical_fixes():
    """测试技术修复"""
    print("🔧 测试WMZC技术修复...")
    
    try:
        # 设置事件循环
        print("1. 创建事件循环...")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 在后台线程中运行事件循环
        def run_event_loop():
            try:
                loop.run_forever()
            except Exception as e:
                print(f"   事件循环异常: {e}")
        
        loop_thread = threading.Thread(target=run_event_loop, daemon=True)
        loop_thread.start()
        print("✅ 事件循环已启动")
        
        # 等待一下确保事件循环启动
        time.sleep(0.5)
        
        # 导入WMZC
        print("2. 导入WMZC...")
        import WMZC
        print("✅ WMZC导入成功")
        
        # 测试DataFrame安全检查函数
        print("3. 测试DataFrame安全检查...")
        test_df = pd.DataFrame({'close': [100, 101, 102]})
        if WMZC._safe_dataframe_check(test_df):
            print("✅ DataFrame安全检查正常")
        else:
            print("❌ DataFrame安全检查失败")
        
        # 测试空DataFrame检查
        empty_df = pd.DataFrame()
        if not WMZC._safe_dataframe_check(empty_df):
            print("✅ 空DataFrame检查正常")
        else:
            print("❌ 空DataFrame检查失败")
        
        # 测试SuperUnifiedManager的get_kline方法
        print("4. 测试SuperUnifiedManager.get_kline方法...")
        if hasattr(WMZC, 'super_unified_manager'):
            manager = WMZC.super_unified_manager
            if hasattr(manager, 'get_kline'):
                print("✅ SuperUnifiedManager.get_kline方法存在")
                # 测试调用（可能会失败，但不应该报方法不存在的错误）
                try:
                    result = manager.get_kline('BTC-USDT-SWAP', '1m', 10)
                    print(f"✅ get_kline方法调用成功，返回类型: {type(result)}")
                except Exception as e:
                    print(f"⚠️ get_kline方法调用失败（预期）: {e}")
            else:
                print("❌ SuperUnifiedManager.get_kline方法不存在")
        else:
            print("⚠️ super_unified_manager未找到")
        
        # 测试OKX API认证函数
        print("5. 测试OKX API认证...")
        try:
            headers = WMZC.get_okx_headers('GET', '/api/v5/public/time')
            if 'Content-Type' in headers:
                print("✅ OKX请求头生成成功")
                if 'OK-ACCESS-KEY' in headers:
                    print("✅ OKX认证头包含完整")
                else:
                    print("⚠️ OKX认证头不完整（可能是配置问题）")
            else:
                print("❌ OKX请求头生成失败")
        except Exception as e:
            print(f"❌ OKX认证测试失败: {e}")
        
        # 测试增强的API请求函数
        print("6. 测试增强的API请求函数...")
        if hasattr(WMZC, 'enhanced_okx_api_request'):
            print("✅ enhanced_okx_api_request函数存在")
        else:
            print("❌ enhanced_okx_api_request函数不存在")
        
        # 测试技术指标计算
        print("7. 测试技术指标计算...")
        test_data = pd.DataFrame({
            'open': [100, 101, 102, 103, 104],
            'high': [101, 102, 103, 104, 105],
            'low': [99, 100, 101, 102, 103],
            'close': [100.5, 101.5, 102.5, 103.5, 104.5],
            'volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        # 测试MACD计算
        try:
            macd_result = WMZC.calculate_macd(test_data)
            if WMZC._safe_dataframe_check(macd_result):
                print("✅ MACD计算成功")
            else:
                print("⚠️ MACD计算返回空结果")
        except Exception as e:
            print(f"❌ MACD计算失败: {e}")
        
        # 测试KDJ计算
        try:
            kdj_result = WMZC.calculate_kdj(test_data)
            if WMZC._safe_dataframe_check(kdj_result):
                print("✅ KDJ计算成功")
            else:
                print("⚠️ KDJ计算返回空结果")
        except Exception as e:
            print(f"❌ KDJ计算失败: {e}")
        
        # 测试RSI计算
        try:
            rsi_result = WMZC.calculate_rsi(test_data)
            if WMZC._safe_dataframe_check(rsi_result):
                print("✅ RSI计算成功")
            else:
                print("⚠️ RSI计算返回空结果")
        except Exception as e:
            print(f"❌ RSI计算失败: {e}")
        
        print("8. 测试完成，清理资源...")
        loop.call_soon_threadsafe(loop.stop)
        loop_thread.join(timeout=2)
        print("✅ 技术修复测试完成")
        
        return True
            
    except Exception as e:
        print(f"❌ 技术修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_technical_fixes()
    if success:
        print("\n🎉 所有技术修复测试通过！")
    else:
        print("\n❌ 技术修复测试失败，需要进一步调试")
