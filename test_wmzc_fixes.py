#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试WMZC.py修复效果
"""

import asyncio
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_wmzc_fixes():
    """测试WMZC修复效果"""
    print("🧪 开始测试WMZC修复效果...")
    
    try:
        # 导入WMZC模块
        print("📦 导入WMZC模块...")
        import WMZC
        print("✅ WMZC模块导入成功")
        
        # 测试数据一致性管理器
        print("\n🔍 测试数据一致性管理器...")
        if hasattr(WMZC, 'data_consistency_manager'):
            dcm = WMZC.data_consistency_manager
            print(f"✅ 数据一致性管理器存在: {type(dcm)}")
            
            # 测试延迟启动功能
            if hasattr(dcm, 'start_delayed_monitoring_if_needed'):
                try:
                    await dcm.start_delayed_monitoring_if_needed()
                    print("✅ 数据一致性监控启动测试通过")
                except Exception as e:
                    print(f"⚠️ 数据一致性监控启动测试失败: {e}")
            else:
                print("⚠️ 数据一致性管理器缺少延迟启动方法")
        else:
            print("❌ 数据一致性管理器不存在")
        
        # 测试日志消费者
        print("\n🔍 测试日志消费者...")
        if hasattr(WMZC, 'start_log_consumer'):
            try:
                await WMZC.start_log_consumer()
                print("✅ 日志消费者启动测试通过")
            except Exception as e:
                print(f"⚠️ 日志消费者启动测试失败: {e}")
        else:
            print("❌ 日志消费者函数不存在")
        
        # 测试优化模块导入状态
        print("\n🔍 测试优化模块导入状态...")
        
        # 检查智能限频管理器
        if hasattr(WMZC, 'RATE_LIMITER_AVAILABLE'):
            print(f"✅ 智能限频管理器: {'可用' if WMZC.RATE_LIMITER_AVAILABLE else '不可用'}")
        
        # 检查订单簿管理器
        if hasattr(WMZC, 'ORDER_BOOK_MANAGER_AVAILABLE'):
            print(f"✅ 订单簿管理器: {'可用' if WMZC.ORDER_BOOK_MANAGER_AVAILABLE else '不可用'}")
        
        # 检查批量订单管理器
        if hasattr(WMZC, 'BATCH_ORDER_MANAGER_AVAILABLE'):
            print(f"✅ 批量订单管理器: {'可用' if WMZC.BATCH_ORDER_MANAGER_AVAILABLE else '不可用'}")
        
        # 检查智能重试处理器
        if hasattr(WMZC, 'SMART_RETRY_HANDLER_AVAILABLE'):
            print(f"✅ 智能重试处理器: {'可用' if WMZC.SMART_RETRY_HANDLER_AVAILABLE else '不可用'}")
        
        print("\n🎉 WMZC修复测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ WMZC修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 WMZC系统修复验证测试")
    print("=" * 60)
    
    # 测试WMZC修复
    wmzc_ok = await test_wmzc_fixes()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"WMZC修复: {'✅ 通过' if wmzc_ok else '❌ 失败'}")
    
    if wmzc_ok:
        print("🎉 修复测试通过！")
    else:
        print("⚠️ 修复测试失败，需要进一步检查")
    
    print("=" * 60)

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
