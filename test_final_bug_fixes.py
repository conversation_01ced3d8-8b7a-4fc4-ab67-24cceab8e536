#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC系统最终BUG修复验证
验证DataFrame歧义性错误、预估强平价监控错误等问题的修复效果
"""

import sys
import os
import traceback
import asyncio
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dataframe_ambiguity_fixes():
    """测试DataFrame歧义性错误修复"""
    print("🔍 测试DataFrame歧义性错误修复...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # 创建测试DataFrame
        test_data = {
            'close': [100, 101, 102, 103, 104],
            'high': [101, 102, 103, 104, 105],
            'low': [99, 100, 101, 102, 103],
            'volume': [1000, 1100, 1200, 1300, 1400]
        }
        df = pd.DataFrame(test_data)
        
        print(f"✅ 测试DataFrame创建成功，包含 {len(df)} 行数据")
        
        # 测试_safe_dataframe_check函数
        try:
            import WMZC
            if hasattr(WMZC, '_safe_dataframe_check'):
                result = WMZC._safe_dataframe_check(df)
                print(f"✅ _safe_dataframe_check函数测试通过: {result}")
            else:
                print("❌ _safe_dataframe_check函数不存在")
                return False
        except Exception as e:
            print(f"❌ _safe_dataframe_check函数测试失败: {e}")
            return False
        
        # 测试技术指标计算函数
        try:
            if hasattr(WMZC, 'calculate_macd'):
                macd_result = WMZC.calculate_macd(df)
                if WMZC._safe_dataframe_check(macd_result):
                    print("✅ MACD计算测试通过")
                else:
                    print("⚠️ MACD计算返回空结果")
            
            if hasattr(WMZC, 'calculate_rsi'):
                rsi_result = WMZC.calculate_rsi(df)
                if WMZC._safe_dataframe_check(rsi_result):
                    print("✅ RSI计算测试通过")
                else:
                    print("⚠️ RSI计算返回空结果")
            
            if hasattr(WMZC, 'calculate_kdj'):
                kdj_result = WMZC.calculate_kdj(df)
                if WMZC._safe_dataframe_check(kdj_result):
                    print("✅ KDJ计算测试通过")
                else:
                    print("⚠️ KDJ计算返回空结果")
                    
        except Exception as e:
            print(f"❌ 技术指标计算测试失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ DataFrame歧义性错误修复测试失败: {e}")
        traceback.print_exc()
        return False

def test_liquidation_monitoring_fixes():
    """测试预估强平价监控修复"""
    print("\n🔍 测试预估强平价监控修复...")
    
    try:
        import WMZC
        
        # 检查TradingApp类是否存在
        if hasattr(WMZC, 'TradingApp'):
            trading_app_class = WMZC.TradingApp
            print("✅ TradingApp类存在")
            
            # 检查预估强平价监控相关方法
            methods_to_check = [
                'start_liquidation_price_monitoring',
                'stop_liquidation_price_monitoring',
                'check_liquidation_price_trigger'
            ]
            
            missing_methods = []
            for method in methods_to_check:
                if not hasattr(trading_app_class, method):
                    missing_methods.append(method)
            
            if missing_methods:
                print(f"❌ 缺少预估强平价监控方法: {missing_methods}")
                return False
            else:
                print("✅ 所有预估强平价监控方法都存在")
            
            # 检查方法中是否还有self.root.after的调用
            import inspect
            for method_name in methods_to_check:
                try:
                    method = getattr(trading_app_class, method_name)
                    source = inspect.getsource(method)
                    
                    if 'self.root.after' in source:
                        print(f"❌ 方法 {method_name} 仍包含 self.root.after 调用")
                        return False
                    elif 'self.after' in source:
                        print(f"✅ 方法 {method_name} 已修复为使用 self.after")
                    
                except Exception as e:
                    print(f"⚠️ 无法检查方法 {method_name}: {e}")
            
            return True
        else:
            print("❌ TradingApp类不存在")
            return False
            
    except Exception as e:
        print(f"❌ 预估强平价监控修复测试失败: {e}")
        traceback.print_exc()
        return False

def test_okx_client_initialization():
    """测试OKX客户端初始化问题"""
    print("\n🔍 测试OKX客户端初始化...")
    
    try:
        import WMZC
        
        # 检查统一交易所管理器
        if hasattr(WMZC, 'unified_exchange'):
            print("✅ 统一交易所管理器存在")
            
            # 检查是否有OKX相关的类或函数
            okx_related = [
                'OKXClient', 'OKXExchange', 'okx_client', 
                'UnifiedExchangeManager'
            ]
            
            found_okx = []
            for item in okx_related:
                if hasattr(WMZC, item):
                    found_okx.append(item)
            
            if found_okx:
                print(f"✅ 找到OKX相关组件: {found_okx}")
            else:
                print("⚠️ 未找到OKX相关组件，可能需要配置")
            
            return True
        else:
            print("❌ 统一交易所管理器不存在")
            return False
            
    except Exception as e:
        print(f"❌ OKX客户端初始化测试失败: {e}")
        traceback.print_exc()
        return False

def test_async_task_safety():
    """测试异步任务安全性"""
    print("\n🔍 测试异步任务安全性...")
    
    try:
        import asyncio
        
        # 测试事件循环检查
        try:
            loop = asyncio.get_running_loop()
            print("✅ 当前有运行中的事件循环")
            
            # 测试异步任务创建
            async def test_task():
                await asyncio.sleep(0.1)
                return "success"
            
            task = asyncio.create_task(test_task())
            result = await task
            
            if result == "success":
                print("✅ 异步任务创建和执行正常")
                return True
            else:
                print("❌ 异步任务执行异常")
                return False
                
        except RuntimeError:
            print("⚠️ 当前没有运行中的事件循环")
            # 这在测试环境中是正常的
            return True
            
    except Exception as e:
        print(f"❌ 异步任务安全性测试失败: {e}")
        traceback.print_exc()
        return False

def test_config_warnings():
    """测试配置警告修复"""
    print("\n🔍 测试配置警告修复...")
    
    try:
        import WMZC
        
        # 检查ConfigValidator类
        if hasattr(WMZC, 'ConfigValidator'):
            validator_class = WMZC.ConfigValidator
            
            if hasattr(validator_class, 'CONFIG_SCHEMA'):
                schema = validator_class.CONFIG_SCHEMA
                
                # 检查新增的配置字段
                required_fields = [
                    'ENABLE_RSI', 'ENABLE_LOGGING', 'LOG_TO_CONSOLE',
                    'KDJ_PERIOD', 'MACD_FAST', 'MACD_SLOW', 'MACD_SIGNAL',
                    'STOP_LOSS', 'TAKE_PROFIT', 'api_key'
                ]
                
                missing_fields = []
                for field in required_fields:
                    if field not in schema:
                        missing_fields.append(field)
                
                if not missing_fields:
                    print("✅ 所有必要配置字段都已添加到CONFIG_SCHEMA")
                    return True
                else:
                    print(f"❌ 仍缺少配置字段: {missing_fields}")
                    return False
            else:
                print("❌ CONFIG_SCHEMA不存在")
                return False
        else:
            print("❌ ConfigValidator类不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置警告修复测试失败: {e}")
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("=" * 80)
    print("🔧 WMZC系统最终BUG修复验证")
    print("=" * 80)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行所有测试
    tests = [
        ("DataFrame歧义性错误修复", test_dataframe_ambiguity_fixes),
        ("预估强平价监控修复", test_liquidation_monitoring_fixes),
        ("OKX客户端初始化", test_okx_client_initialization),
        ("异步任务安全性", test_async_task_safety),
        ("配置警告修复", test_config_warnings),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    print("\n" + "=" * 80)
    print("📊 最终修复验证结果")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有BUG修复验证通过！")
        print("\n📋 修复总结:")
        print("✅ DataFrame歧义性错误 - 使用_safe_dataframe_check函数")
        print("✅ 预估强平价监控错误 - 修复self.root.after调用")
        print("✅ OKX客户端初始化 - 统一交易所管理器正常")
        print("✅ 异步任务安全性 - 事件循环检查正常")
        print("✅ 配置警告 - 所有必要字段已添加")
        print("\n🎯 预期效果：WMZC系统应该能够无错误地正常运行")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
