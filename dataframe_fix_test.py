#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DataFrame修复验证测试
验证P0级别DataFrame布尔值歧义错误修复效果
"""

import pandas as pd
import numpy as np

def test_dataframe_fixes():
    """测试DataFrame修复"""
    print("🔍 测试DataFrame修复...")
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'close': [100 + i for i in range(50)],
            'high': [105 + i for i in range(50)],
            'low': [95 + i for i in range(50)],
            'open': [98 + i for i in range(50)],
            'volume': [1000 + i * 10 for i in range(50)]
        })
        
        print(f"✅ 测试数据创建成功: {len(test_data)}行")
        
        # 测试_safe_dataframe_check函数
        try:
            # 导入WMZC模块
            print("🔍 导入WMZC模块...")
            import WMZC
            print("✅ WMZC模块导入成功")
            
            # 测试_safe_dataframe_check函数
            if hasattr(WMZC, '_safe_dataframe_check'):
                result = WMZC._safe_dataframe_check(test_data)
                print(f"✅ _safe_dataframe_check测试: {result}")
                
                # 测试空DataFrame
                empty_df = pd.DataFrame()
                result_empty = WMZC._safe_dataframe_check(empty_df)
                print(f"✅ 空DataFrame测试: {result_empty}")
                
            else:
                print("❌ _safe_dataframe_check函数不存在")
                
        except Exception as e:
            print(f"❌ WMZC导入或测试失败: {e}")
            return False
        
        # 测试技术指标计算
        try:
            print("🔍 测试技术指标计算...")
            
            # 测试MACD
            macd_result = WMZC.calculate_macd(test_data.copy())
            if isinstance(macd_result, pd.DataFrame) and len(macd_result) > 0:
                print(f"✅ MACD计算成功: {len(macd_result)}行")
            else:
                print("⚠️ MACD计算返回空结果")
            
            # 测试RSI
            rsi_result = WMZC.calculate_rsi(test_data.copy())
            if isinstance(rsi_result, pd.DataFrame) and len(rsi_result) > 0:
                print(f"✅ RSI计算成功: {len(rsi_result)}行")
            else:
                print("⚠️ RSI计算返回空结果")
            
            # 测试KDJ
            kdj_result = WMZC.calculate_kdj(test_data.copy())
            if isinstance(kdj_result, pd.DataFrame) and len(kdj_result) > 0:
                print(f"✅ KDJ计算成功: {len(kdj_result)}行")
            else:
                print("⚠️ KDJ计算返回空结果")
                
        except Exception as e:
            print(f"❌ 技术指标计算失败: {e}")
            return False
        
        print("🎉 所有测试通过！DataFrame修复成功")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_dataframe_fixes()
    if success:
        print("\n✅ DataFrame修复验证成功！")
        print("💡 现在可以尝试运行交易系统了")
    else:
        print("\n❌ DataFrame修复验证失败")
    
    print("\n按回车键退出...")
    input()
