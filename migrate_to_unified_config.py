#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 迁移到统一配置系统
将用户目录的分散配置迁移到统一配置文件
"""

import os
import json
import shutil
from datetime import datetime

def migrate_to_unified_config():
    """迁移到统一配置系统"""
    print("🔄 迁移到WMZC统一配置系统")
    print("=" * 60)
    
    user_config_dir = os.path.expanduser("~/.wmzc_trading")
    current_dir = os.getcwd()
    
    # 1. 备份用户目录配置
    print("1. 备份用户目录配置...")
    backup_dir = f"user_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if os.path.exists(user_config_dir):
        try:
            shutil.copytree(user_config_dir, backup_dir)
            print(f"  ✅ 已备份到: {backup_dir}")
        except Exception as e:
            print(f"  ❌ 备份失败: {e}")
            return False
    
    # 2. 读取用户目录的配置
    print("2. 读取用户目录配置...")
    user_trading_config = {}
    user_wmzc_config = {}
    
    trading_config_path = os.path.join(user_config_dir, 'trading_config.json')
    wmzc_config_path = os.path.join(user_config_dir, 'wmzc_config.json')
    
    if os.path.exists(trading_config_path):
        try:
            with open(trading_config_path, 'r', encoding='utf-8') as f:
                user_trading_config = json.load(f)
            print(f"  ✅ 读取trading_config.json: {len(user_trading_config)} 个字段")
        except Exception as e:
            print(f"  ❌ 读取trading_config.json失败: {e}")
    
    if os.path.exists(wmzc_config_path):
        try:
            with open(wmzc_config_path, 'r', encoding='utf-8') as f:
                user_wmzc_config = json.load(f)
            print(f"  ✅ 读取wmzc_config.json: {len(user_wmzc_config)} 个字段")
        except Exception as e:
            print(f"  ❌ 读取wmzc_config.json失败: {e}")
    
    # 3. 更新统一配置文件
    print("3. 更新统一配置文件...")
    unified_config_path = os.path.join(current_dir, 'wmzc_unified_config.json')
    
    if os.path.exists(unified_config_path):
        try:
            with open(unified_config_path, 'r', encoding='utf-8') as f:
                unified_config = json.load(f)
        except Exception as e:
            print(f"  ❌ 读取统一配置失败: {e}")
            return False
    else:
        print("  ❌ 统一配置文件不存在")
        return False
    
    # 4. 合并配置
    print("4. 合并配置...")
    
    # 从用户配置中提取API密钥
    api_key = user_trading_config.get('API_KEY', user_trading_config.get('OKX_API_KEY', ''))
    api_secret = user_trading_config.get('API_SECRET', user_trading_config.get('OKX_SECRET_KEY', ''))
    passphrase = user_trading_config.get('PASSPHRASE', user_trading_config.get('OKX_PASSPHRASE', ''))
    
    # 如果用户配置中有真实API密钥，更新统一配置
    if api_key and api_key != 'placeholder_api_key_min_10_chars':
        unified_config['api']['okx']['api_key'] = api_key
        print(f"  ✅ 更新API密钥")
    
    if api_secret and api_secret != 'placeholder_api_secret_min_10_chars':
        unified_config['api']['okx']['api_secret'] = api_secret
        print(f"  ✅ 更新API密钥密码")
    
    if passphrase and passphrase != 'placeholder_passphrase':
        unified_config['api']['okx']['passphrase'] = passphrase
        print(f"  ✅ 更新API口令")
    
    # 更新其他重要配置
    if 'SYMBOL' in user_trading_config:
        unified_config['trading']['symbol'] = user_trading_config['SYMBOL']
        print(f"  ✅ 更新交易对: {user_trading_config['SYMBOL']}")
    
    if 'TIMEFRAME' in user_trading_config:
        unified_config['trading']['timeframe'] = user_trading_config['TIMEFRAME']
        print(f"  ✅ 更新时间周期: {user_trading_config['TIMEFRAME']}")
    
    if 'ORDER_USDT_AMOUNT' in user_trading_config:
        unified_config['trading']['order_amount'] = user_trading_config['ORDER_USDT_AMOUNT']
        print(f"  ✅ 更新下单金额: {user_trading_config['ORDER_USDT_AMOUNT']}")
    
    if 'LEVERAGE' in user_trading_config:
        unified_config['trading']['leverage'] = user_trading_config['LEVERAGE']
        print(f"  ✅ 更新杠杆倍数: {user_trading_config['LEVERAGE']}")
    
    # 更新策略配置
    strategy_fields = ['ENABLE_KDJ', 'ENABLE_MACD', 'ENABLE_RSI', 'ENABLE_PINBAR']
    for field in strategy_fields:
        if field in user_trading_config:
            strategy_key = field.lower().replace('enable_', '')
            unified_config['strategies'][f'enable_{strategy_key}'] = user_trading_config[field]
            print(f"  ✅ 更新策略: {field} = {user_trading_config[field]}")
    
    # 更新元数据
    unified_config['_metadata']['last_updated'] = datetime.now().isoformat()
    unified_config['_metadata']['migrated_from_user_config'] = True
    unified_config['_metadata']['migration_time'] = datetime.now().isoformat()
    
    # 5. 保存更新后的统一配置
    print("5. 保存更新后的统一配置...")
    try:
        with open(unified_config_path, 'w', encoding='utf-8') as f:
            json.dump(unified_config, f, indent=2, ensure_ascii=False)
        print(f"  ✅ 统一配置已更新")
    except Exception as e:
        print(f"  ❌ 保存统一配置失败: {e}")
        return False
    
    # 6. 清空用户目录配置（可选）
    print("6. 清空用户目录配置...")
    choice = input("是否清空用户目录的配置文件？(y/N): ").strip().lower()
    
    if choice == 'y':
        config_files = ['trading_config.json', 'wmzc_config.json', 'user_settings.json', 
                       'misc_optimization_config.json', 'ai_config.json']
        
        for config_file in config_files:
            file_path = os.path.join(user_config_dir, config_file)
            if os.path.exists(file_path):
                try:
                    # 创建空配置文件
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump({}, f)
                    print(f"  ✅ 已清空: {config_file}")
                except Exception as e:
                    print(f"  ❌ 清空 {config_file} 失败: {e}")
    
    print("\n🎉 迁移完成！")
    print("💡 现在系统将使用统一配置文件")
    print("💡 配置警告应该消失")
    print("💡 重新启动WMZC系统以使用新配置")
    
    return True

def main():
    """主函数"""
    try:
        success = migrate_to_unified_config()
        
        if success:
            print("\n✅ 迁移成功完成")
            print("🚀 建议下一步操作:")
            print("  1. 重新启动WMZC系统")
            print("  2. 验证配置是否正确加载")
            print("  3. 检查配置警告是否消失")
        else:
            print("\n❌ 迁移失败")
            print("💡 请检查错误信息并重试")
    
    except KeyboardInterrupt:
        print("\n🛑 迁移被中断")
    except Exception as e:
        print(f"\n❌ 迁移异常: {e}")

if __name__ == "__main__":
    main()
