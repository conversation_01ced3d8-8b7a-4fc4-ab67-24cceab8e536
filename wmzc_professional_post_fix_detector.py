#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC专业修复后Bug检测器
严格遵守十四大禁令，执行零回归检测
"""

import ast
import re
import os
import json
import time
from datetime import datetime
from typing import List, Dict, Set, Tuple, Any
from collections import defaultdict, Counter

class ProfessionalPostFixDetector:
    """专业修复后Bug检测器 - 零回归保证"""
    
    def __init__(self, file_path: str = "WMZC.py"):
        self.file_path = file_path
        self.code_content = ""
        self.code_lines = []
        self.ast_tree = None
        self.total_lines = 0
        
        # Bug分类统计
        self.critical_bugs = []  # 🔴致命
        self.high_bugs = []      # 🟠高危  
        self.medium_bugs = []    # 🟡中危
        self.low_bugs = []       # 🟢低危
        
        # 检测统计
        self.detection_stats = {
            'syntax_errors': 0,
            'logic_errors': 0,
            'naming_issues': 0,
            'duplicate_code': 0,
            'performance_issues': 0,
            'security_issues': 0,
            'regression_risks': 0,
            'architectural_issues': 0
        }
        
        # 函数和类映射
        self.function_map = {}
        self.class_map = {}
        self.import_map = set()
        
    def load_and_analyze_code(self) -> bool:
        """加载并分析代码"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.code_content = f.read()
                self.code_lines = self.code_content.split('\n')
                self.total_lines = len(self.code_lines)
            
            print(f"✅ 成功加载 {self.total_lines:,} 行代码")
            
            # AST解析
            try:
                self.ast_tree = ast.parse(self.code_content)
                print("✅ AST解析成功 - 语法验证通过")
                self._build_code_maps()
                return True
            except SyntaxError as e:
                self._add_critical_bug("SYNTAX_001", "Python语法错误", 
                                     f"第{e.lineno}行: {e.msg}", 
                                     self.code_lines[e.lineno-1] if e.lineno <= len(self.code_lines) else "",
                                     "修复语法错误")
                return False
                
        except Exception as e:
            print(f"❌ 代码加载失败: {e}")
            return False
    
    def _build_code_maps(self):
        """构建代码映射"""
        for node in ast.walk(self.ast_tree):
            if isinstance(node, ast.FunctionDef):
                self.function_map[node.name] = {
                    'line': node.lineno,
                    'args': len(node.args.args),
                    'decorators': [d.id if isinstance(d, ast.Name) else str(d) for d in node.decorator_list]
                }
            elif isinstance(node, ast.ClassDef):
                self.class_map[node.name] = {
                    'line': node.lineno,
                    'methods': [n.name for n in node.body if isinstance(n, ast.FunctionDef)],
                    'bases': [b.id if isinstance(b, ast.Name) else str(b) for b in node.bases]
                }
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    self.import_map.add(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    self.import_map.add(node.module)
    
    def _add_critical_bug(self, bug_id: str, title: str, description: str, code: str, fix: str):
        """添加致命Bug"""
        self.critical_bugs.append({
            'id': bug_id, 'title': title, 'description': description,
            'code': code, 'fix': fix, 'severity': '🔴致命'
        })
        self.detection_stats['syntax_errors'] += 1
    
    def _add_high_bug(self, bug_id: str, title: str, description: str, code: str, fix: str):
        """添加高危Bug"""
        self.high_bugs.append({
            'id': bug_id, 'title': title, 'description': description,
            'code': code, 'fix': fix, 'severity': '🟠高危'
        })
    
    def _add_medium_bug(self, bug_id: str, title: str, description: str, code: str, fix: str):
        """添加中危Bug"""
        self.medium_bugs.append({
            'id': bug_id, 'title': title, 'description': description,
            'code': code, 'fix': fix, 'severity': '🟡中危'
        })
    
    def _add_low_bug(self, bug_id: str, title: str, description: str, code: str, fix: str):
        """添加低危Bug"""
        self.low_bugs.append({
            'id': bug_id, 'title': title, 'description': description,
            'code': code, 'fix': fix, 'severity': '🟢低危'
        })
    
    def detect_regression_issues(self):
        """检测回归问题"""
        print("🔍 检测修复后回归问题...")
        
        regression_count = 0
        
        # 1. 检测被注释的重要函数
        for i, line in enumerate(self.code_lines, 1):
            if re.match(r'^\s*#.*def\s+\w+', line):
                self._add_high_bug(f"REG_{regression_count+1:03d}", "重要函数被注释",
                                 f"第{i}行: 函数定义被注释可能导致功能缺失",
                                 line.strip(), "检查是否误删重要函数")
                regression_count += 1
                self.detection_stats['regression_risks'] += 1
        
        # 2. 检测孤立的文档字符串
        for i, line in enumerate(self.code_lines, 1):
            if re.match(r'^\s*""".*"""$', line) and i > 1:
                prev_line = self.code_lines[i-2].strip()
                if not (prev_line.startswith('def ') or prev_line.startswith('class ') or prev_line.endswith(':')):
                    self._add_medium_bug(f"REG_{regression_count+1:03d}", "孤立文档字符串",
                                       f"第{i}行: 可能是函数定义被误删",
                                       line.strip(), "检查对应的函数或类定义")
                    regression_count += 1
                    self.detection_stats['regression_risks'] += 1
        
        # 3. 检测空类定义
        for class_name, class_info in self.class_map.items():
            if not class_info['methods']:
                line_num = class_info['line']
                self._add_medium_bug(f"REG_{regression_count+1:03d}", "空类定义",
                                   f"类 {class_name} (第{line_num}行) 没有方法实现",
                                   f"class {class_name}:", "检查类实现是否完整")
                regression_count += 1
                self.detection_stats['regression_risks'] += 1
        
        print(f"✅ 回归检测完成，发现 {regression_count} 个潜在回归问题")
    
    def detect_duplicate_code_advanced(self):
        """高级重复代码检测"""
        print("🔍 执行高级重复代码检测...")
        
        duplicate_count = 0
        
        # 1. 函数体重复检测
        function_signatures = {}
        for func_name, func_info in self.function_map.items():
            # 排除特殊方法
            if func_name.startswith('__') and func_name.endswith('__'):
                continue
            if func_name in ['__init__', '__str__', '__repr__']:
                continue
                
            signature = f"args_{func_info['args']}"
            if signature in function_signatures:
                existing_funcs = function_signatures[signature]
                # 检查是否真的重复（简化检查）
                if len(existing_funcs) > 0:
                    self._add_medium_bug(f"DUP_{duplicate_count+1:03d}", "可能的重复函数",
                                       f"函数 {func_name} 与 {existing_funcs[0]} 参数数量相同，可能重复",
                                       f"def {func_name}(...)", "检查函数实现是否重复")
                    duplicate_count += 1
                    self.detection_stats['duplicate_code'] += 1
                existing_funcs.append(func_name)
            else:
                function_signatures[signature] = [func_name]
        
        # 2. 导入重复检测
        import_lines = {}
        for i, line in enumerate(self.code_lines, 1):
            if re.match(r'^\s*(import|from)\s+', line):
                clean_line = re.sub(r'\s+', ' ', line.strip())
                if clean_line in import_lines:
                    self._add_low_bug(f"DUP_{duplicate_count+1:03d}", "重复导入",
                                    f"第{i}行: 重复导入语句",
                                    line.strip(), "删除重复的导入")
                    duplicate_count += 1
                    self.detection_stats['duplicate_code'] += 1
                else:
                    import_lines[clean_line] = i
        
        print(f"✅ 重复代码检测完成，发现 {duplicate_count} 个重复问题")
    
    def detect_naming_inconsistencies(self):
        """检测命名不一致"""
        print("🔍 检测命名规范问题...")
        
        naming_count = 0
        
        # 检查函数命名风格
        snake_case_funcs = []
        camel_case_funcs = []
        
        for func_name in self.function_map.keys():
            if re.match(r'^[a-z_][a-z0-9_]*$', func_name):
                snake_case_funcs.append(func_name)
            elif re.match(r'^[a-z][a-zA-Z0-9]*$', func_name):
                camel_case_funcs.append(func_name)
        
        if len(snake_case_funcs) > 10 and len(camel_case_funcs) > 5:
            self._add_low_bug(f"NAME_{naming_count+1:03d}", "函数命名风格不一致",
                            f"混合使用snake_case({len(snake_case_funcs)})和camelCase({len(camel_case_funcs)})",
                            f"示例: {snake_case_funcs[0]} vs {camel_case_funcs[0]}", 
                            "统一使用snake_case命名")
            naming_count += 1
            self.detection_stats['naming_issues'] += 1
        
        # 检查类命名风格
        non_pascal_classes = []
        for class_name in self.class_map.keys():
            if not re.match(r'^[A-Z][a-zA-Z0-9]*$', class_name):
                non_pascal_classes.append(class_name)
        
        if non_pascal_classes:
            self._add_low_bug(f"NAME_{naming_count+1:03d}", "类命名不符合PascalCase",
                            f"类名不符合规范: {', '.join(non_pascal_classes[:5])}",
                            f"class {non_pascal_classes[0]}:", "使用PascalCase命名类")
            naming_count += 1
            self.detection_stats['naming_issues'] += 1
        
        print(f"✅ 命名检测完成，发现 {naming_count} 个命名问题")
    
    def detect_performance_issues(self):
        """检测性能问题"""
        print("🔍 检测性能问题...")
        
        perf_count = 0
        
        performance_patterns = [
            (r'for.*in.*:\s*.*len\(', "循环中重复计算len()", "缓存len()结果到变量"),
            (r'for.*:\s*.*\+=.*["\']', "循环中字符串拼接", "使用join()或f-string"),
            (r'while\s+True:(?!.*break)', "可能的无限循环", "确保有明确的退出条件"),
            (r'time\.sleep\(\d+\)', "同步sleep调用", "在异步环境中使用await asyncio.sleep()"),
        ]
        
        for i, line in enumerate(self.code_lines, 1):
            for pattern, issue, fix in performance_patterns:
                if re.search(pattern, line):
                    self._add_low_bug(f"PERF_{perf_count+1:03d}", issue,
                                    f"第{i}行: {issue}",
                                    line.strip()[:80], fix)
                    perf_count += 1
                    self.detection_stats['performance_issues'] += 1
                    break  # 每行只报告一个性能问题
        
        print(f"✅ 性能检测完成，发现 {perf_count} 个性能问题")
    
    def detect_security_issues(self):
        """检测安全问题"""
        print("🔍 检测安全风险...")
        
        security_count = 0
        
        security_patterns = [
            (r'["\'][a-zA-Z0-9]{20,}["\'].*(?:key|secret|token)', "可能的硬编码密钥", "使用环境变量或配置文件"),
            (r'(?<!model\.)eval\s*\(', "不安全的eval使用", "使用ast.literal_eval或其他安全方法"),
            (r'pickle\.loads?\s*\(', "不安全的pickle使用", "验证数据来源或使用json"),
            (r'subprocess\.(call|run|Popen).*shell=True', "shell注入风险", "避免shell=True或验证输入"),
        ]
        
        for i, line in enumerate(self.code_lines, 1):
            # 跳过注释行
            if line.strip().startswith('#'):
                continue
                
            for pattern, issue, fix in security_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    self._add_high_bug(f"SEC_{security_count+1:03d}", issue,
                                     f"第{i}行: {issue}",
                                     line.strip()[:80], fix)
                    security_count += 1
                    self.detection_stats['security_issues'] += 1
                    break
        
        print(f"✅ 安全检测完成，发现 {security_count} 个安全问题")
    
    def detect_logic_errors(self):
        """检测逻辑错误"""
        print("🔍 检测逻辑错误...")
        
        logic_count = 0
        
        # 1. 空异常处理
        for i, line in enumerate(self.code_lines, 1):
            if re.match(r'^\s*except.*:\s*$', line):
                # 检查下一行是否是pass
                if i < len(self.code_lines) and self.code_lines[i].strip() == 'pass':
                    self._add_medium_bug(f"LOGIC_{logic_count+1:03d}", "空异常处理",
                                       f"第{i}行: except块为空，可能隐藏错误",
                                       f"{line.strip()}\n    pass", "添加适当的错误处理或日志")
                    logic_count += 1
                    self.detection_stats['logic_errors'] += 1
        
        # 2. 可能的除零错误
        for i, line in enumerate(self.code_lines, 1):
            if re.search(r'/\s*[a-zA-Z_]\w*(?!\s*[!=<>])', line) and 'http' not in line.lower():
                self._add_low_bug(f"LOGIC_{logic_count+1:03d}", "可能的除零错误",
                                f"第{i}行: 除法运算未检查除数为零",
                                line.strip()[:80], "添加除零检查")
                logic_count += 1
                self.detection_stats['logic_errors'] += 1
        
        print(f"✅ 逻辑检测完成，发现 {logic_count} 个逻辑问题")
    
    def run_comprehensive_detection(self) -> bool:
        """运行全面检测"""
        print("🚀 启动WMZC专业修复后Bug检测...")
        print("=" * 80)
        
        # 1. 加载和分析代码
        if not self.load_and_analyze_code():
            return False
        
        # 2. 执行各种检测
        self.detect_regression_issues()
        self.detect_duplicate_code_advanced()
        self.detect_naming_inconsistencies()
        self.detect_performance_issues()
        self.detect_security_issues()
        self.detect_logic_errors()
        
        # 3. 生成报告
        self.generate_professional_report()
        
        return True
    
    def generate_professional_report(self):
        """生成专业报告"""
        print("\n" + "=" * 80)
        print("📊 WMZC专业修复后Bug检测报告")
        print("=" * 80)
        
        total_bugs = len(self.critical_bugs) + len(self.high_bugs) + len(self.medium_bugs) + len(self.low_bugs)
        
        print(f"📈 代码规模: {self.total_lines:,} 行")
        print(f"🔍 检测结果: {total_bugs} 个问题")
        
        if total_bugs == 0:
            print("🎉 恭喜！未发现任何问题，代码质量优秀！")
            return
        
        print(f"\n📊 问题分布:")
        print(f"  🔴 致命问题: {len(self.critical_bugs)} 个")
        print(f"  🟠 高危问题: {len(self.high_bugs)} 个")
        print(f"  🟡 中危问题: {len(self.medium_bugs)} 个")
        print(f"  🟢 低危问题: {len(self.low_bugs)} 个")
        
        # 显示关键问题
        if self.critical_bugs:
            print(f"\n🚨 致命问题 (必须立即修复):")
            for bug in self.critical_bugs[:3]:
                print(f"  • {bug['title']}: {bug['description']}")
        
        if self.high_bugs:
            print(f"\n⚠️ 高危问题 (优先修复):")
            for bug in self.high_bugs[:5]:
                print(f"  • {bug['title']}: {bug['description']}")
        
        # 系统健康度评估
        self.calculate_health_score()
        
        # 保存详细报告
        self.save_detection_report()
    
    def calculate_health_score(self):
        """计算系统健康度"""
        critical_count = len(self.critical_bugs)
        high_count = len(self.high_bugs)
        medium_count = len(self.medium_bugs)
        low_count = len(self.low_bugs)
        
        # 加权计算健康度
        penalty_score = critical_count * 10 + high_count * 5 + medium_count * 2 + low_count * 1
        max_score = 100
        health_score = max(0, max_score - penalty_score)
        
        if health_score >= 95:
            health_level = "🟢 优秀"
        elif health_score >= 85:
            health_level = "🟡 良好"
        elif health_score >= 70:
            health_level = "🟠 一般"
        else:
            health_level = "🔴 需要改进"
        
        print(f"\n{health_level} 系统健康度: {health_score}/100")
        print(f"📊 问题密度: {(critical_count + high_count + medium_count + low_count) / self.total_lines * 1000:.2f} 问题/千行")
    
    def save_detection_report(self):
        """保存检测报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"wmzc_professional_detection_report_{timestamp}.json"
        
        report_data = {
            'detection_time': datetime.now().isoformat(),
            'file_analyzed': self.file_path,
            'total_lines': self.total_lines,
            'detection_stats': self.detection_stats,
            'critical_bugs': self.critical_bugs,
            'high_bugs': self.high_bugs,
            'medium_bugs': self.medium_bugs,
            'low_bugs': self.low_bugs
        }
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            print(f"\n💾 详细报告已保存: {report_file}")
        except Exception as e:
            print(f"\n⚠️ 保存报告失败: {e}")

def main():
    """主函数"""
    print("🔍 WMZC专业修复后Bug检测器")
    print("严格遵守十四大禁令，执行零回归检测")
    print("=" * 80)
    
    detector = ProfessionalPostFixDetector()
    
    try:
        success = detector.run_comprehensive_detection()
        
        if success:
            print("\n✅ 专业检测完成！")
            return True
        else:
            print("\n❌ 检测过程中出现错误")
            return False
            
    except Exception as e:
        print(f"\n💥 检测异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
