#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC系统快速验证脚本
"""

import os
import re

def quick_verify_fixes():
    """快速验证P0级别修复效果"""
    print("🔍 快速验证P0级别Bug修复效果")
    print("=" * 50)
    
    # 检查WMZC.py文件
    if not os.path.exists('WMZC.py'):
        print("❌ WMZC.py文件不存在")
        return
    
    with open('WMZC.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 检查重复导入
    print("📋 1. 检查重复导入修复...")
    threading_imports = re.findall(r'import threading', content)
    function_level_imports = re.findall(r'^\s+import threading', content, re.MULTILINE)
    
    print(f"  总threading导入数: {len(threading_imports)}")
    print(f"  函数级导入数: {len(function_level_imports)}")
    
    if len(function_level_imports) == 0:
        print("  ✅ 重复导入已清理")
    else:
        print(f"  ⚠️ 仍有{len(function_level_imports)}个函数级导入需要清理")
    
    # 2. 检查注释代码清理
    print("\n📋 2. 检查注释代码清理...")
    commented_sleep_patterns = [
        r'#.*已移除time\.sleep',
        r'#.*time\.sleep.*await asyncio\.sleep',
        r'#.*🔧.*time\.sleep'
    ]
    
    total_commented_sleep = 0
    for pattern in commented_sleep_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        total_commented_sleep += len(matches)
    
    print(f"  注释的sleep调用数: {total_commented_sleep}")
    
    if total_commented_sleep < 10:
        print("  ✅ 注释代码清理良好")
    else:
        print(f"  ⚠️ 仍有{total_commented_sleep}个注释的sleep调用")
    
    # 3. 检查DataFrame安全性
    print("\n📋 3. 检查DataFrame安全性...")
    risky_patterns = [
        r'if\s+df\s*:',
        r'if\s+.*dataframe\s*:',
        r'if\s+not\s+df\s*:',
    ]
    
    safe_patterns = [
        r'if\s+df\s+is\s+None',
        r'if\s+df\.empty',
        r'if\s+not\s+df\.empty',
        r'if\s+isinstance\(df',
        r'if\s+len\(df\)',
    ]
    
    risky_count = 0
    safe_count = 0
    
    for pattern in risky_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        risky_count += len(matches)
    
    for pattern in safe_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        safe_count += len(matches)
    
    print(f"  风险DataFrame检查: {risky_count}")
    print(f"  安全DataFrame检查: {safe_count}")
    
    if risky_count < 5:
        print("  ✅ DataFrame安全性良好")
    else:
        print(f"  ⚠️ 发现{risky_count}个潜在风险")
    
    # 4. 语法检查
    print("\n📋 4. 语法正确性检查...")
    try:
        import ast
        ast.parse(content)
        print("  ✅ 语法正确")
    except SyntaxError as e:
        print(f"  ❌ 语法错误: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 验证总结")
    print("=" * 50)
    
    import_score = 100 if len(function_level_imports) == 0 else max(0, 100 - len(function_level_imports) * 10)
    comment_score = 100 if total_commented_sleep < 10 else max(0, 100 - total_commented_sleep * 2)
    df_score = 100 if risky_count < 5 else max(0, 100 - risky_count * 5)
    
    overall_score = (import_score + comment_score + df_score) / 3
    
    print(f"重复导入修复: {import_score}%")
    print(f"注释代码清理: {comment_score}%")
    print(f"DataFrame安全性: {df_score}%")
    print(f"总体评分: {overall_score:.1f}%")
    
    if overall_score >= 90:
        print("✅ 优秀！P0级别bug修复效果很好")
        return True
    elif overall_score >= 70:
        print("⚠️ 良好，但仍有改进空间")
        return False
    else:
        print("❌ 需要进一步修复")
        return False

if __name__ == "__main__":
    quick_verify_fixes()
