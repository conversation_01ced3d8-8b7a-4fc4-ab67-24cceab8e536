#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化模块配置参数定义 - 为杂项配置标签页提供参数结构
包含4个优化模块的所有可配置参数及其默认值、范围、描述等信息
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Tuple, Optional
from enum import Enum


class ParameterType(Enum):
    """参数类型枚举"""
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    STRING = "string"
    CHOICE = "choice"


@dataclass
class ConfigParameter:
    """配置参数定义"""
    name: str                           # 参数名称
    display_name: str                   # 显示名称
    param_type: ParameterType          # 参数类型
    default_value: Any                 # 默认值
    min_value: Optional[Any] = None    # 最小值
    max_value: Optional[Any] = None    # 最大值
    choices: Optional[List[Any]] = None # 选择项（用于下拉菜单）
    description: str = ""              # 参数描述
    tooltip: str = ""                  # 工具提示
    unit: str = ""                     # 单位
    category: str = ""                 # 分类
    requires_restart: bool = False     # 是否需要重启生效
    advanced: bool = False             # 是否为高级参数


class OptimizationConfigParameters:
    """优化模块配置参数集合"""
    
    def __init__(self):
        self.parameters = self._initialize_parameters()
        self.categories = self._get_categories()
        self.presets = self._initialize_presets()
    
    def _initialize_parameters(self) -> Dict[str, ConfigParameter]:
        """初始化所有配置参数"""
        params = {}
        
        # ==================== 智能限频管理器参数 ====================
        
        # OKX限频配置
        params['okx_requests_per_second'] = ConfigParameter(
            name='okx_requests_per_second',
            display_name='OKX每秒请求数',
            param_type=ParameterType.INTEGER,
            default_value=20,
            min_value=1,
            max_value=50,
            description='OKX交易所每秒最大API请求数',
            tooltip='设置过高可能触发限频，设置过低会影响性能',
            unit='req/s',
            category='智能限频管理器'
        )
        
        params['okx_weight_limit'] = ConfigParameter(
            name='okx_weight_limit',
            display_name='OKX权重限制',
            param_type=ParameterType.INTEGER,
            default_value=100,
            min_value=10,
            max_value=200,
            description='OKX API调用权重限制',
            tooltip='不同API调用有不同权重，总权重不能超过此限制',
            unit='权重',
            category='智能限频管理器'
        )
        
        params['okx_burst_limit'] = ConfigParameter(
            name='okx_burst_limit',
            display_name='OKX突发限制',
            param_type=ParameterType.INTEGER,
            default_value=40,
            min_value=10,
            max_value=100,
            description='OKX突发请求限制',
            tooltip='短时间内允许的最大请求数',
            unit='请求',
            category='智能限频管理器'
        )
        
        # Gate.io限频配置
        params['gate_requests_per_second'] = ConfigParameter(
            name='gate_requests_per_second',
            display_name='Gate.io每秒请求数',
            param_type=ParameterType.INTEGER,
            default_value=10,
            min_value=1,
            max_value=30,
            description='Gate.io交易所每秒最大API请求数',
            tooltip='Gate.io限频相对严格，建议保守设置',
            unit='req/s',
            category='智能限频管理器'
        )
        
        params['gate_weight_limit'] = ConfigParameter(
            name='gate_weight_limit',
            display_name='Gate.io权重限制',
            param_type=ParameterType.INTEGER,
            default_value=50,
            min_value=10,
            max_value=100,
            description='Gate.io API调用权重限制',
            tooltip='Gate.io的权重限制相对较低',
            unit='权重',
            category='智能限频管理器'
        )
        
        params['gate_burst_limit'] = ConfigParameter(
            name='gate_burst_limit',
            display_name='Gate.io突发限制',
            param_type=ParameterType.INTEGER,
            default_value=30,
            min_value=5,
            max_value=60,
            description='Gate.io突发请求限制',
            tooltip='Gate.io突发限制相对保守',
            unit='请求',
            category='智能限频管理器'
        )
        
        # ==================== 订单簿管理器参数 ====================
        
        params['orderbook_okx_max_depth'] = ConfigParameter(
            name='orderbook_okx_max_depth',
            display_name='OKX订单簿最大深度',
            param_type=ParameterType.INTEGER,
            default_value=400,
            min_value=10,
            max_value=1000,
            description='OKX订单簿维护的最大价格档位数',
            tooltip='更大深度提供更多信息但消耗更多内存',
            unit='档位',
            category='订单簿管理器'
        )
        
        params['orderbook_gate_max_depth'] = ConfigParameter(
            name='orderbook_gate_max_depth',
            display_name='Gate.io订单簿最大深度',
            param_type=ParameterType.INTEGER,
            default_value=100,
            min_value=10,
            max_value=500,
            description='Gate.io订单簿维护的最大价格档位数',
            tooltip='Gate.io支持的深度相对较小',
            unit='档位',
            category='订单簿管理器'
        )
        
        params['orderbook_checksum_enabled'] = ConfigParameter(
            name='orderbook_checksum_enabled',
            display_name='启用校验和验证',
            param_type=ParameterType.BOOLEAN,
            default_value=True,
            description='是否启用订单簿数据完整性校验',
            tooltip='启用后会验证数据完整性，但会增加CPU开销',
            category='订单簿管理器'
        )
        
        params['orderbook_sequence_validation'] = ConfigParameter(
            name='orderbook_sequence_validation',
            display_name='启用序列号验证',
            param_type=ParameterType.BOOLEAN,
            default_value=True,
            description='是否启用序列号连续性验证',
            tooltip='确保更新顺序正确，防止数据不一致',
            category='订单簿管理器'
        )
        
        params['orderbook_update_interval'] = ConfigParameter(
            name='orderbook_update_interval',
            display_name='更新间隔',
            param_type=ParameterType.INTEGER,
            default_value=100,
            min_value=10,
            max_value=1000,
            description='订单簿更新间隔',
            tooltip='更小间隔提供更实时数据但增加系统负载',
            unit='毫秒',
            category='订单簿管理器'
        )
        
        params['orderbook_reconnect_delay'] = ConfigParameter(
            name='orderbook_reconnect_delay',
            display_name='重连延迟',
            param_type=ParameterType.FLOAT,
            default_value=5.0,
            min_value=1.0,
            max_value=30.0,
            description='WebSocket重连延迟时间',
            tooltip='连接断开后等待多长时间重连',
            unit='秒',
            category='订单簿管理器'
        )
        
        # ==================== 批量操作接口参数 ====================
        
        params['batch_okx_max_batch_size'] = ConfigParameter(
            name='batch_okx_max_batch_size',
            display_name='OKX最大批次大小',
            param_type=ParameterType.INTEGER,
            default_value=20,
            min_value=1,
            max_value=20,
            description='OKX批量操作单次最大订单数',
            tooltip='OKX官方限制最多20个订单',
            unit='订单',
            category='批量操作接口'
        )
        
        params['batch_gate_max_batch_size'] = ConfigParameter(
            name='batch_gate_max_batch_size',
            display_name='Gate.io最大批次大小',
            param_type=ParameterType.INTEGER,
            default_value=10,
            min_value=1,
            max_value=10,
            description='Gate.io批量操作单次最大订单数',
            tooltip='Gate.io官方限制最多10个订单',
            unit='订单',
            category='批量操作接口'
        )
        
        params['batch_okx_max_concurrent'] = ConfigParameter(
            name='batch_okx_max_concurrent',
            display_name='OKX最大并发批次',
            param_type=ParameterType.INTEGER,
            default_value=3,
            min_value=1,
            max_value=10,
            description='OKX同时执行的最大批次数',
            tooltip='过多并发可能触发限频',
            unit='批次',
            category='批量操作接口'
        )
        
        params['batch_gate_max_concurrent'] = ConfigParameter(
            name='batch_gate_max_concurrent',
            display_name='Gate.io最大并发批次',
            param_type=ParameterType.INTEGER,
            default_value=2,
            min_value=1,
            max_value=5,
            description='Gate.io同时执行的最大批次数',
            tooltip='Gate.io限频较严，建议保守设置',
            unit='批次',
            category='批量操作接口'
        )
        
        params['batch_retry_attempts'] = ConfigParameter(
            name='batch_retry_attempts',
            display_name='批量操作重试次数',
            param_type=ParameterType.INTEGER,
            default_value=3,
            min_value=0,
            max_value=10,
            description='批量操作失败时的重试次数',
            tooltip='0表示不重试，过多重试可能延长执行时间',
            unit='次',
            category='批量操作接口'
        )
        
        params['batch_retry_delay'] = ConfigParameter(
            name='batch_retry_delay',
            display_name='批量操作重试延迟',
            param_type=ParameterType.FLOAT,
            default_value=1.0,
            min_value=0.1,
            max_value=10.0,
            description='批量操作重试间隔时间',
            tooltip='重试前等待的时间',
            unit='秒',
            category='批量操作接口'
        )
        
        # ==================== 智能重试机制参数 ====================
        
        params['retry_network_max_retries'] = ConfigParameter(
            name='retry_network_max_retries',
            display_name='网络错误最大重试次数',
            param_type=ParameterType.INTEGER,
            default_value=5,
            min_value=0,
            max_value=20,
            description='网络连接错误时的最大重试次数',
            tooltip='网络错误通常是临时的，可以多重试几次',
            unit='次',
            category='智能重试机制'
        )
        
        params['retry_network_base_delay'] = ConfigParameter(
            name='retry_network_base_delay',
            display_name='网络错误基础延迟',
            param_type=ParameterType.FLOAT,
            default_value=1.0,
            min_value=0.1,
            max_value=10.0,
            description='网络错误重试的基础延迟时间',
            tooltip='实际延迟会根据退避策略调整',
            unit='秒',
            category='智能重试机制'
        )
        
        params['retry_network_max_delay'] = ConfigParameter(
            name='retry_network_max_delay',
            display_name='网络错误最大延迟',
            param_type=ParameterType.FLOAT,
            default_value=30.0,
            min_value=1.0,
            max_value=300.0,
            description='网络错误重试的最大延迟时间',
            tooltip='防止延迟时间过长',
            unit='秒',
            category='智能重试机制'
        )
        
        params['retry_ratelimit_max_retries'] = ConfigParameter(
            name='retry_ratelimit_max_retries',
            display_name='限频错误最大重试次数',
            param_type=ParameterType.INTEGER,
            default_value=10,
            min_value=0,
            max_value=50,
            description='API限频错误时的最大重试次数',
            tooltip='限频错误需要等待，可以多重试',
            unit='次',
            category='智能重试机制'
        )
        
        params['retry_ratelimit_base_delay'] = ConfigParameter(
            name='retry_ratelimit_base_delay',
            display_name='限频错误基础延迟',
            param_type=ParameterType.FLOAT,
            default_value=2.0,
            min_value=0.5,
            max_value=20.0,
            description='限频错误重试的基础延迟时间',
            tooltip='限频错误需要较长等待时间',
            unit='秒',
            category='智能重试机制'
        )
        
        params['retry_server_max_retries'] = ConfigParameter(
            name='retry_server_max_retries',
            display_name='服务器错误最大重试次数',
            param_type=ParameterType.INTEGER,
            default_value=3,
            min_value=0,
            max_value=10,
            description='服务器错误时的最大重试次数',
            tooltip='服务器错误可能持续较长时间',
            unit='次',
            category='智能重试机制'
        )
        
        params['retry_backoff_strategy'] = ConfigParameter(
            name='retry_backoff_strategy',
            display_name='退避策略',
            param_type=ParameterType.CHOICE,
            default_value='exponential_jitter',
            choices=['fixed', 'linear', 'exponential', 'exponential_jitter', 'fibonacci'],
            description='重试延迟的计算策略',
            tooltip='指数退避+抖动通常效果最好',
            category='智能重试机制'
        )
        
        return params
    
    def _get_categories(self) -> List[str]:
        """获取所有参数分类"""
        categories = set()
        for param in self.parameters.values():
            categories.add(param.category)
        return sorted(list(categories))
    
    def _initialize_presets(self) -> Dict[str, Dict[str, Any]]:
        """初始化预设配置方案"""
        return {
            '保守模式': {
                'description': '适合新手用户，优先稳定性',
                'okx_requests_per_second': 15,
                'okx_weight_limit': 80,
                'okx_burst_limit': 30,
                'gate_requests_per_second': 8,
                'gate_weight_limit': 40,
                'gate_burst_limit': 20,
                'orderbook_okx_max_depth': 200,
                'orderbook_gate_max_depth': 50,
                'batch_okx_max_batch_size': 10,
                'batch_gate_max_batch_size': 5,
                'batch_okx_max_concurrent': 2,
                'batch_gate_max_concurrent': 1,
                'retry_network_max_retries': 3,
                'retry_ratelimit_max_retries': 5,
                'retry_server_max_retries': 2
            },
            '平衡模式': {
                'description': '平衡性能和稳定性，推荐设置',
                'okx_requests_per_second': 20,
                'okx_weight_limit': 100,
                'okx_burst_limit': 40,
                'gate_requests_per_second': 10,
                'gate_weight_limit': 50,
                'gate_burst_limit': 30,
                'orderbook_okx_max_depth': 400,
                'orderbook_gate_max_depth': 100,
                'batch_okx_max_batch_size': 20,
                'batch_gate_max_batch_size': 10,
                'batch_okx_max_concurrent': 3,
                'batch_gate_max_concurrent': 2,
                'retry_network_max_retries': 5,
                'retry_ratelimit_max_retries': 10,
                'retry_server_max_retries': 3
            },
            '激进模式': {
                'description': '追求最高性能，适合专业用户',
                'okx_requests_per_second': 25,
                'okx_weight_limit': 120,
                'okx_burst_limit': 50,
                'gate_requests_per_second': 15,
                'gate_weight_limit': 60,
                'gate_burst_limit': 40,
                'orderbook_okx_max_depth': 600,
                'orderbook_gate_max_depth': 200,
                'batch_okx_max_batch_size': 20,
                'batch_gate_max_batch_size': 10,
                'batch_okx_max_concurrent': 5,
                'batch_gate_max_concurrent': 3,
                'retry_network_max_retries': 8,
                'retry_ratelimit_max_retries': 15,
                'retry_server_max_retries': 5
            }
        }
    
    def get_parameter(self, name: str) -> Optional[ConfigParameter]:
        """获取指定参数"""
        return self.parameters.get(name)
    
    def get_parameters_by_category(self, category: str) -> Dict[str, ConfigParameter]:
        """获取指定分类的所有参数"""
        return {name: param for name, param in self.parameters.items() 
                if param.category == category}
    
    def get_preset_config(self, preset_name: str) -> Optional[Dict[str, Any]]:
        """获取预设配置"""
        return self.presets.get(preset_name)
    
    def validate_parameter_value(self, param_name: str, value: Any) -> Tuple[bool, str]:
        """验证参数值"""
        param = self.get_parameter(param_name)
        if not param:
            return False, f"未知参数: {param_name}"
        
        try:
            # 类型检查
            if param.param_type == ParameterType.INTEGER:
                value = int(value)
            elif param.param_type == ParameterType.FLOAT:
                value = float(value)
            elif param.param_type == ParameterType.BOOLEAN:
                value = bool(value)
            elif param.param_type == ParameterType.STRING:
                value = str(value)
            elif param.param_type == ParameterType.CHOICE:
                if value not in param.choices:
                    return False, f"值必须是以下之一: {param.choices}"
            
            # 范围检查
            if param.min_value is not None and value < param.min_value:
                return False, f"值不能小于 {param.min_value}"
            if param.max_value is not None and value > param.max_value:
                return False, f"值不能大于 {param.max_value}"
            
            return True, ""
            
        except (ValueError, TypeError) as e:
            return False, f"类型错误: {e}"


# 全局实例
optimization_config = OptimizationConfigParameters()


if __name__ == "__main__":
    # 测试代码
    config = OptimizationConfigParameters()
    
    print("📊 优化模块配置参数分析:")
    print(f"总参数数量: {len(config.parameters)}")
    print(f"参数分类: {config.categories}")
    print(f"预设方案: {list(config.presets.keys())}")
    
    print("\n🔧 智能限频管理器参数:")
    rate_limit_params = config.get_parameters_by_category('智能限频管理器')
    for name, param in rate_limit_params.items():
        print(f"  {param.display_name}: {param.default_value} {param.unit}")
    
    print("\n📈 预设配置示例:")
    balanced_config = config.get_preset_config('平衡模式')
    if balanced_config:
        print(f"  平衡模式: {balanced_config['description']}")
        for key, value in list(balanced_config.items())[:5]:
            if key != 'description':
                print(f"    {key}: {value}")
