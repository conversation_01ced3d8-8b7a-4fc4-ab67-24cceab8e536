#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC交易系统完整启动器
集成所有功能的统一启动入口
"""

import asyncio
import sys
import os
import time
import argparse
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append('.')

# 导入WMZC模块
try:
    import WMZC
    print("✅ WMZC模块导入成功")
except ImportError as e:
    print(f"❌ WMZC模块导入失败: {e}")
    sys.exit(1)

class WMZCLauncher:
    """WMZC系统启动器"""
    
    def __init__(self):
        self.api_key = "d5ea5faa068d66204bb68b75201c56d5"
        self.secret_key = "5b516e55788fba27e61f9bd06b22ab3661b3115797076d5e73199bea3a8afb1c"
        self.components = {}
        
    def print_banner(self):
        """打印启动横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                    🚀 WMZC 专业量化交易系统 🚀                                ║
║                                                                              ║
║                        Version 2.0 - Professional Edition                   ║
║                                                                              ║
║  🎯 功能特性:                                                                 ║
║     ✅ Gate.io API 完整集成                                                   ║
║     ✅ 6种专业交易策略                                                         ║
║     ✅ 银行级风险管理                                                          ║
║     ✅ 实时数据流处理                                                          ║
║     ✅ 异步高性能架构                                                          ║
║     ✅ 智能信号综合判断                                                        ║
║                                                                              ║
║  ⚠️  风险提示: 量化交易存在风险，请谨慎操作                                     ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    async def initialize_system(self):
        """初始化系统组件"""
        print("🔧 正在初始化系统组件...")
        
        # 基础组件
        self.components['api_tester'] = WMZC.GateIOAPITester(self.api_key, self.secret_key)
        self.components['indicator_calculator'] = WMZC.TechnicalIndicatorCalculator(self.components['api_tester'])
        
        # 高级组件
        self.components['risk_manager'] = WMZC.AdvancedRiskManager(initial_balance=1000.0)
        self.components['strategy_manager'] = WMZC.AdvancedStrategyManager(
            self.components['indicator_calculator'], 
            self.components['risk_manager']
        )
        self.components['order_manager'] = WMZC.GateIOOrderManager(self.components['api_tester'])
        self.components['websocket_manager'] = WMZC.GateIOWebSocketManager(self.api_key, self.secret_key)
        
        print("✅ 系统组件初始化完成")
    
    async def run_system_check(self):
        """运行系统检查"""
        print("\n🔍 运行系统自检...")
        
        # API连接检查
        api_result = await self.components['api_tester'].run_comprehensive_test()
        if api_result.get('overall_success'):
            print("✅ API连接检查通过")
        else:
            print("❌ API连接检查失败")
            return False
        
        # 数据获取检查
        await self.components['indicator_calculator'].fetch_kline_data("BTC_USDT", "1m", 50)
        if self.components['indicator_calculator'].kline_data:
            print("✅ 市场数据获取正常")
        else:
            print("❌ 市场数据获取失败")
            return False
        
        # 策略系统检查
        strategy_result = await self.components['strategy_manager'].run_all_strategies()
        if strategy_result.get('final_signal'):
            print("✅ 策略系统运行正常")
        else:
            print("❌ 策略系统运行异常")
            return False
        
        print("🎉 系统自检完成，所有组件正常")
        return True
    
    async def run_demo_mode(self):
        """运行演示模式"""
        print("\n🎭 启动演示模式...")
        
        for i in range(5):
            print(f"\n📊 演示轮次 {i+1}/5:")
            
            # 获取最新数据
            await self.components['indicator_calculator'].fetch_kline_data("BTC_USDT", "1m", 100)
            
            # 运行策略分析
            strategy_result = await self.components['strategy_manager'].run_all_strategies()
            signal = strategy_result.get('final_signal', 'HOLD')
            confidence = strategy_result.get('final_confidence', 0)
            
            print(f"   🎯 策略信号: {signal} (置信度: {confidence:.2f})")
            
            # 风险检查
            risk_metrics = self.components['risk_manager'].get_risk_metrics()
            print(f"   🛡️ 风险状态: {risk_metrics['risk_status']}")
            print(f"   💰 账户余额: {risk_metrics['current_balance']:.2f}")
            
            # 模拟交易决策
            if signal != 'HOLD' and confidence > 0.6:
                print(f"   📈 模拟执行: {signal} 订单")
                
                # 模拟交易结果
                import random
                pnl = random.uniform(-20, 30)
                self.components['risk_manager'].update_trade_result(pnl)
                print(f"   💼 交易结果: {pnl:+.2f}")
            else:
                print(f"   ⏸️ 跳过交易: 信号不明确或置信度不足")
            
            await asyncio.sleep(2)
        
        # 最终统计
        final_metrics = self.components['risk_manager'].get_risk_metrics()
        print(f"\n📋 演示模式总结:")
        print(f"   💰 最终余额: {final_metrics['current_balance']:.2f}")
        print(f"   📈 总盈亏: {final_metrics['total_pnl']:+.2f}")
        print(f"   🎯 胜率: {final_metrics['win_rate']:.2%}")
        print(f"   📊 交易次数: {final_metrics['trade_count']}")
    
    async def run_live_mode(self):
        """运行实盘模式"""
        print("\n🔴 启动实盘模式...")
        print("⚠️ 注意: 这将使用真实资金进行交易！")
        
        # 确认提示
        confirm = input("请输入 'CONFIRM' 确认启动实盘模式: ")
        if confirm != 'CONFIRM':
            print("❌ 实盘模式启动取消")
            return
        
        print("🚀 实盘模式启动...")
        
        # 实盘交易循环
        iteration = 0
        while True:
            iteration += 1
            print(f"\n📊 实盘交易循环 #{iteration}:")
            
            try:
                # 获取最新数据
                await self.components['indicator_calculator'].fetch_kline_data("BTC_USDT", "1m", 100)
                
                # 运行策略分析
                strategy_result = await self.components['strategy_manager'].run_all_strategies()
                signal = strategy_result.get('final_signal', 'HOLD')
                confidence = strategy_result.get('final_confidence', 0)
                
                print(f"   🎯 策略信号: {signal} (置信度: {confidence:.2f})")
                
                # 风险检查
                risk_ok = (
                    self.components['risk_manager'].check_position_size(100) and
                    self.components['risk_manager'].check_daily_loss_limit() and
                    self.components['risk_manager'].check_trade_frequency()
                )
                
                print(f"   🛡️ 风险检查: {'通过' if risk_ok else '不通过'}")
                
                # 执行交易
                if signal != 'HOLD' and confidence > 0.7 and risk_ok:
                    print(f"   📈 准备执行: {signal} 订单")
                    
                    # 这里可以调用真实的订单执行
                    # result = await self.components['order_manager'].place_buy_order(...)
                    print(f"   ⚠️ 实际订单执行已禁用（安全考虑）")
                else:
                    print(f"   ⏸️ 跳过交易")
                
                # 等待下一轮
                print(f"   ⏰ 等待60秒后进行下一轮...")
                await asyncio.sleep(60)
                
            except KeyboardInterrupt:
                print("\n⏹️ 用户中断，退出实盘模式")
                break
            except Exception as e:
                print(f"\n❌ 实盘模式异常: {e}")
                await asyncio.sleep(10)
    
    async def run_gui_mode(self):
        """运行GUI模式"""
        print("\n🖥️ 启动GUI模式...")
        
        try:
            # 这里可以启动GUI界面
            app = WMZC.TradingApp()
            app.run()
        except Exception as e:
            print(f"❌ GUI启动失败: {e}")
            print("💡 请检查tkinter是否正确安装")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='WMZC专业量化交易系统')
    parser.add_argument('--mode', choices=['demo', 'live', 'gui', 'check'], 
                       default='demo', help='运行模式')
    parser.add_argument('--no-banner', action='store_true', help='不显示启动横幅')
    
    args = parser.parse_args()
    
    launcher = WMZCLauncher()
    
    # 显示横幅
    if not args.no_banner:
        launcher.print_banner()
    
    async def run_launcher():
        # 初始化系统
        await launcher.initialize_system()
        
        # 运行系统检查
        if not await launcher.run_system_check():
            print("❌ 系统检查失败，无法启动")
            return
        
        # 根据模式运行
        if args.mode == 'demo':
            await launcher.run_demo_mode()
        elif args.mode == 'live':
            await launcher.run_live_mode()
        elif args.mode == 'gui':
            await launcher.run_gui_mode()
        elif args.mode == 'check':
            print("✅ 系统检查完成，所有功能正常")
    
    try:
        asyncio.run(run_launcher())
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
