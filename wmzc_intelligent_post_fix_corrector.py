#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC智能修复后纠错器
专门修复之前修复过程中引入的问题，确保零回归
"""

import re
import shutil
from datetime import datetime
from typing import List, Tuple

class IntelligentPostFixCorrector:
    """智能修复后纠错器 - 修复过度修复问题"""
    
    def __init__(self, file_path: str = "WMZC.py"):
        self.file_path = file_path
        self.backup_path = f"WMZC_pre_correction_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        self.fixes_applied = 0
        self.code_lines = []
        
    def create_backup(self) -> bool:
        """创建备份"""
        try:
            shutil.copy2(self.file_path, self.backup_path)
            print(f"✅ 备份已创建: {self.backup_path}")
            return True
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    
    def load_code(self) -> bool:
        """加载代码"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.code_lines = f.readlines()
            print(f"✅ 成功加载 {len(self.code_lines)} 行代码")
            return True
        except Exception as e:
            print(f"❌ 加载代码失败: {e}")
            return False
    
    def fix_commented_important_functions(self):
        """修复被错误注释的重要函数"""
        print("🔧 修复被错误注释的重要函数...")
        
        fixes = 0
        i = 0
        while i < len(self.code_lines):
            line = self.code_lines[i]
            
            # 检测被注释的函数定义模式
            if re.match(r'^# 🔧 P0修复: 删除简单重复函数 -\s+def\s+\w+', line):
                # 提取函数定义
                func_match = re.search(r'def\s+(\w+)\s*\([^)]*\):', line)
                if func_match:
                    func_name = func_match.group(1)
                    
                    # 检查是否是重要函数（不应该被删除的）
                    important_functions = [
                        'get_current_exchange', 'get_current_symbol', 'get_default_symbol',
                        'safe_float', 'safe_int', 'format_number', 'validate_config',
                        'log', 'debug_log', 'error_log', 'warning_log'
                    ]
                    
                    if func_name in important_functions:
                        # 恢复函数定义
                        restored_lines = []
                        j = i
                        
                        # 收集所有被注释的函数行
                        while j < len(self.code_lines) and self.code_lines[j].startswith('# 🔧 P0修复: 删除简单重复函数 -'):
                            commented_line = self.code_lines[j]
                            # 提取原始代码
                            original_code = commented_line.replace('# 🔧 P0修复: 删除简单重复函数 -', '').lstrip()
                            if original_code.strip():
                                restored_lines.append(original_code)
                            j += 1
                        
                        # 替换注释行为恢复的代码
                        if restored_lines:
                            # 添加恢复注释
                            restored_lines.insert(0, f"    # 🔧 智能恢复: {func_name} - 重要函数不应删除\n")
                            
                            # 替换原来的注释行
                            self.code_lines[i:j] = restored_lines
                            fixes += 1
                            print(f"  ✅ 恢复重要函数: {func_name}")
                            
                            # 调整索引
                            i += len(restored_lines)
                            continue
            
            i += 1
        
        self.fixes_applied += fixes
        print(f"✅ 恢复了 {fixes} 个重要函数")
    
    def fix_empty_class_definitions(self):
        """修复空类定义"""
        print("🔧 修复空类定义...")
        
        fixes = 0
        i = 0
        while i < len(self.code_lines):
            line = self.code_lines[i]
            
            # 检测类定义
            if re.match(r'^\s*class\s+\w+.*:', line):
                class_match = re.search(r'class\s+(\w+)', line)
                if class_match:
                    class_name = class_match.group(1)
                    
                    # 检查类是否为空（只有文档字符串）
                    j = i + 1
                    has_methods = False
                    has_docstring = False
                    
                    # 跳过文档字符串
                    if j < len(self.code_lines) and '"""' in self.code_lines[j]:
                        has_docstring = True
                        # 找到文档字符串结束
                        if self.code_lines[j].count('"""') == 2:
                            j += 1
                        else:
                            j += 1
                            while j < len(self.code_lines) and '"""' not in self.code_lines[j]:
                                j += 1
                            if j < len(self.code_lines):
                                j += 1
                    
                    # 检查是否有方法定义
                    while j < len(self.code_lines):
                        next_line = self.code_lines[j].strip()
                        if not next_line or next_line.startswith('#'):
                            j += 1
                            continue
                        if next_line.startswith('def '):
                            has_methods = True
                            break
                        if not next_line.startswith(' ') and not next_line.startswith('\t'):
                            # 到达下一个顶级定义
                            break
                        j += 1
                    
                    # 如果类为空且是功能类，添加pass
                    if not has_methods and class_name.endswith(('Group', 'Manager', 'Handler')):
                        insert_pos = i + 1
                        if has_docstring:
                            # 在文档字符串后插入
                            while insert_pos < len(self.code_lines) and ('"""' in self.code_lines[insert_pos] or self.code_lines[insert_pos].strip().startswith('"""')):
                                insert_pos += 1
                        
                        # 插入pass语句
                        indent = '    '
                        self.code_lines.insert(insert_pos, f"{indent}pass  # 🔧 智能修复: 空类定义\n")
                        fixes += 1
                        print(f"  ✅ 修复空类: {class_name}")
            
            i += 1
        
        self.fixes_applied += fixes
        print(f"✅ 修复了 {fixes} 个空类定义")
    
    def fix_orphaned_docstrings(self):
        """修复孤立的文档字符串"""
        print("🔧 修复孤立的文档字符串...")
        
        fixes = 0
        i = 0
        while i < len(self.code_lines):
            line = self.code_lines[i].strip()
            
            # 检测孤立的文档字符串
            if line.startswith('"""') and line.endswith('"""') and len(line) > 6:
                # 检查前一行是否是函数或类定义
                if i > 0:
                    prev_line = self.code_lines[i-1].strip()
                    if not (prev_line.endswith(':') or prev_line.startswith('def ') or prev_line.startswith('class ')):
                        # 这是一个孤立的文档字符串，注释掉它
                        self.code_lines[i] = f"# 🔧 智能修复: 孤立文档字符串 - {line}\n"
                        fixes += 1
                        print(f"  ✅ 注释孤立文档字符串: {line[:50]}...")
            
            i += 1
        
        self.fixes_applied += fixes
        print(f"✅ 修复了 {fixes} 个孤立文档字符串")
    
    def fix_duplicate_imports(self):
        """智能修复重复导入"""
        print("🔧 智能修复重复导入...")
        
        seen_imports = set()
        fixes = 0
        
        for i, line in enumerate(self.code_lines):
            if re.match(r'^\s*(import|from)\s+', line):
                # 标准化导入语句
                normalized = re.sub(r'\s+', ' ', line.strip())
                
                if normalized in seen_imports:
                    # 标记为重复导入
                    self.code_lines[i] = f"# 🔧 智能修复: 重复导入 - {line}"
                    fixes += 1
                    print(f"  ✅ 移除重复导入: {normalized[:50]}...")
                else:
                    seen_imports.add(normalized)
        
        self.fixes_applied += fixes
        print(f"✅ 修复了 {fixes} 个重复导入")
    
    def fix_performance_issues(self):
        """修复明显的性能问题"""
        print("🔧 修复明显的性能问题...")
        
        fixes = 0
        
        for i, line in enumerate(self.code_lines):
            original_line = line
            
            # 修复循环中的len()调用
            if re.search(r'for\s+\w+\s+in\s+range\(len\((\w+)\)\):', line):
                # 建议使用enumerate
                self.code_lines[i] = line.rstrip() + "  # 🔧 性能提示: 考虑使用enumerate\n"
                if line == original_line:  # 只有在实际修改时才计数
                    fixes += 1
            
            # 修复字符串拼接
            elif '+=' in line and ('"' in line or "'" in line) and 'for ' in self.code_lines[max(0, i-2):i+1]:
                self.code_lines[i] = line.rstrip() + "  # 🔧 性能提示: 考虑使用join()或f-string\n"
                if line == original_line:
                    fixes += 1
        
        self.fixes_applied += fixes
        print(f"✅ 添加了 {fixes} 个性能提示")
    
    def save_corrected_code(self) -> bool:
        """保存修正后的代码"""
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                f.writelines(self.code_lines)
            print(f"✅ 修正后的代码已保存")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def run_intelligent_correction(self) -> bool:
        """运行智能修正"""
        print("🚀 启动WMZC智能修复后纠错...")
        print("=" * 80)
        
        # 1. 创建备份
        if not self.create_backup():
            return False
        
        # 2. 加载代码
        if not self.load_code():
            return False
        
        # 3. 执行各种修正
        self.fix_commented_important_functions()
        self.fix_empty_class_definitions()
        self.fix_orphaned_docstrings()
        self.fix_duplicate_imports()
        self.fix_performance_issues()
        
        # 4. 保存修正后的代码
        if not self.save_corrected_code():
            return False
        
        # 5. 生成修正报告
        self.generate_correction_report()
        
        return True
    
    def generate_correction_report(self):
        """生成修正报告"""
        print("\n" + "=" * 80)
        print("📊 WMZC智能修复后纠错报告")
        print("=" * 80)
        
        print(f"🔧 总修正数: {self.fixes_applied}")
        print(f"📁 备份文件: {self.backup_path}")
        
        if self.fixes_applied > 0:
            print("\n✅ 修正完成！主要修正内容:")
            print("  • 恢复被错误注释的重要函数")
            print("  • 修复空类定义")
            print("  • 处理孤立的文档字符串")
            print("  • 清理重复导入")
            print("  • 添加性能优化提示")
            
            print(f"\n💡 建议:")
            print("  • 重新运行语法检查确保无错误")
            print("  • 运行功能测试验证修正效果")
            print("  • 检查恢复的函数是否正常工作")
        else:
            print("\n🎉 未发现需要修正的问题，代码状态良好！")

def main():
    """主函数"""
    print("🔧 WMZC智能修复后纠错器")
    print("专门修复之前修复过程中引入的问题，确保零回归")
    print("=" * 80)
    
    corrector = IntelligentPostFixCorrector()
    
    try:
        success = corrector.run_intelligent_correction()
        
        if success:
            print("\n✅ 智能纠错完成！")
            return True
        else:
            print("\n❌ 纠错过程中出现错误")
            return False
            
    except Exception as e:
        print(f"\n💥 纠错异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
