#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 紧急修复验证测试
验证刚刚修复的运行时错误是否解决
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_log_function():
    """测试日志函数是否正常工作"""
    print("🔧 测试1: 日志函数递归调用修复")
    try:
        from WMZC import log
        
        # 测试基本日志功能
        log("测试消息1", "INFO")
        log("测试消息2", "DEBUG", context="测试上下文")
        log("测试消息3", "ERROR", error_details="测试错误详情")
        
        # 测试递归防护
        for i in range(5):
            log(f"递归测试 {i}", "INFO")
            
        print("✅ 日志函数测试通过")
        return True
    except Exception as e:
        print(f"❌ 日志函数测试失败: {e}")
        return False

def test_integrated_ai_manager():
    """测试IntegratedAIManager是否正常初始化"""
    print("🔧 测试2: IntegratedAIManager修复")
    try:
        from WMZC import IntegratedAIManager
        
        # 测试初始化
        ai_manager = IntegratedAIManager()
        
        # 检查方法是否存在
        if hasattr(ai_manager, '_load_api_keys_from_config'):
            print("✅ _load_api_keys_from_config方法存在")
        else:
            print("❌ _load_api_keys_from_config方法不存在")
            return False
            
        print("✅ IntegratedAIManager测试通过")
        return True
    except Exception as e:
        print(f"❌ IntegratedAIManager测试失败: {e}")
        return False

def test_config_persistence():
    """测试UnifiedConfigPersistence是否正常工作"""
    print("🔧 测试3: UnifiedConfigPersistence修复")
    try:
        from WMZC import UnifiedConfigPersistence
        
        # 测试初始化
        config_manager = UnifiedConfigPersistence()
        
        # 检查方法是否存在
        if hasattr(config_manager, 'load_config'):
            print("✅ load_config方法存在")
        else:
            print("❌ load_config方法不存在")
            return False
            
        # 测试方法调用
        try:
            config_manager.load_config()
            print("✅ load_config方法调用成功")
        except Exception as e:
            print(f"⚠️ load_config方法调用警告: {e}")
            
        print("✅ UnifiedConfigPersistence测试通过")
        return True
    except Exception as e:
        print(f"❌ UnifiedConfigPersistence测试失败: {e}")
        return False

def test_super_unified_manager():
    """测试SuperUnifiedManager是否正常工作"""
    print("🔧 测试4: SuperUnifiedManager修复")
    try:
        from WMZC import SuperUnifiedManager
        
        # 测试初始化
        manager = SuperUnifiedManager()
        
        # 检查方法是否存在
        methods_to_check = ['get_kline', 'log']
        for method_name in methods_to_check:
            if hasattr(manager, method_name):
                print(f"✅ {method_name}方法存在")
            else:
                print(f"❌ {method_name}方法不存在")
                return False
                
        print("✅ SuperUnifiedManager测试通过")
        return True
    except Exception as e:
        print(f"❌ SuperUnifiedManager测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始运行紧急修复验证测试")
    print("=" * 50)
    
    tests = [
        test_log_function,
        test_integrated_ai_manager,
        test_config_persistence,
        test_super_unified_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print("-" * 30)
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            print("-" * 30)
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修复验证测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
