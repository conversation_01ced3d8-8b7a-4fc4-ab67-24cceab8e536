#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC配置警告最终修复验证
验证9个未知配置字段的修复效果
"""

import sys
import os
import json
import traceback

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_schema_completeness():
    """测试配置架构完整性"""
    print("🔍 测试配置架构完整性...")
    
    try:
        import WMZC
        
        # 获取ConfigValidator类
        if hasattr(WMZC, 'ConfigValidator'):
            validator_class = WMZC.ConfigValidator
            schema = validator_class.CONFIG_SCHEMA
            
            # 读取实际配置文件
            config_file = r'C:\Users\<USER>\.wmzc_trading\trading_config.json'
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    actual_config = json.load(f)
                
                print(f"✅ 配置文件加载成功，包含 {len(actual_config)} 个字段")
                
                # 检查哪些字段在配置文件中但不在schema中
                unknown_fields = set(actual_config.keys()) - set(schema.keys())
                
                print(f"\n📊 配置字段分析:")
                print(f"  - 配置文件字段数: {len(actual_config)}")
                print(f"  - CONFIG_SCHEMA字段数: {len(schema)}")
                print(f"  - 未知字段数: {len(unknown_fields)}")
                
                if unknown_fields:
                    print(f"\n❌ 仍有未知字段:")
                    for i, field in enumerate(sorted(unknown_fields), 1):
                        print(f"  {i:2d}. {field}")
                    return False
                else:
                    print(f"\n✅ 所有配置字段都已在CONFIG_SCHEMA中定义")
                    return True
            else:
                print("❌ 配置文件不存在")
                return False
        else:
            print("❌ ConfigValidator类不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置架构完整性测试失败: {e}")
        traceback.print_exc()
        return False

def test_config_validation():
    """测试配置验证功能"""
    print("\n🔍 测试配置验证功能...")
    
    try:
        import WMZC
        
        # 获取ConfigValidator类
        validator_class = WMZC.ConfigValidator
        
        # 读取实际配置文件
        config_file = r'C:\Users\<USER>\.wmzc_trading\trading_config.json'
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 执行配置验证
            result = validator_class.validate_config(config_data, strict_mode=False)
            
            print(f"📋 验证结果:")
            print(f"  - 验证状态: {'✅ 通过' if result['valid'] else '❌ 失败'}")
            print(f"  - 错误数量: {len(result.get('errors', []))}")
            print(f"  - 警告数量: {len(result.get('warnings', []))}")
            
            # 检查是否还有未知字段警告
            unknown_field_warnings = [w for w in result.get('warnings', []) if '未知配置字段' in w]
            
            if unknown_field_warnings:
                print(f"\n❌ 仍有未知字段警告:")
                for warning in unknown_field_warnings:
                    print(f"  - {warning}")
                return False
            else:
                print(f"\n✅ 没有未知字段警告")
                
                # 显示其他警告（如果有）
                other_warnings = [w for w in result.get('warnings', []) if '未知配置字段' not in w]
                if other_warnings:
                    print(f"\n📝 其他警告 ({len(other_warnings)}个):")
                    for warning in other_warnings[:3]:  # 只显示前3个
                        print(f"  - {warning}")
                    if len(other_warnings) > 3:
                        print(f"  - ... 还有 {len(other_warnings) - 3} 个警告")
                
                return True
        else:
            print("❌ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        traceback.print_exc()
        return False

def test_specific_fields():
    """测试特定字段的修复"""
    print("\n🔍 测试特定字段修复...")
    
    try:
        import WMZC
        
        validator_class = WMZC.ConfigValidator
        schema = validator_class.CONFIG_SCHEMA
        
        # 检查之前缺失的9个字段
        target_fields = [
            'ENABLE_LOGGING', 'LOG_TO_CONSOLE', 'KDJ_PERIOD',
            'MACD_FAST', 'MACD_SLOW', 'MACD_SIGNAL',
            'STOP_LOSS', 'TAKE_PROFIT', 'api_key'
        ]
        
        print(f"📋 检查目标字段 ({len(target_fields)}个):")
        
        missing_fields = []
        for field in target_fields:
            if field in schema:
                print(f"  ✅ {field}: {schema[field]}")
            else:
                print(f"  ❌ {field}: 缺失")
                missing_fields.append(field)
        
        if missing_fields:
            print(f"\n❌ 仍有 {len(missing_fields)} 个字段缺失")
            return False
        else:
            print(f"\n✅ 所有目标字段都已添加")
            return True
            
    except Exception as e:
        print(f"❌ 特定字段测试失败: {e}")
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔍 测试向后兼容性...")
    
    try:
        import WMZC
        
        validator_class = WMZC.ConfigValidator
        
        # 测试大小写兼容性
        test_configs = [
            {'API_KEY': 'test-key-uppercase'},
            {'api_key': 'test-key-lowercase'},
            {'SYMBOL': 'BTC-USDT-SWAP'},
            {'symbol': 'ETH-USDT-SWAP'},
            {'LEVERAGE': 5},
            {'leverage': 3}
        ]
        
        print(f"📋 测试向后兼容性:")
        
        all_passed = True
        for i, config in enumerate(test_configs, 1):
            result = validator_class.validate_config(config, strict_mode=False)
            field_name = list(config.keys())[0]
            
            if result['valid']:
                print(f"  ✅ 测试 {i}: {field_name} = {config[field_name]}")
            else:
                print(f"  ❌ 测试 {i}: {field_name} = {config[field_name]}")
                all_passed = False
        
        if all_passed:
            print(f"\n✅ 向后兼容性测试通过")
            return True
        else:
            print(f"\n❌ 向后兼容性测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("🔧 WMZC配置警告最终修复验证")
    print("=" * 80)
    print("🎯 目标：验证9个未知配置字段的修复效果")
    print()
    
    # 执行所有测试
    tests = [
        ("配置架构完整性", test_config_schema_completeness),
        ("配置验证功能", test_config_validation),
        ("特定字段修复", test_specific_fields),
        ("向后兼容性", test_backward_compatibility),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    print("\n" + "=" * 80)
    print("📊 最终验证结果")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 配置警告修复验证完全通过！")
        print("\n📋 修复成果:")
        print("✅ 9个未知配置字段已全部添加到CONFIG_SCHEMA")
        print("✅ 配置验证不再产生未知字段警告")
        print("✅ 向后兼容性得到保证")
        print("✅ 系统配置验证功能正常")
        print("\n🎯 预期效果：启动WMZC系统时不再有配置警告")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    # 运行验证
    success = main()
    sys.exit(0 if success else 1)
