#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC日志控制台诊断工具
全面诊断日志控制台显示问题并提供修复方案
"""

import os
import json
import logging
import tkinter as tk
from datetime import datetime
import traceback

class LogConsoleDiagnoser:
    """日志控制台诊断器"""
    
    def __init__(self):
        self.current_dir = os.getcwd()
        self.wmzc_file = 'WMZC.py'
        self.config_files = ['trading_config.json', 'wmzc_config.json']
        self.diagnosis_results = {}
        self.issues_found = []
        
    def run_comprehensive_diagnosis(self):
        """运行全面诊断"""
        print("🔍 WMZC日志控制台诊断")
        print("=" * 60)
        
        try:
            # 1. 检查日志控制台GUI组件
            self.check_log_console_gui()
            
            # 2. 检查日志配置设置
            self.check_log_configuration()
            
            # 3. 检查日志记录器设置
            self.check_logger_setup()
            
            # 4. 检查日志输出重定向
            self.check_log_output_redirection()
            
            # 5. 检查GUI日志绑定
            self.check_gui_log_binding()
            
            # 6. 测试日志功能
            self.test_log_functionality()
            
            # 7. 生成诊断报告
            self.generate_diagnosis_report()
            
            return len(self.issues_found) == 0
            
        except Exception as e:
            print(f"❌ 诊断过程中发生异常: {e}")
            traceback.print_exc()
            return False
    
    def check_log_console_gui(self):
        """检查日志控制台GUI组件"""
        print("\n📜 检查日志控制台GUI组件...")
        
        if not os.path.exists(self.wmzc_file):
            print("  ❌ WMZC.py 文件不存在")
            self.issues_found.append("WMZC.py 文件缺失")
            return
        
        try:
            with open(self.wmzc_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查日志控制台相关的关键组件
            gui_components = {
                '日志控制台标签页': '📜 日志控制台',
                '日志文本框组件': 'log_text',
                '日志滚动条': 'log_scrollbar',
                '日志清除按钮': 'clear_log',
                '日志级别设置': 'log_level'
            }
            
            missing_components = []
            found_components = []
            
            for component_name, search_pattern in gui_components.items():
                if search_pattern in content:
                    found_components.append(component_name)
                    print(f"  ✅ {component_name}: 已找到")
                else:
                    missing_components.append(component_name)
                    print(f"  ❌ {component_name}: 未找到")
            
            # 检查日志控制台初始化函数
            log_init_patterns = [
                'def setup_log_console',
                'def init_log_console',
                'def create_log_tab',
                'log_console_frame'
            ]
            
            log_init_found = []
            for pattern in log_init_patterns:
                if pattern in content:
                    log_init_found.append(pattern)
            
            if log_init_found:
                print(f"  ✅ 日志控制台初始化函数: {log_init_found}")
            else:
                print("  ❌ 日志控制台初始化函数: 未找到")
                self.issues_found.append("日志控制台初始化函数缺失")
            
            # 检查日志处理器绑定
            handler_patterns = [
                'TextHandler',
                'GUILogHandler',
                'log_text.insert',
                'log_text.config'
            ]
            
            handler_found = []
            for pattern in handler_patterns:
                if pattern in content:
                    handler_found.append(pattern)
            
            if handler_found:
                print(f"  ✅ 日志处理器绑定: {handler_found}")
            else:
                print("  ⚠️ 日志处理器绑定: 可能存在问题")
                self.issues_found.append("日志处理器绑定可能有问题")
            
            self.diagnosis_results['gui_components'] = {
                'found_components': found_components,
                'missing_components': missing_components,
                'init_functions': log_init_found,
                'handler_bindings': handler_found
            }
            
        except Exception as e:
            print(f"  ❌ 检查GUI组件失败: {e}")
            self.issues_found.append(f"GUI组件检查失败: {e}")
    
    def check_log_configuration(self):
        """检查日志配置设置"""
        print("\n⚙️ 检查日志配置设置...")
        
        log_config = {}
        
        # 检查配置文件中的日志设置
        for config_file in self.config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    log_level = config.get('LOG_LEVEL', 'INFO')
                    log_config[config_file] = {
                        'LOG_LEVEL': log_level,
                        'ENABLE_LOGGING': config.get('ENABLE_LOGGING', True),
                        'LOG_TO_FILE': config.get('LOG_TO_FILE', False),
                        'LOG_TO_CONSOLE': config.get('LOG_TO_CONSOLE', True)
                    }
                    
                    print(f"  📄 {config_file}:")
                    print(f"    LOG_LEVEL: {log_level}")
                    print(f"    ENABLE_LOGGING: {config.get('ENABLE_LOGGING', True)}")
                    print(f"    LOG_TO_CONSOLE: {config.get('LOG_TO_CONSOLE', True)}")
                    
                    # 检查日志级别是否过高
                    if log_level in ['ERROR', 'CRITICAL']:
                        print(f"    ⚠️ 日志级别过高，可能过滤掉重要信息")
                        self.issues_found.append(f"{config_file} 日志级别过高: {log_level}")
                    
                except Exception as e:
                    print(f"  ❌ 读取 {config_file} 失败: {e}")
            else:
                print(f"  ❌ {config_file}: 文件不存在")
        
        self.diagnosis_results['log_configuration'] = log_config
    
    def check_logger_setup(self):
        """检查日志记录器设置"""
        print("\n📝 检查日志记录器设置...")
        
        if not os.path.exists(self.wmzc_file):
            return
        
        try:
            with open(self.wmzc_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查日志记录器相关代码
            logger_patterns = {
                'logging模块导入': 'import logging',
                'logger创建': 'logging.getLogger',
                'handler添加': 'addHandler',
                'formatter设置': 'Formatter',
                'basicConfig调用': 'logging.basicConfig'
            }
            
            logger_setup = {}
            
            for pattern_name, pattern in logger_patterns.items():
                if pattern in content:
                    logger_setup[pattern_name] = True
                    print(f"  ✅ {pattern_name}: 已找到")
                else:
                    logger_setup[pattern_name] = False
                    print(f"  ❌ {pattern_name}: 未找到")
            
            # 检查自定义日志函数
            custom_log_patterns = [
                'def log(',
                'def print_log(',
                'def write_log(',
                'def add_log('
            ]
            
            custom_log_found = []
            for pattern in custom_log_patterns:
                if pattern in content:
                    custom_log_found.append(pattern)
            
            if custom_log_found:
                print(f"  ✅ 自定义日志函数: {custom_log_found}")
            else:
                print("  ⚠️ 自定义日志函数: 未找到")
            
            self.diagnosis_results['logger_setup'] = {
                'standard_patterns': logger_setup,
                'custom_functions': custom_log_found
            }
            
        except Exception as e:
            print(f"  ❌ 检查日志记录器失败: {e}")
    
    def check_log_output_redirection(self):
        """检查日志输出重定向"""
        print("\n🔄 检查日志输出重定向...")
        
        if not os.path.exists(self.wmzc_file):
            return
        
        try:
            with open(self.wmzc_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查可能的输出重定向
            redirect_patterns = {
                'sys.stdout重定向': 'sys.stdout =',
                'sys.stderr重定向': 'sys.stderr =',
                'print函数重定向': 'print.*file=',
                '文件日志handler': 'FileHandler',
                '流日志handler': 'StreamHandler'
            }
            
            redirect_status = {}
            
            for pattern_name, pattern in redirect_patterns.items():
                if pattern in content:
                    redirect_status[pattern_name] = True
                    print(f"  ⚠️ {pattern_name}: 检测到")
                    if 'stdout' in pattern_name or 'stderr' in pattern_name:
                        self.issues_found.append(f"检测到{pattern_name}，可能影响控制台输出")
                else:
                    redirect_status[pattern_name] = False
                    print(f"  ✅ {pattern_name}: 未检测到")
            
            # 检查日志文件输出
            log_files = []
            for file in os.listdir('.'):
                if file.endswith('.log') or 'log' in file.lower():
                    log_files.append(file)
            
            if log_files:
                print(f"  📁 发现日志文件: {log_files}")
                print("  💡 日志可能被重定向到文件")
            else:
                print("  ✅ 未发现日志文件")
            
            self.diagnosis_results['output_redirection'] = {
                'redirect_patterns': redirect_status,
                'log_files': log_files
            }
            
        except Exception as e:
            print(f"  ❌ 检查输出重定向失败: {e}")
    
    def check_gui_log_binding(self):
        """检查GUI日志绑定"""
        print("\n🔗 检查GUI日志绑定...")
        
        if not os.path.exists(self.wmzc_file):
            return
        
        try:
            with open(self.wmzc_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查GUI日志绑定相关代码
            binding_patterns = {
                'GUI日志处理器类': 'class.*LogHandler',
                'GUI日志写入方法': 'def write.*log',
                '日志文本框更新': 'log_text.*insert',
                '日志自动滚动': 'log_text.*see',
                '日志清除功能': 'log_text.*delete'
            }
            
            binding_status = {}
            
            for pattern_name, pattern in binding_patterns.items():
                if pattern in content:
                    binding_status[pattern_name] = True
                    print(f"  ✅ {pattern_name}: 已实现")
                else:
                    binding_status[pattern_name] = False
                    print(f"  ❌ {pattern_name}: 未实现")
            
            # 检查日志控制台初始化时机
            init_timing_patterns = [
                'setup_log_console',
                'init_logging',
                'create_log_tab'
            ]
            
            init_timing = []
            for pattern in init_timing_patterns:
                if pattern in content:
                    init_timing.append(pattern)
            
            if init_timing:
                print(f"  ✅ 日志控制台初始化: {init_timing}")
            else:
                print("  ❌ 日志控制台初始化: 可能存在问题")
                self.issues_found.append("日志控制台初始化可能有问题")
            
            self.diagnosis_results['gui_binding'] = {
                'binding_patterns': binding_status,
                'init_timing': init_timing
            }
            
        except Exception as e:
            print(f"  ❌ 检查GUI绑定失败: {e}")
    
    def test_log_functionality(self):
        """测试日志功能"""
        print("\n🧪 测试日志功能...")
        
        try:
            # 创建测试日志记录器
            test_logger = logging.getLogger('wmzc_test')
            test_logger.setLevel(logging.DEBUG)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.DEBUG)
            
            # 创建格式器
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            test_logger.addHandler(console_handler)
            
            # 测试不同级别的日志
            print("  🧪 测试日志输出...")
            test_logger.debug("DEBUG级别测试消息")
            test_logger.info("INFO级别测试消息")
            test_logger.warning("WARNING级别测试消息")
            test_logger.error("ERROR级别测试消息")
            
            print("  ✅ 基础日志功能正常")
            
            # 测试GUI日志功能（如果可能）
            try:
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                
                # 创建测试文本框
                test_text = tk.Text(root)
                
                # 测试文本插入
                test_text.insert(tk.END, "测试日志消息\n")
                test_text.see(tk.END)
                
                print("  ✅ GUI日志组件测试正常")
                
                root.destroy()
                
            except Exception as e:
                print(f"  ⚠️ GUI日志组件测试失败: {e}")
                self.issues_found.append(f"GUI日志组件测试失败: {e}")
            
            self.diagnosis_results['functionality_test'] = {
                'basic_logging': True,
                'gui_components': True
            }
            
        except Exception as e:
            print(f"  ❌ 日志功能测试失败: {e}")
            self.issues_found.append(f"日志功能测试失败: {e}")
            self.diagnosis_results['functionality_test'] = {
                'basic_logging': False,
                'gui_components': False
            }
    
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n📊 生成诊断报告...")
        
        report = {
            'diagnosis_time': datetime.now().isoformat(),
            'diagnosis_results': self.diagnosis_results,
            'issues_found': self.issues_found,
            'recommendations': self.generate_recommendations()
        }
        
        try:
            with open('log_console_diagnosis_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print("  ✅ 诊断报告已保存: log_console_diagnosis_report.json")
        except Exception as e:
            print(f"  ❌ 保存诊断报告失败: {e}")
        
        # 显示问题总结
        if self.issues_found:
            print(f"\n⚠️ 发现 {len(self.issues_found)} 个问题:")
            for i, issue in enumerate(self.issues_found, 1):
                print(f"  {i}. {issue}")
        else:
            print("\n✅ 未发现明显问题")
    
    def generate_recommendations(self):
        """生成修复建议"""
        recommendations = []
        
        if any('GUI组件' in issue for issue in self.issues_found):
            recommendations.append("修复日志控制台GUI组件")
        
        if any('日志级别过高' in issue for issue in self.issues_found):
            recommendations.append("调整日志级别为INFO或DEBUG")
        
        if any('初始化' in issue for issue in self.issues_found):
            recommendations.append("修复日志控制台初始化逻辑")
        
        if any('重定向' in issue for issue in self.issues_found):
            recommendations.append("检查并修复输出重定向问题")
        
        if any('绑定' in issue for issue in self.issues_found):
            recommendations.append("修复GUI日志绑定机制")
        
        if not recommendations:
            recommendations.append("日志控制台诊断未发现明显问题，可能需要更深入的分析")
        
        return recommendations

def main():
    """主函数"""
    print("🔍 WMZC日志控制台诊断工具")
    print("=" * 60)
    
    diagnoser = LogConsoleDiagnoser()
    
    try:
        success = diagnoser.run_comprehensive_diagnosis()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ 日志控制台诊断完成，未发现严重问题")
            print("💡 建议:")
            print("  1. 检查日志控制台标签页是否正常显示")
            print("  2. 验证日志消息是否实时更新")
            print("  3. 测试日志级别设置是否生效")
        else:
            print("⚠️ 日志控制台诊断发现问题")
            print("💡 建议:")
            print("  1. 查看诊断报告了解具体问题")
            print("  2. 运行日志控制台修复工具")
            print("  3. 重新启动WMZC系统验证修复效果")
        
        return success
        
    except Exception as e:
        print(f"❌ 诊断过程中发生异常: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
