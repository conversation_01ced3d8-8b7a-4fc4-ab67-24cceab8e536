#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找WMZC.py中所有await在非async函数中的问题
"""

import re

def find_await_issues():
    """查找所有await使用问题"""
    
    with open('WMZC.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    issues = []
    current_function = None
    current_function_line = 0
    is_async_function = False
    
    for i, line in enumerate(lines, 1):
        line_stripped = line.strip()
        
        # 跳过注释行
        if line_stripped.startswith('#'):
            continue
        
        # 检查函数定义
        if re.match(r'^\s*def\s+', line):
            if 'async def' in line:
                is_async_function = True
                current_function = re.search(r'async def\s+(\w+)', line)
            else:
                is_async_function = False
                current_function = re.search(r'def\s+(\w+)', line)
            
            if current_function:
                current_function = current_function.group(1)
                current_function_line = i
        
        # 检查类定义（重置函数状态）
        elif re.match(r'^\s*class\s+', line):
            current_function = None
            is_async_function = False
        
        # 检查await使用
        elif 'await ' in line and not is_async_function and current_function:
            # 排除字符串中的await
            if not ('"await' in line or "'await" in line):
                issues.append({
                    'line_number': i,
                    'line_content': line_stripped,
                    'function_name': current_function,
                    'function_line': current_function_line
                })
    
    return issues

def main():
    print("🔍 查找WMZC.py中的await使用问题")
    print("=" * 50)
    
    issues = find_await_issues()
    
    if not issues:
        print("✅ 未发现await在非async函数中的使用问题")
    else:
        print(f"⚠️ 发现 {len(issues)} 个await使用问题:")
        print()
        
        for i, issue in enumerate(issues, 1):
            print(f"问题 {i}:")
            print(f"  函数: {issue['function_name']} (第{issue['function_line']}行)")
            print(f"  位置: 第{issue['line_number']}行")
            print(f"  内容: {issue['line_content']}")
            print()
    
    print("=" * 50)
    return len(issues) == 0

if __name__ == "__main__":
    success = main()
    if not success:
        print("❌ 需要修复这些问题")
    else:
        print("🎉 检查完成，无问题")
