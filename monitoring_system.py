#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 WMZC监控系统完善模块
日志系统优化、性能指标监控、健康检查机制
"""

import logging
import time
import json
import threading
import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from collections import deque, defaultdict
from datetime import datetime, timedelta
from pathlib import Path
import queue

@dataclass
class LogEntry:
    """日志条目"""
    timestamp: float
    level: str
    message: str
    module: str = ""
    function: str = ""
    line_number: int = 0
    extra_data: Dict = field(default_factory=dict)

@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    unit: str
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)

@dataclass
class HealthCheckResult:
    """健康检查结果"""
    component: str
    status: str  # healthy, warning, critical
    message: str
    timestamp: float
    metrics: Dict[str, Any] = field(default_factory=dict)

class AdvancedLogger:
    """高级日志系统"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 日志队列
        self.log_queue = queue.Queue(maxsize=10000)
        self.log_history = deque(maxlen=1000)
        
        # 日志处理线程
        self.logging_thread = None
        self.running = False
        
        # 日志级别映射
        self.level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        
        # 设置日志格式
        self._setup_loggers()
        
        # 统计信息
        self.stats = defaultdict(int)
    
    def _setup_loggers(self):
        """设置日志记录器 - 🔧 Bug修复: 添加资源管理"""
        # 主日志文件
        self.main_logger = logging.getLogger('wmzc_main')
        self.main_logger.setLevel(logging.DEBUG)

        # 🔧 Bug修复: 存储处理器引用以便后续清理
        self.handlers = []

        # 文件处理器
        main_handler = logging.FileHandler(
            self.log_dir / f"wmzc_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        )
        main_handler.setLevel(logging.DEBUG)
        self.handlers.append(main_handler)

        # 错误日志文件
        error_handler = logging.FileHandler(
            self.log_dir / f"wmzc_error_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        self.handlers.append(error_handler)

        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        main_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)

        # 添加处理器
        self.main_logger.addHandler(main_handler)
        self.main_logger.addHandler(error_handler)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.main_logger.addHandler(console_handler)
        self.handlers.append(console_handler)
    
    def start(self):
        """启动日志系统"""
        if self.running:
            return
        
        self.running = True
        self.logging_thread = threading.Thread(
            target=self._log_processor,
            daemon=True
        )
        self.logging_thread.start()
        self.log("📊 高级日志系统已启动", "INFO")
    
    def stop(self):
        """停止日志系统 - 🔧 Bug修复: 添加资源清理"""
        if not self.running:
            return

        self.running = False
        if self.logging_thread:
            self.logging_thread.join(timeout=5)

        # 🔧 Bug修复: 清理文件处理器以防止资源泄漏
        self._cleanup_handlers()

        self.log("🛑 高级日志系统已停止", "INFO")

    def _cleanup_handlers(self):
        """🔧 P1-2修复: 完全重构资源清理，确保所有资源完全释放"""
        cleanup_errors = []

        try:
            # 🔧 P1-2修复: 首先停止日志处理线程
            self._stop_logging_thread()

            # 🔧 P1-2修复: 清理日志队列
            self._cleanup_log_queue()

            # 🔧 P1-2修复: 清理紧急缓存
            self._cleanup_emergency_cache()

            # 清理处理器
            if hasattr(self, 'handlers') and self.handlers:
                for i, handler in enumerate(self.handlers):
                    try:
                        # 🔧 P1-2修复: 多次尝试刷新，确保数据写入
                        for attempt in range(3):
                            try:
                                if hasattr(handler, 'flush'):
                                    handler.flush()
                                break
                            except Exception as e:
                                if attempt == 2:  # 最后一次尝试
                                    cleanup_errors.append(f"处理器{i}刷新失败: {e}")

                        # 🔧 P1-2修复: 安全关闭处理器，多次尝试
                        for attempt in range(3):
                            try:
                                if hasattr(handler, 'close'):
                                    handler.close()
                                break
                            except Exception as e:
                                if attempt == 2:
                                    cleanup_errors.append(f"处理器{i}关闭失败: {e}")

                        # 从所有logger中移除
                        self._remove_handler_from_loggers(handler)

                    except Exception as e:
                        cleanup_errors.append(f"处理器{i}清理失败: {e}")

                # 清空处理器列表
                self.handlers.clear()

            # 🔧 P1-2修复: 强制垃圾回收，多次执行确保清理
            import gc
            for _ in range(3):
                gc.collect()

        except Exception as e:
            cleanup_errors.append(f"清理处理器主流程异常: {e}")

        # 报告清理结果
        if cleanup_errors:
            for error in cleanup_errors:
                print(f"⚠️ {error}")
        else:
            print("✅ 日志处理器清理完成")

    def _stop_logging_thread(self):
        """🔧 P1-2修复: 安全停止日志处理线程"""
        try:
            if hasattr(self, 'logging_thread') and self.logging_thread:
                # 设置停止标志
                self.running = False

                # 等待线程结束，最多等待5秒
                if self.logging_thread.is_alive():
                    self.logging_thread.join(timeout=5.0)

                    # 如果线程仍在运行，强制标记为非活跃
                    if self.logging_thread.is_alive():
                        print("⚠️ 日志线程未能在5秒内停止")
                    else:
                        print("✅ 日志线程已安全停止")

                self.logging_thread = None

        except Exception as e:
            print(f"❌ 停止日志线程失败: {e}")

    def _cleanup_log_queue(self):
        """🔧 P1-2修复: 清理日志队列，避免内存泄漏"""
        try:
            if hasattr(self, 'log_queue'):
                # 统计队列中剩余的日志数量
                remaining_count = self.log_queue.qsize()

                # 清空队列
                while not self.log_queue.empty():
                    try:
                        self.log_queue.get_nowait()
                    except queue.Empty:
                        break

                print(f"✅ 清理了{remaining_count}个未处理的日志条目")

        except Exception as e:
            print(f"❌ 清理日志队列失败: {e}")

    def _cleanup_emergency_cache(self):
        """🔧 P1-2修复: 清理紧急缓存"""
        try:
            if hasattr(self, 'emergency_cache'):
                cache_size = len(self.emergency_cache)
                self.emergency_cache.clear()
                print(f"✅ 清理了{cache_size}个紧急缓存条目")

        except Exception as e:
            print(f"❌ 清理紧急缓存失败: {e}")

    def _remove_handler_from_loggers(self, handler):
        """🔧 P1-2修复: 从所有logger中安全移除处理器"""
        try:
            # 从主logger中移除
            if hasattr(self, 'main_logger') and self.main_logger:
                try:
                    self.main_logger.removeHandler(handler)
                except:
                    pass

            # 从错误logger中移除
            if hasattr(self, 'error_logger') and self.error_logger:
                try:
                    self.error_logger.removeHandler(handler)
                except:
                    pass

            # 从根logger中移除（如果被添加过）
            import logging
            try:
                logging.getLogger().removeHandler(handler)
            except:
                pass

        except Exception as e:
            print(f"❌ 从logger移除处理器失败: {e}")
    
    def log(self, message: str, level: str = "INFO", **kwargs):
        """记录日志"""
        try:
            log_entry = LogEntry(
                timestamp=time.time(),
                level=level.upper(),
                message=message,
                module=kwargs.get('module', ''),
                function=kwargs.get('function', ''),
                line_number=kwargs.get('line_number', 0),
                extra_data=kwargs.get('extra_data', {})
            )
            
            # 🔧 Bug修复: 改进队列满时的处理策略
            try:
                # 尝试非阻塞添加到队列
                self.log_queue.put_nowait(log_entry)
            except queue.Full:
                # 🔧 P1级别Bug修复: 改进队列满时的处理策略，避免重要日志丢失
                self.stats['queue_full_count'] = self.stats.get('queue_full_count', 0) + 1

                # 根据日志级别决定处理策略
                if level in ['ERROR', 'CRITICAL']:
                    # 错误和严重日志：强制保留，移除低优先级日志
                    removed_count = 0
                    max_remove = min(3, self.log_queue.qsize() // 3)  # 最多移除1/3

                    for _ in range(max_remove):
                        try:
                            old_entry = self.log_queue.get_nowait()
                            # 如果移除的也是高优先级日志，尝试保留
                            if old_entry.get('level') not in ['ERROR', 'CRITICAL']:
                                removed_count += 1
                            else:
                                # 高优先级日志放回去
                                try:
                                    self.log_queue.put_nowait(old_entry)
                                except queue.Full:
                                    pass  # 如果放不回去就丢弃
                                break
                        except queue.Empty:
                            break

                    # 尝试添加当前重要日志
                    try:
                        self.log_queue.put_nowait(log_entry)
                        self.stats['high_priority_saved'] = self.stats.get('high_priority_saved', 0) + 1
                    except queue.Full:
                        # 仍然满，记录到紧急缓存
                        if not hasattr(self, 'emergency_cache'):
                            self.emergency_cache = []
                        if len(self.emergency_cache) < 100:  # 限制紧急缓存大小
                            self.emergency_cache.append(log_entry)
                        self.stats['emergency_cached'] = self.stats.get('emergency_cached', 0) + 1

                else:
                    # 普通日志：简单移除最旧的
                    try:
                        self.log_queue.get_nowait()  # 移除最旧的
                        self.log_queue.put_nowait(log_entry)  # 添加新的
                        self.stats['dropped_logs'] = self.stats.get('dropped_logs', 0) + 1
                    except queue.Empty:
                        # 如果队列为空（竞争条件），直接添加
                        try:
                            self.log_queue.put_nowait(log_entry)
                        except queue.Full:
                            # 如果仍然满，记录到统计
                            self.stats['queue_full_errors'] = self.stats.get('queue_full_errors', 0) + 1

            # 添加到历史记录
            self.log_history.append(log_entry)

            # 更新统计
            self.stats[level.upper()] += 1
            self.stats['total'] += 1
            
        except Exception as e:
            print(f"日志记录失败: {e}")
    
    def _log_processor(self):
        """日志处理线程"""
        while self.running:
            try:
                # 批量处理日志
                entries = []
                timeout = 1.0
                
                # 收集一批日志
                try:
                    entry = self.log_queue.get(timeout=timeout)
                    entries.append(entry)
                    
                    # 尝试获取更多日志（非阻塞）
                    while len(entries) < 100:
                        try:
                            entry = self.log_queue.get_nowait()
                            entries.append(entry)
                        except queue.Empty:
                            break
                
                except queue.Empty:
                    continue
                
                # 批量写入日志
                for entry in entries:
                    self._write_log_entry(entry)
                
            except Exception as e:
                print(f"日志处理错误: {e}")
    
    def _write_log_entry(self, entry: LogEntry):
        """写入日志条目"""
        try:
            level_num = self.level_map.get(entry.level, logging.INFO)
            
            # 构建日志消息
            message = entry.message
            if entry.extra_data:
                message += f" | 额外数据: {json.dumps(entry.extra_data, ensure_ascii=False)}"
            
            # 写入日志
            self.main_logger.log(level_num, message)
            
        except Exception as e:
            print(f"写入日志失败: {e}")
    
    def get_recent_logs(self, count: int = 100, level: Optional[str] = None) -> List[LogEntry]:
        """获取最近的日志"""
        logs = list(self.log_history)
        
        if level:
            logs = [log for log in logs if log.level == level.upper()]
        
        return logs[-count:]
    
    def get_stats(self) -> Dict[str, int]:
        """获取日志统计"""
        return dict(self.stats)

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_metrics: int = 10000):
        self.max_metrics = max_metrics
        self.metrics = deque(maxlen=max_metrics)
        self.metric_types = defaultdict(deque)
        self.lock = threading.Lock()
    
    def record_metric(self, name: str, value: float, unit: str = "", tags: Optional[Dict] = None):
        """记录指标"""
        with self.lock:
            metric = PerformanceMetric(
                name=name,
                value=value,
                unit=unit,
                timestamp=time.time(),
                tags=tags or {}
            )
            
            self.metrics.append(metric)
            self.metric_types[name].append(metric)
            
            # 限制每种指标的数量
            if len(self.metric_types[name]) > 1000:
                self.metric_types[name].popleft()
    
    def get_metrics(self, name: Optional[str] = None, 
                   start_time: Optional[float] = None,
                   end_time: Optional[float] = None) -> List[PerformanceMetric]:
        """获取指标"""
        with self.lock:
            if name:
                metrics = list(self.metric_types.get(name, []))
            else:
                metrics = list(self.metrics)
            
            # 时间过滤
            if start_time or end_time:
                filtered_metrics = []
                for metric in metrics:
                    if start_time and metric.timestamp < start_time:
                        continue
                    if end_time and metric.timestamp > end_time:
                        continue
                    filtered_metrics.append(metric)
                metrics = filtered_metrics
            
            return metrics
    
    def get_metric_summary(self, name: str, duration_minutes: int = 60) -> Dict:
        """获取指标摘要"""
        end_time = time.time()
        start_time = end_time - (duration_minutes * 60)
        
        metrics = self.get_metrics(name, start_time, end_time)
        if not metrics:
            return {}
        
        values = [m.value for m in metrics]
        return {
            'count': len(values),
            'avg': sum(values) / len(values),
            'min': min(values),
            'max': max(values),
            'latest': values[-1] if values else 0,
            'start_time': start_time,
            'end_time': end_time
        }

class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.checks = {}
        self.results = deque(maxlen=1000)
        self.running = False
        self.check_thread = None
        self.check_interval = 60  # 默认60秒检查一次
    
    def register_check(self, name: str, check_func: Callable, interval: int = 60):
        """注册健康检查"""
        self.checks[name] = {
            'func': check_func,
            'interval': interval,
            'last_check': 0
        }
    
    def start(self):
        """启动健康检查"""
        if self.running:
            return
        
        self.running = True
        self.check_thread = threading.Thread(
            target=self._check_loop,
            daemon=True
        )
        self.check_thread.start()
    
    def stop(self):
        """停止健康检查"""
        self.running = False
        if self.check_thread:
            self.check_thread.join(timeout=5)
    
    def _check_loop(self):
        """健康检查循环"""
        while self.running:
            try:
                current_time = time.time()
                
                for name, check_info in self.checks.items():
                    # 检查是否需要执行
                    if current_time - check_info['last_check'] >= check_info['interval']:
                        try:
                            result = check_info['func']()
                            if isinstance(result, dict):
                                health_result = HealthCheckResult(
                                    component=name,
                                    status=result.get('status', 'unknown'),
                                    message=result.get('message', ''),
                                    timestamp=current_time,
                                    metrics=result.get('metrics', {})
                                )
                            else:
                                health_result = HealthCheckResult(
                                    component=name,
                                    status='healthy' if result else 'critical',
                                    message=str(result),
                                    timestamp=current_time
                                )
                            
                            self.results.append(health_result)
                            check_info['last_check'] = current_time
                            
                        except Exception as e:
                            error_result = HealthCheckResult(
                                component=name,
                                status='critical',
                                message=f"健康检查失败: {e}",
                                timestamp=current_time
                            )
                            self.results.append(error_result)
                            check_info['last_check'] = current_time
                
                time.sleep(min(self.check_interval, 10))
                
            except Exception as e:
                print(f"健康检查循环错误: {e}")
                time.sleep(10)
    
    def get_health_status(self) -> Dict:
        """获取健康状态"""
        if not self.results:
            return {'overall': 'unknown', 'components': {}}
        
        # 获取最新的检查结果
        latest_results = {}
        for result in reversed(self.results):
            if result.component not in latest_results:
                latest_results[result.component] = result
        
        # 计算整体状态
        statuses = [r.status for r in latest_results.values()]
        if 'critical' in statuses:
            overall = 'critical'
        elif 'warning' in statuses:
            overall = 'warning'
        elif 'healthy' in statuses:
            overall = 'healthy'
        else:
            overall = 'unknown'
        
        return {
            'overall': overall,
            'components': {name: result.__dict__ for name, result in latest_results.items()},
            'last_check': max(r.timestamp for r in latest_results.values()) if latest_results else 0
        }

class MonitoringSystem:
    """监控系统主类"""
    
    def __init__(self):
        self.logger = AdvancedLogger()
        self.metrics = MetricsCollector()
        self.health_checker = HealthChecker()
        
        # 注册基础健康检查
        self._register_basic_health_checks()
    
    def start(self):
        """启动监控系统"""
        self.logger.start()
        self.health_checker.start()
        self.logger.log("🚀 监控系统已启动", "INFO")
    
    def stop(self):
        """停止监控系统"""
        self.health_checker.stop()
        self.logger.stop()
        print("🛑 监控系统已停止")
    
    def _register_basic_health_checks(self):
        """注册基础健康检查"""
        
        def check_memory():
            try:
                import psutil
                memory = psutil.virtual_memory()
                if memory.percent > 90:
                    return {'status': 'critical', 'message': f'内存使用率过高: {memory.percent:.1f}%'}
                elif memory.percent > 80:
                    return {'status': 'warning', 'message': f'内存使用率警告: {memory.percent:.1f}%'}
                else:
                    return {'status': 'healthy', 'message': f'内存使用率正常: {memory.percent:.1f}%'}
            except Exception as e:
                return {'status': 'critical', 'message': f'内存检查失败: {e}'}
        
        def check_disk():
            try:
                import psutil
                disk = psutil.disk_usage('.')
                percent = (disk.used / disk.total) * 100
                if percent > 90:
                    return {'status': 'critical', 'message': f'磁盘使用率过高: {percent:.1f}%'}
                elif percent > 80:
                    return {'status': 'warning', 'message': f'磁盘使用率警告: {percent:.1f}%'}
                else:
                    return {'status': 'healthy', 'message': f'磁盘使用率正常: {percent:.1f}%'}
            except Exception as e:
                return {'status': 'critical', 'message': f'磁盘检查失败: {e}'}
        
        self.health_checker.register_check('memory', check_memory, 30)
        self.health_checker.register_check('disk', check_disk, 60)
    
    def log(self, message: str, level: str = "INFO", **kwargs):
        """记录日志"""
        self.logger.log(message, level, **kwargs)
    
    def record_metric(self, name: str, value: float, unit: str = "", tags: Optional[Dict] = None):
        """记录指标"""
        self.metrics.record_metric(name, value, unit, tags)
    
    def get_dashboard_data(self) -> Dict:
        """获取仪表板数据"""
        return {
            'health': self.health_checker.get_health_status(),
            'log_stats': self.logger.get_stats(),
            'recent_logs': [log.__dict__ for log in self.logger.get_recent_logs(50)],
            'metrics_summary': {
                'trading_loop_duration': self.metrics.get_metric_summary('trading_loop_duration'),
                'api_response_time': self.metrics.get_metric_summary('api_response_time'),
                'memory_usage': self.metrics.get_metric_summary('memory_usage'),
                'cache_hit_rate': self.metrics.get_metric_summary('cache_hit_rate')
            },
            'timestamp': time.time()
        }

# 全局监控系统实例
monitoring_system = MonitoringSystem()

# 兼容性函数
def log(message: str, level: str = "INFO", **kwargs):
    """全局日志函数"""
    monitoring_system.log(message, level, **kwargs)

def record_metric(name: str, value: float, unit: str = "", tags: Optional[Dict] = None):
    """全局指标记录函数"""
    monitoring_system.record_metric(name, value, unit, tags)
