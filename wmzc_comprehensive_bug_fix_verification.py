#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC系统全面Bug修复验证脚本
验证P0级别bug的修复效果
"""

import os
import re
import sys
import ast
import time
from typing import Dict, List, Tuple

class WMZCBugFixVerifier:
    """WMZC Bug修复验证器"""
    
    def __init__(self):
        self.core_files = [
            'WMZC.py',
            'Global_Position_Controller.py',
            'trading_loop_modules.py',
            'monitoring_system.py',
            'performance_optimizer.py',
            'batch_order_manager.py',
            'order_book_manager.py',
            'exchange_rate_limiter.py',
            'smart_retry_handler.py',
            'optimization_config_parameters.py'
        ]
        self.verification_results = {}
        
    def verify_p0_fixes(self):
        """验证P0级别bug修复"""
        print("🔍 验证P0级别Bug修复...")
        print("=" * 60)
        
        # 验证1: 重复导入修复
        self.verify_duplicate_imports()
        
        # 验证2: 注释代码清理
        self.verify_commented_code_cleanup()
        
        # 验证3: DataFrame歧义性检查
        self.verify_dataframe_ambiguity_fixes()
        
        # 生成总结报告
        self.generate_verification_report()
        
    def verify_duplicate_imports(self):
        """验证重复导入修复"""
        print("📋 1. 验证重复导入修复...")
        
        results = {}
        for file_path in self.core_files:
            if not os.path.exists(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查threading重复导入
                threading_imports = re.findall(r'import threading', content)
                
                # 统计函数内部的重复导入
                function_imports = re.findall(r'^\s+import threading', content, re.MULTILINE)
                
                results[file_path] = {
                    'total_threading_imports': len(threading_imports),
                    'function_level_imports': len(function_imports),
                    'status': 'FIXED' if len(function_imports) == 0 else 'NEEDS_FIX'
                }
                
                if len(function_imports) > 0:
                    print(f"  ⚠️ {file_path}: 仍有{len(function_imports)}个函数级threading导入")
                else:
                    print(f"  ✅ {file_path}: 重复导入已清理")
                    
            except Exception as e:
                results[file_path] = {'error': str(e)}
                print(f"  ❌ {file_path}: 检查失败 - {e}")
        
        self.verification_results['duplicate_imports'] = results
        
    def verify_commented_code_cleanup(self):
        """验证注释代码清理"""
        print("\n📋 2. 验证注释代码清理...")
        
        results = {}
        for file_path in self.core_files:
            if not os.path.exists(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查被注释的time.sleep调用
                commented_sleep_patterns = [
                    r'#.*已移除time\.sleep',
                    r'#.*time\.sleep.*await asyncio\.sleep',
                    r'#.*🔧.*time\.sleep'
                ]
                
                total_commented_sleep = 0
                for pattern in commented_sleep_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    total_commented_sleep += len(matches)
                
                # 检查其他大块注释代码
                large_comment_blocks = re.findall(r'#.*\n(#.*\n){5,}', content)
                
                results[file_path] = {
                    'commented_sleep_calls': total_commented_sleep,
                    'large_comment_blocks': len(large_comment_blocks),
                    'status': 'NEEDS_CLEANUP' if total_commented_sleep > 10 else 'GOOD'
                }
                
                if total_commented_sleep > 10:
                    print(f"  ⚠️ {file_path}: 仍有{total_commented_sleep}个注释的sleep调用")
                else:
                    print(f"  ✅ {file_path}: 注释代码清理良好")
                    
            except Exception as e:
                results[file_path] = {'error': str(e)}
                print(f"  ❌ {file_path}: 检查失败 - {e}")
        
        self.verification_results['commented_code'] = results
        
    def verify_dataframe_ambiguity_fixes(self):
        """验证DataFrame歧义性修复"""
        print("\n📋 3. 验证DataFrame歧义性修复...")
        
        results = {}
        for file_path in self.core_files:
            if not os.path.exists(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查潜在的DataFrame布尔值判断问题
                risky_patterns = [
                    r'if\s+df\s*:',  # if df:
                    r'if\s+.*dataframe\s*:',  # if dataframe:
                    r'if\s+not\s+df\s*:',  # if not df:
                    r'while\s+df\s*:',  # while df:
                ]
                
                safe_patterns = [
                    r'if\s+df\s+is\s+None',  # if df is None
                    r'if\s+df\.empty',  # if df.empty
                    r'if\s+not\s+df\.empty',  # if not df.empty
                    r'if\s+isinstance\(df',  # if isinstance(df
                    r'if\s+len\(df\)',  # if len(df)
                ]
                
                risky_count = 0
                safe_count = 0
                
                for pattern in risky_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    risky_count += len(matches)
                
                for pattern in safe_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    safe_count += len(matches)
                
                results[file_path] = {
                    'risky_dataframe_checks': risky_count,
                    'safe_dataframe_checks': safe_count,
                    'safety_ratio': safe_count / (risky_count + safe_count) if (risky_count + safe_count) > 0 else 1.0,
                    'status': 'GOOD' if risky_count < 5 else 'NEEDS_REVIEW'
                }
                
                if risky_count > 5:
                    print(f"  ⚠️ {file_path}: 发现{risky_count}个潜在的DataFrame歧义性问题")
                else:
                    print(f"  ✅ {file_path}: DataFrame检查安全 (风险:{risky_count}, 安全:{safe_count})")
                    
            except Exception as e:
                results[file_path] = {'error': str(e)}
                print(f"  ❌ {file_path}: 检查失败 - {e}")
        
        self.verification_results['dataframe_ambiguity'] = results
        
    def verify_syntax_correctness(self):
        """验证语法正确性"""
        print("\n📋 4. 验证语法正确性...")
        
        results = {}
        for file_path in self.core_files:
            if not os.path.exists(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 尝试解析AST
                try:
                    ast.parse(content)
                    results[file_path] = {'syntax_valid': True, 'error': None}
                    print(f"  ✅ {file_path}: 语法正确")
                except SyntaxError as e:
                    results[file_path] = {'syntax_valid': False, 'error': str(e)}
                    print(f"  ❌ {file_path}: 语法错误 - {e}")
                    
            except Exception as e:
                results[file_path] = {'syntax_valid': False, 'error': str(e)}
                print(f"  ❌ {file_path}: 检查失败 - {e}")
        
        self.verification_results['syntax'] = results
        
    def generate_verification_report(self):
        """生成验证报告"""
        print("\n" + "=" * 60)
        print("📊 Bug修复验证总结报告")
        print("=" * 60)
        
        # 统计总体情况
        total_files = len([f for f in self.core_files if os.path.exists(f)])
        
        # 重复导入修复统计
        import_fixed = sum(1 for result in self.verification_results.get('duplicate_imports', {}).values() 
                          if isinstance(result, dict) and result.get('status') == 'FIXED')
        
        # 注释代码清理统计
        comment_good = sum(1 for result in self.verification_results.get('commented_code', {}).values() 
                          if isinstance(result, dict) and result.get('status') == 'GOOD')
        
        # DataFrame安全性统计
        df_safe = sum(1 for result in self.verification_results.get('dataframe_ambiguity', {}).values() 
                     if isinstance(result, dict) and result.get('status') == 'GOOD')
        
        print(f"📁 检查文件总数: {total_files}")
        print(f"🔧 重复导入修复: {import_fixed}/{total_files} ({import_fixed/total_files*100:.1f}%)")
        print(f"🧹 注释代码清理: {comment_good}/{total_files} ({comment_good/total_files*100:.1f}%)")
        print(f"🛡️ DataFrame安全性: {df_safe}/{total_files} ({df_safe/total_files*100:.1f}%)")
        
        # 总体评分
        overall_score = (import_fixed + comment_good + df_safe) / (total_files * 3) * 100
        
        print(f"\n🎯 总体修复评分: {overall_score:.1f}%")
        
        if overall_score >= 90:
            print("✅ 优秀！P0级别bug修复效果很好")
        elif overall_score >= 70:
            print("⚠️ 良好，但仍有改进空间")
        else:
            print("❌ 需要进一步修复")
            
        # 建议
        print("\n💡 修复建议:")
        if import_fixed < total_files:
            print("  - 继续清理重复导入")
        if comment_good < total_files:
            print("  - 清理更多注释代码")
        if df_safe < total_files:
            print("  - 改进DataFrame安全检查")
            
    def run_verification(self):
        """运行完整验证"""
        print("🚀 开始WMZC系统Bug修复验证")
        print(f"⏰ 验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 运行P0级别验证
        self.verify_p0_fixes()
        
        # 运行语法验证
        self.verify_syntax_correctness()
        
        print("\n🎉 验证完成！")

def main():
    """主函数"""
    verifier = WMZCBugFixVerifier()
    verifier.run_verification()

if __name__ == "__main__":
    main()
