#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证WMZC.py异步修复效果的脚本
"""

import re
import ast

def verify_async_fixes():
    """验证异步修复效果"""
    print("🔍 验证WMZC.py异步修复效果...")
    
    # 读取WMZC.py文件
    try:
        with open('WMZC.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 无法读取WMZC.py: {e}")
        return
    
    # 检查修复项目
    fixes_verified = []
    
    # 1. 检查HTTP请求异步化
    print("\n1. 检查HTTP请求异步化...")
    
    # 检查aiohttp的使用
    if 'import aiohttp' in content:
        fixes_verified.append("✅ 已导入aiohttp异步HTTP库")
    else:
        fixes_verified.append("⚠️ 未找到aiohttp导入")
    
    # 检查异步HTTP请求模式
    if 'async with aiohttp.ClientSession' in content:
        fixes_verified.append("✅ 使用了异步HTTP会话管理")
    else:
        fixes_verified.append("⚠️ 未找到异步HTTP会话")
    
    # 检查线程池执行器的使用
    if 'ThreadPoolExecutor' in content and 'run_in_executor' in content:
        fixes_verified.append("✅ 使用线程池执行器处理同步代码")
    else:
        fixes_verified.append("⚠️ 未找到线程池执行器")
    
    # 2. 检查同步阻塞操作
    print("\n2. 检查同步阻塞操作...")
    
    # 统计requests.get/post的使用
    requests_get_count = len(re.findall(r'requests\.get\(', content))
    requests_post_count = len(re.findall(r'requests\.post\(', content))
    
    if requests_get_count == 0 and requests_post_count == 0:
        fixes_verified.append("✅ 已移除所有同步requests调用")
    else:
        fixes_verified.append(f"⚠️ 仍有 {requests_get_count + requests_post_count} 个同步requests调用")
    
    # 统计urllib.request.urlopen的使用
    urllib_count = len(re.findall(r'urllib\.request\.urlopen\(', content))
    if urllib_count == 0:
        fixes_verified.append("✅ 已移除所有同步urllib调用")
    else:
        fixes_verified.append(f"⚠️ 仍有 {urllib_count} 个同步urllib调用")
    
    # 3. 检查time.sleep的使用
    print("\n3. 检查time.sleep使用情况...")
    
    # 查找所有time.sleep
    time_sleep_matches = re.finditer(r'time\.sleep\(', content)
    time_sleep_lines = []
    
    lines = content.split('\n')
    for match in time_sleep_matches:
        # 找到行号
        line_num = content[:match.start()].count('\n') + 1
        time_sleep_lines.append(line_num)
    
    # 检查这些time.sleep是否在异步函数中
    async_function_violations = []
    
    try:
        tree = ast.parse(content)
        
        # 遍历AST查找异步函数
        for node in ast.walk(tree):
            if isinstance(node, ast.AsyncFunctionDef):
                func_start_line = node.lineno
                func_end_line = node.end_lineno if hasattr(node, 'end_lineno') else func_start_line + 50
                
                # 检查这个异步函数中是否有time.sleep
                for sleep_line in time_sleep_lines:
                    if func_start_line <= sleep_line <= func_end_line:
                        # 检查这行是否有注释说明是正确的
                        line_content = lines[sleep_line - 1] if sleep_line <= len(lines) else ""
                        if "在同步函数中使用同步sleep" not in line_content and "在线程中使用同步sleep" not in line_content:
                            async_function_violations.append({
                                'function': node.name,
                                'line': sleep_line,
                                'content': line_content.strip()
                            })
    
    except Exception as e:
        print(f"⚠️ AST解析失败: {e}")
    
    if not async_function_violations:
        fixes_verified.append("✅ 异步函数中没有发现time.sleep违规")
    else:
        fixes_verified.append(f"⚠️ 发现 {len(async_function_violations)} 个异步函数中的time.sleep违规")
        for violation in async_function_violations:
            print(f"   - 函数 {violation['function']} 第{violation['line']}行: {violation['content']}")
    
    # 4. 检查asyncio.sleep的使用
    print("\n4. 检查asyncio.sleep使用情况...")
    
    asyncio_sleep_count = len(re.findall(r'await asyncio\.sleep\(', content))
    if asyncio_sleep_count > 0:
        fixes_verified.append(f"✅ 正确使用了 {asyncio_sleep_count} 次 await asyncio.sleep()")
    else:
        fixes_verified.append("⚠️ 未找到await asyncio.sleep()的使用")
    
    # 5. 语法检查
    print("\n5. 进行语法检查...")
    
    try:
        ast.parse(content)
        fixes_verified.append("✅ 语法检查通过")
    except SyntaxError as e:
        fixes_verified.append(f"❌ 语法错误: 第{e.lineno}行 - {e.msg}")
    
    # 输出验证结果
    print("\n" + "="*60)
    print("🎯 异步修复验证结果")
    print("="*60)
    
    success_count = 0
    for result in fixes_verified:
        print(result)
        if result.startswith("✅"):
            success_count += 1
    
    total_checks = len(fixes_verified)
    success_rate = (success_count / total_checks) * 100 if total_checks > 0 else 0
    
    print(f"\n📊 修复成功率: {success_rate:.1f}% ({success_count}/{total_checks})")
    
    if success_rate >= 80:
        print("🎉 异步修复效果良好！")
    elif success_rate >= 60:
        print("👍 异步修复基本完成，还有改进空间")
    else:
        print("⚠️ 异步修复需要进一步完善")
    
    return success_rate

if __name__ == "__main__":
    verify_async_fixes()
