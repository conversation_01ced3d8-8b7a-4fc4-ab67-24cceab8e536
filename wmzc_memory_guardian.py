#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ WMZC内存守护者
持续监控内存使用，防止336MB泄漏导致系统退出
"""

import os
import gc
import sys
import time
import psutil
import threading
import traceback
from datetime import datetime
from collections import deque

class WMZCMemoryGuardian:
    """WMZC内存守护者 - 防止内存泄漏导致系统退出"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.baseline_memory = self.process.memory_info().rss
        self.monitoring = False
        self.monitor_thread = None
        
        # 关键阈值设置 (基于336MB系统退出阈值)
        self.warning_threshold = 150 * 1024 * 1024   # 150MB 警告
        self.danger_threshold = 250 * 1024 * 1024    # 250MB 危险
        self.critical_threshold = 320 * 1024 * 1024  # 320MB 临界 (留16MB缓冲)
        
        # 监控历史
        self.memory_history = deque(maxlen=100)
        self.cleanup_history = deque(maxlen=50)
        
        # 统计信息
        self.stats = {
            'total_cleanups': 0,
            'memory_saved_mb': 0,
            'max_memory_seen': self.baseline_memory,
            'last_cleanup_time': None
        }
        
        print(f"🛡️ WMZC内存守护者初始化")
        print(f"📊 基线内存: {self.baseline_memory / 1024 / 1024:.1f} MB")
        print(f"⚠️ 警告阈值: {self.warning_threshold / 1024 / 1024:.1f} MB")
        print(f"🚨 危险阈值: {self.danger_threshold / 1024 / 1024:.1f} MB")
        print(f"💀 临界阈值: {self.critical_threshold / 1024 / 1024:.1f} MB")
    
    def start_monitoring(self, interval=30):
        """启动内存监控"""
        if self.monitoring:
            print("⚠️ 内存监控已在运行")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        print(f"🚀 内存监控已启动，检查间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止内存监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print("🛑 内存监控已停止")
    
    def _monitor_loop(self, interval):
        """监控循环"""
        while self.monitoring:
            try:
                current_memory = self.process.memory_info().rss
                memory_growth = current_memory - self.baseline_memory
                
                # 记录历史
                self.memory_history.append({
                    'timestamp': time.time(),
                    'memory_mb': current_memory / 1024 / 1024,
                    'growth_mb': memory_growth / 1024 / 1024
                })
                
                # 更新统计
                if current_memory > self.stats['max_memory_seen']:
                    self.stats['max_memory_seen'] = current_memory
                
                # 检查阈值并执行相应操作
                if memory_growth > self.critical_threshold:
                    self._handle_critical_memory(memory_growth)
                elif memory_growth > self.danger_threshold:
                    self._handle_dangerous_memory(memory_growth)
                elif memory_growth > self.warning_threshold:
                    self._handle_warning_memory(memory_growth)
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"❌ 内存监控异常: {e}")
                time.sleep(interval)
    
    def _handle_critical_memory(self, memory_growth):
        """处理临界内存状态 (>320MB)"""
        print(f"💀 临界内存警报: {memory_growth / 1024 / 1024:.1f} MB")
        print(f"🚨 距离系统退出阈值(336MB)仅剩: {(336 * 1024 * 1024 - memory_growth) / 1024 / 1024:.1f} MB")
        
        # 执行紧急清理
        saved_memory = self._emergency_cleanup()
        
        # 如果清理后仍然危险，建议重启
        current_memory = self.process.memory_info().rss
        current_growth = current_memory - self.baseline_memory
        
        if current_growth > self.danger_threshold:
            print(f"🚨 紧急建议: 立即重启WMZC系统以避免自动退出")
            self._log_restart_recommendation()
    
    def _handle_dangerous_memory(self, memory_growth):
        """处理危险内存状态 (250-320MB)"""
        print(f"🚨 危险内存警报: {memory_growth / 1024 / 1024:.1f} MB")
        
        # 执行积极清理
        saved_memory = self._aggressive_cleanup()
        print(f"🧹 积极清理完成，释放内存: {saved_memory / 1024 / 1024:.1f} MB")
    
    def _handle_warning_memory(self, memory_growth):
        """处理警告内存状态 (150-250MB)"""
        print(f"⚠️ 内存使用警告: {memory_growth / 1024 / 1024:.1f} MB")
        
        # 执行预防性清理
        saved_memory = self._preventive_cleanup()
        if saved_memory > 0:
            print(f"🧹 预防性清理完成，释放内存: {saved_memory / 1024 / 1024:.1f} MB")
    
    def _emergency_cleanup(self):
        """紧急内存清理"""
        print(f"🚨 执行紧急内存清理...")
        
        before_memory = self.process.memory_info().rss
        
        try:
            import WMZC
            
            # 1. 停止交易系统
            if hasattr(WMZC, 'global_state'):
                WMZC.global_state.trading_active = False
                print(f"🛑 已停止交易系统")
            
            # 2. 清理所有缓存
            self._clear_all_caches()
            
            # 3. 清理所有日志和历史数据
            self._clear_all_logs()
            
            # 4. 强制垃圾回收
            for _ in range(3):
                collected = gc.collect()
                if collected == 0:
                    break
            
            # 5. 清理大型对象
            self._clear_large_objects()
            
        except Exception as e:
            print(f"⚠️ 紧急清理部分失败: {e}")
        
        after_memory = self.process.memory_info().rss
        saved_memory = before_memory - after_memory
        
        # 记录清理历史
        self._record_cleanup('emergency', saved_memory)
        
        return max(0, saved_memory)
    
    def _aggressive_cleanup(self):
        """积极内存清理"""
        print(f"⚡ 执行积极内存清理...")
        
        before_memory = self.process.memory_info().rss
        
        try:
            import WMZC
            
            # 1. 清理主要缓存
            self._clear_major_caches()
            
            # 2. 清理历史数据
            self._clear_historical_data()
            
            # 3. 垃圾回收
            gc.collect()
            
        except Exception as e:
            print(f"⚠️ 积极清理部分失败: {e}")
        
        after_memory = self.process.memory_info().rss
        saved_memory = before_memory - after_memory
        
        # 记录清理历史
        self._record_cleanup('aggressive', saved_memory)
        
        return max(0, saved_memory)
    
    def _preventive_cleanup(self):
        """预防性内存清理"""
        before_memory = self.process.memory_info().rss
        
        try:
            import WMZC
            
            # 1. 清理过期数据
            self._clear_expired_data()
            
            # 2. 轻量级垃圾回收
            gc.collect()
            
        except Exception as e:
            print(f"⚠️ 预防性清理失败: {e}")
        
        after_memory = self.process.memory_info().rss
        saved_memory = before_memory - after_memory
        
        # 记录清理历史
        if saved_memory > 1024 * 1024:  # 只记录释放超过1MB的清理
            self._record_cleanup('preventive', saved_memory)
        
        return max(0, saved_memory)
    
    def _clear_all_caches(self):
        """清理所有缓存"""
        try:
            import WMZC
            
            cache_objects = [
                'smart_cache_manager', 'indicator_cache', 'kline_cache',
                'kdj_cache', 'macd_cache', 'rsi_cache'
            ]
            
            for cache_name in cache_objects:
                if hasattr(WMZC, cache_name):
                    cache_obj = getattr(WMZC, cache_name)
                    if hasattr(cache_obj, 'clear'):
                        cache_obj.clear()
                        print(f"  ✅ 已清理 {cache_name}")
                        
        except Exception as e:
            print(f"⚠️ 缓存清理失败: {e}")
    
    def _clear_all_logs(self):
        """清理所有日志"""
        try:
            import WMZC
            
            # 清理全局日志列表
            global_logs = ['signal_log', 'trading_records']
            for log_name in global_logs:
                if log_name in globals():
                    log_obj = globals()[log_name]
                    if isinstance(log_obj, list):
                        log_obj.clear()
                        print(f"  ✅ 已清理全局 {log_name}")
            
            # 清理WMZC对象中的日志
            wmzc_logs = ['audit_trail', 'alert_history', 'connection_errors']
            for log_name in wmzc_logs:
                if hasattr(WMZC, log_name):
                    log_obj = getattr(WMZC, log_name)
                    if hasattr(log_obj, 'clear'):
                        log_obj.clear()
                        print(f"  ✅ 已清理 {log_name}")
                        
        except Exception as e:
            print(f"⚠️ 日志清理失败: {e}")
    
    def _clear_large_objects(self):
        """清理大型对象"""
        try:
            cleared_count = 0
            
            for obj in gc.get_objects():
                try:
                    # 清理大型DataFrame
                    if hasattr(obj, 'memory_usage') and hasattr(obj, 'shape'):
                        memory_usage = obj.memory_usage(deep=True).sum()
                        if memory_usage > 5 * 1024 * 1024:  # 大于5MB
                            if hasattr(obj, 'drop'):
                                obj.drop(obj.index, inplace=True)
                                cleared_count += 1
                except Exception:
                    continue
            
            if cleared_count > 0:
                print(f"  ✅ 已清理 {cleared_count} 个大型DataFrame")
                
        except Exception as e:
            print(f"⚠️ 大型对象清理失败: {e}")
    
    def _clear_major_caches(self):
        """清理主要缓存（保留关键缓存）"""
        # 实现主要缓存清理
        self._clear_all_caches()
    
    def _clear_historical_data(self):
        """清理历史数据（保留最近数据）"""
        try:
            import WMZC
            
            # 清理信号日志，只保留最新50条
            if 'signal_log' in globals():
                signal_log = globals()['signal_log']
                if len(signal_log) > 50:
                    signal_log[:] = signal_log[-50:]
                    print(f"  ✅ signal_log清理至50条")
            
            # 清理交易记录，只保留最新50条
            if 'trading_records' in globals():
                trading_records = globals()['trading_records']
                if len(trading_records) > 50:
                    trading_records[:] = trading_records[-50:]
                    print(f"  ✅ trading_records清理至50条")
                    
        except Exception as e:
            print(f"⚠️ 历史数据清理失败: {e}")
    
    def _clear_expired_data(self):
        """清理过期数据"""
        try:
            import WMZC
            
            # 轻量级清理，只清理明显过期的数据
            current_time = time.time()
            
            # 清理内存监控历史中的旧数据
            if len(self.memory_history) > 50:
                # 只保留最近50条记录
                while len(self.memory_history) > 50:
                    self.memory_history.popleft()
                    
        except Exception as e:
            print(f"⚠️ 过期数据清理失败: {e}")
    
    def _record_cleanup(self, cleanup_type, saved_memory):
        """记录清理历史"""
        self.cleanup_history.append({
            'timestamp': time.time(),
            'type': cleanup_type,
            'saved_memory_mb': saved_memory / 1024 / 1024
        })
        
        self.stats['total_cleanups'] += 1
        self.stats['memory_saved_mb'] += saved_memory / 1024 / 1024
        self.stats['last_cleanup_time'] = time.time()
    
    def _log_restart_recommendation(self):
        """记录重启建议"""
        with open('wmzc_restart_recommendation.log', 'a', encoding='utf-8') as f:
            f.write(f"{datetime.now().isoformat()} - 建议重启WMZC系统以避免内存泄漏导致的自动退出\n")
    
    def get_status_report(self):
        """获取状态报告"""
        current_memory = self.process.memory_info().rss
        memory_growth = current_memory - self.baseline_memory
        
        return {
            'current_memory_mb': current_memory / 1024 / 1024,
            'memory_growth_mb': memory_growth / 1024 / 1024,
            'baseline_memory_mb': self.baseline_memory / 1024 / 1024,
            'max_memory_seen_mb': self.stats['max_memory_seen'] / 1024 / 1024,
            'total_cleanups': self.stats['total_cleanups'],
            'memory_saved_mb': self.stats['memory_saved_mb'],
            'monitoring_active': self.monitoring,
            'status': self._get_memory_status(memory_growth)
        }
    
    def _get_memory_status(self, memory_growth):
        """获取内存状态"""
        if memory_growth > self.critical_threshold:
            return 'CRITICAL'
        elif memory_growth > self.danger_threshold:
            return 'DANGEROUS'
        elif memory_growth > self.warning_threshold:
            return 'WARNING'
        else:
            return 'NORMAL'

def main():
    """主函数"""
    print(f"🛡️ WMZC内存守护者")
    print(f"=" * 60)
    
    guardian = WMZCMemoryGuardian()
    
    try:
        # 启动监控
        guardian.start_monitoring(interval=30)  # 每30秒检查一次
        
        print(f"💡 内存守护者已启动，将持续监控内存使用")
        print(f"💡 自动防止336MB内存泄漏导致系统退出")
        print(f"💡 按 Ctrl+C 停止监控")
        
        # 定期显示状态
        while True:
            time.sleep(300)  # 每5分钟显示一次状态
            report = guardian.get_status_report()
            print(f"\n📊 内存状态: {report['status']} | "
                  f"当前: {report['current_memory_mb']:.1f}MB | "
                  f"增长: {report['memory_growth_mb']:.1f}MB | "
                  f"清理次数: {report['total_cleanups']}")
    
    except KeyboardInterrupt:
        print(f"\n🛑 停止内存守护者...")
        guardian.stop_monitoring()
        
        # 显示最终报告
        final_report = guardian.get_status_report()
        print(f"\n📋 最终报告:")
        print(f"  总清理次数: {final_report['total_cleanups']}")
        print(f"  总释放内存: {final_report['memory_saved_mb']:.1f} MB")
        print(f"  最大内存: {final_report['max_memory_seen_mb']:.1f} MB")
        print(f"  最终状态: {final_report['status']}")
    
    except Exception as e:
        print(f"❌ 内存守护者异常: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
