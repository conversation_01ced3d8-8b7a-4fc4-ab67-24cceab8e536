#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易所选择相关异常处理修复
"""

import asyncio
import sys
import os
import traceback
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_exchange_selection_error_handler():
    """测试交易所选择异常处理器"""
    print("🧪 测试交易所选择异常处理器...")
    
    try:
        from WMZC import ExchangeSelectionErrorHandler
        
        # 创建异常处理器实例
        handler = ExchangeSelectionErrorHandler()
        
        # 测试网络错误处理
        print("\n1. 测试网络错误处理:")
        network_error = ConnectionError("网络连接超时")
        result = handler.handle_exchange_selection_error(
            network_error, "test_network", "OKX"
        )
        print(f"   结果: {result}")
        
        # 测试配置错误处理
        print("\n2. 测试配置错误处理:")
        config_error = KeyError("API_KEY")
        result = handler.handle_exchange_selection_error(
            config_error, "test_config", "Gate.io"
        )
        print(f"   结果: {result}")
        
        # 测试参数错误处理
        print("\n3. 测试参数错误处理:")
        param_error = ValueError("无效的交易对格式")
        result = handler.handle_exchange_selection_error(
            param_error, "test_param", "OKX"
        )
        print(f"   结果: {result}")
        
        # 测试记录成功交易所
        print("\n4. 测试记录成功交易所:")
        handler.record_successful_exchange("OKX")
        stats = handler.get_error_statistics()
        print(f"   统计: {stats}")
        
        print("✅ 交易所选择异常处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 交易所选择异常处理器测试失败: {e}")
        traceback.print_exc()
        return False

async def test_exchange_health_checker():
    """测试交易所健康检查器"""
    print("\n🧪 测试交易所健康检查器...")
    
    try:
        from WMZC import ExchangeHealthChecker
        
        # 创建健康检查器实例
        checker = ExchangeHealthChecker()
        
        # 测试单个交易所健康检查（模拟）
        print("\n1. 测试单个交易所健康检查:")
        
        # 由于没有真实的API连接，这里会返回错误状态
        health_result = await checker.check_exchange_health("OKX")
        print(f"   OKX健康状态: {health_result['overall_health']}")
        
        # 测试获取推荐交易所
        print("\n2. 测试获取推荐交易所:")
        recommended = checker.get_recommended_exchange()
        print(f"   推荐交易所: {recommended}")
        
        # 测试缓存功能
        print("\n3. 测试缓存功能:")
        cached = checker.get_cached_health("OKX")
        if cached:
            print(f"   缓存状态: {cached['overall_health']}")
        else:
            print("   无缓存数据")
        
        print("✅ 交易所健康检查器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 交易所健康检查器测试失败: {e}")
        traceback.print_exc()
        return False

async def test_select_best_exchange():
    """测试最佳交易所选择函数"""
    print("\n🧪 测试最佳交易所选择函数...")
    
    try:
        from WMZC import _select_best_exchange
        
        # 测试有效输入
        print("\n1. 测试有效输入:")
        exchanges = ['OKX', 'Gate.io']
        result = await _select_best_exchange('BTC-USDT', exchanges, 'best_price')
        print(f"   选择结果: {result}")
        
        # 测试空列表
        print("\n2. 测试空交易所列表:")
        result = await _select_best_exchange('BTC-USDT', [], 'best_price')
        print(f"   选择结果: {result}")
        
        # 测试无效策略
        print("\n3. 测试无效策略:")
        result = await _select_best_exchange('BTC-USDT', exchanges, 'invalid_strategy')
        print(f"   选择结果: {result}")
        
        # 测试延迟策略
        print("\n4. 测试延迟策略:")
        result = await _select_best_exchange('BTC-USDT', exchanges, 'lowest_latency')
        print(f"   选择结果: {result}")
        
        print("✅ 最佳交易所选择函数测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 最佳交易所选择函数测试失败: {e}")
        traceback.print_exc()
        return False

def test_unified_error_handler():
    """测试统一错误处理器"""
    print("\n🧪 测试统一错误处理器...")
    
    try:
        from WMZC import UnifiedErrorHandler
        
        # 创建错误处理器实例
        handler = UnifiedErrorHandler()
        
        # 测试不同严重程度的错误
        print("\n1. 测试不同严重程度的错误:")
        
        # 测试警告级别
        warning_error = ValueError("测试警告")
        result = handler.handle_error(warning_error, "test_context", "WARNING")
        print(f"   警告处理结果: {result}")
        
        # 测试错误级别
        error_error = ConnectionError("测试错误")
        result = handler.handle_error(error_error, "test_context", "ERROR")
        print(f"   错误处理结果: {result}")
        
        # 测试严重错误级别
        critical_error = Exception("测试严重错误")
        result = handler.handle_error(critical_error, "test_context", "CRITICAL")
        print(f"   严重错误处理结果: {result}")
        
        # 测试错误摘要
        print("\n2. 测试错误摘要:")
        summary = handler.get_error_summary()
        print(f"   错误摘要: {summary}")
        
        print("✅ 统一错误处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 统一错误处理器测试失败: {e}")
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试交易所选择相关异常处理修复")
    print("=" * 50)
    
    test_results = []
    
    # 测试1: 交易所选择异常处理器
    result1 = test_exchange_selection_error_handler()
    test_results.append(("交易所选择异常处理器", result1))
    
    # 测试2: 交易所健康检查器
    result2 = await test_exchange_health_checker()
    test_results.append(("交易所健康检查器", result2))
    
    # 测试3: 最佳交易所选择函数
    result3 = await test_select_best_exchange()
    test_results.append(("最佳交易所选择函数", result3))
    
    # 测试4: 统一错误处理器
    result4 = test_unified_error_handler()
    test_results.append(("统一错误处理器", result4))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！交易所选择异常处理修复成功。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    try:
        # 运行异步测试
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        traceback.print_exc()
        sys.exit(1)
