#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
log函数修复验证脚本
验证log函数重复定义问题是否已解决
"""

def test_log_function_fix():
    """测试log函数修复"""
    print("🔍 开始验证log函数修复...")
    
    try:
        # 导入WMZC模块
        print("📦 导入WMZC模块...")
        import WMZC
        
        # 测试1: 检查log函数是否存在
        print("🔍 测试1: 检查log函数是否存在")
        if hasattr(WMZC, 'log'):
            print("✅ log函数存在")
            
            # 测试2: 调用log函数
            print("🔍 测试2: 调用log函数")
            try:
                WMZC.log("这是一条测试日志", "INFO")
                print("✅ log函数调用成功")
                
                # 测试3: 测试不同日志级别
                print("🔍 测试3: 测试不同日志级别")
                WMZC.log("DEBUG级别测试", "DEBUG")
                WMZC.log("WARNING级别测试", "WARNING")
                WMZC.log("ERROR级别测试", "ERROR")
                print("✅ 多级别日志测试成功")
                
                # 测试4: 测试带上下文的日志
                print("🔍 测试4: 测试带上下文的日志")
                WMZC.log("带上下文的测试", "INFO", context="测试上下文")
                print("✅ 上下文日志测试成功")
                
                # 测试5: 测试带错误详情的日志
                print("🔍 测试5: 测试带错误详情的日志")
                WMZC.log("带错误详情的测试", "ERROR", error_details="这是错误详情")
                print("✅ 错误详情日志测试成功")
                
            except Exception as e:
                print(f"❌ log函数调用失败: {e}")
                return False
                
        else:
            print("❌ log函数不存在")
            return False
            
        # 测试6: 检查是否还有重复定义
        print("🔍 测试6: 检查模块中log函数的定义数量")
        
        # 通过检查源码来验证
        import inspect
        try:
            source = inspect.getsource(WMZC.log)
            if "智能日志函数路由" in source:
                print("✅ 使用的是修复后的智能路由版本")
            else:
                print("⚠️ 可能使用的不是预期的版本")
        except Exception as e:
            print(f"⚠️ 无法检查源码: {e}")
            
        print("🎉 log函数修复验证完成！")
        return True
        
    except ImportError as e:
        print(f"❌ WMZC模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        return False

def test_high_performance_logger():
    """测试高性能日志系统"""
    print("\n🔍 测试高性能日志系统...")
    
    try:
        import WMZC
        
        # 检查高性能日志器是否存在
        if hasattr(WMZC, '_high_performance_logger'):
            print("✅ 高性能日志器存在")
            
            # 测试高性能日志器的方法
            logger = WMZC._high_performance_logger
            if hasattr(logger, 'log'):
                print("✅ 高性能日志器有log方法")
                
                # 测试直接调用高性能日志器
                try:
                    logger.log("直接调用高性能日志器测试", "INFO")
                    print("✅ 高性能日志器直接调用成功")
                except Exception as e:
                    print(f"⚠️ 高性能日志器直接调用失败: {e}")
            else:
                print("⚠️ 高性能日志器缺少log方法")
        else:
            print("⚠️ 高性能日志器不存在，将使用临时日志函数")
            
        return True
        
    except Exception as e:
        print(f"❌ 高性能日志系统测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 log函数重复定义修复验证")
    print("=" * 60)
    
    # 测试log函数修复
    success1 = test_log_function_fix()
    
    # 测试高性能日志系统
    success2 = test_high_performance_logger()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过！log函数修复成功")
        print("✅ 修复效果:")
        print("   - 消除了log函数重复定义")
        print("   - 实现了智能日志路由")
        print("   - 保持了向后兼容性")
        print("   - 提供了优雅的降级机制")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    print("=" * 60)

if __name__ == "__main__":
    main()
