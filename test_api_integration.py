#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC API集成测试脚本
测试OKX和Gate.io API集成功能
"""

import asyncio
import time
import json
from datetime import datetime

def test_api_integration():
    """测试API集成功能"""
    print("🚀 开始WMZC API集成测试...")
    
    try:
        # 设置事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 导入WMZC
        import WMZC
        print("✅ WMZC模块导入成功")
        
        # 运行异步测试
        loop.run_until_complete(run_async_tests())
        
        print("🎉 API集成测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ API集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_async_tests():
    """运行异步测试"""
    print("\n📋 开始异步API测试...")
    
    # 测试1: 统一合约交易管理器
    await test_unified_contract_trading()
    
    # 测试2: OKX客户端
    await test_okx_client()
    
    # 测试3: Gate.io客户端
    await test_gate_client()
    
    # 测试4: WebSocket管理器
    await test_websocket_manager()
    
    # 测试5: 智能策略执行器
    await test_smart_strategy_executor()

async def test_unified_contract_trading():
    """测试统一合约交易管理器"""
    print("\n1. 测试统一合约交易管理器...")
    
    try:
        import WMZC
        
        # 创建统一交易管理器
        trading_manager = WMZC.UnifiedContractTrading()
        print("✅ 统一交易管理器创建成功")
        
        # 初始化交易所
        await trading_manager.initialize_exchanges()
        print("✅ 交易所初始化完成")
        
        # 测试获取持仓（模拟）
        position = await trading_manager.get_position('BTC-USDT-SWAP')
        print(f"✅ 持仓查询测试: {type(position)}")
        
        # 测试下单参数标准化
        order_params = trading_manager._standardize_order_params(
            'BTC-USDT-SWAP', 'buy', 100, 'market', None
        )
        print(f"✅ 订单参数标准化: {len(order_params)} 个参数")
        
    except Exception as e:
        print(f"❌ 统一合约交易管理器测试失败: {e}")

async def test_okx_client():
    """测试OKX客户端"""
    print("\n2. 测试OKX客户端...")
    
    try:
        import WMZC
        
        # 创建OKX客户端配置
        config = {
            'api_key': 'test_key',
            'secret_key': 'test_secret',
            'passphrase': 'test_passphrase',
            'sandbox': True
        }
        
        # 创建OKX客户端
        okx_client = WMZC.OKXContractClient(config)
        print("✅ OKX客户端创建成功")
        
        # 测试签名生成
        signature = okx_client._generate_signature(
            time.time(), 'GET', '/api/v5/account/balance'
        )
        print(f"✅ OKX签名生成: {'有效' if signature else '无效'}")
        
        # 测试请求头生成
        headers = okx_client._get_headers('GET', '/api/v5/account/balance')
        print(f"✅ OKX请求头生成: {len(headers)} 个头部")
        
        # 测试订单参数构建
        order_params = {
            'symbol': 'BTC-USDT-SWAP',
            'side': 'buy',
            'size': '100',
            'type': 'market'
        }
        okx_params = okx_client._build_okx_order_params(order_params)
        print(f"✅ OKX订单参数构建: {len(okx_params)} 个参数")
        
    except Exception as e:
        print(f"❌ OKX客户端测试失败: {e}")

async def test_gate_client():
    """测试Gate.io客户端"""
    print("\n3. 测试Gate.io客户端...")
    
    try:
        import WMZC
        
        # 创建Gate.io客户端配置
        config = {
            'api_key': 'test_key',
            'secret_key': 'test_secret',
            'sandbox': True
        }
        
        # 创建Gate.io客户端
        gate_client = WMZC.GateContractClient(config)
        print("✅ Gate.io客户端创建成功")
        
        # 测试签名生成
        signature, timestamp = gate_client._generate_signature(
            'GET', '/api/v4/futures/usdt/positions'
        )
        print(f"✅ Gate.io签名生成: {'有效' if signature else '无效'}")
        
        # 测试请求头生成
        headers = gate_client._get_headers('GET', '/api/v4/futures/usdt/positions')
        print(f"✅ Gate.io请求头生成: {len(headers)} 个头部")
        
        # 测试订单参数构建
        order_params = {
            'symbol': 'BTC_USDT',
            'side': 'buy',
            'size': '100',
            'type': 'market'
        }
        gate_params = gate_client._build_gate_order_params(order_params)
        print(f"✅ Gate.io订单参数构建: {len(gate_params)} 个参数")
        
    except Exception as e:
        print(f"❌ Gate.io客户端测试失败: {e}")

async def test_websocket_manager():
    """测试WebSocket管理器"""
    print("\n4. 测试WebSocket管理器...")
    
    try:
        import WMZC
        
        # 创建WebSocket管理器
        ws_manager = WMZC.EnhancedWebSocketManager()
        print("✅ WebSocket管理器创建成功")
        
        # 测试回调注册
        def test_callback(data):
            print(f"收到数据: {type(data)}")
            
        ws_manager.register_callback('tickers', test_callback)
        print("✅ WebSocket回调注册成功")
        
        # 测试Gate.io认证
        auth = ws_manager._get_gate_auth('futures.orders', 'subscribe')
        print(f"✅ Gate.io WebSocket认证: {len(auth)} 个字段")
        
    except Exception as e:
        print(f"❌ WebSocket管理器测试失败: {e}")

async def test_smart_strategy_executor():
    """测试智能策略执行器"""
    print("\n5. 测试智能策略执行器...")
    
    try:
        import WMZC
        
        # 创建依赖组件
        trading_manager = WMZC.UnifiedContractTrading()
        ws_manager = WMZC.EnhancedWebSocketManager()
        
        # 创建策略执行器
        strategy_executor = WMZC.SmartStrategyExecutor(trading_manager, ws_manager)
        print("✅ 智能策略执行器创建成功")
        
        # 测试策略配置验证
        valid_config = {
            'id': 'test_strategy',
            'symbol': 'BTC-USDT-SWAP',
            'strategy_type': 'trend_following'
        }
        
        is_valid = strategy_executor._validate_strategy_config(valid_config)
        print(f"✅ 策略配置验证: {'通过' if is_valid else '失败'}")
        
        # 测试仓位计算
        position_size = strategy_executor._calculate_position_size('BTC-USDT-SWAP', 0.8)
        print(f"✅ 仓位计算: {position_size}")
        
        # 测试波动率获取
        volatility = strategy_executor._get_symbol_volatility('BTC-USDT-SWAP')
        print(f"✅ 波动率获取: {volatility}")
        
    except Exception as e:
        print(f"❌ 智能策略执行器测试失败: {e}")

def test_api_configuration():
    """测试API配置"""
    print("\n6. 测试API配置...")
    
    try:
        import WMZC
        
        # 测试环境变量获取
        okx_key = WMZC.safe_get_env_config('OKX_API_KEY', 'default_key')
        gate_key = WMZC.safe_get_env_config('GATE_API_KEY', 'default_key')
        
        print(f"✅ OKX API Key: {'已配置' if okx_key != 'default_key' else '未配置'}")
        print(f"✅ Gate.io API Key: {'已配置' if gate_key != 'default_key' else '未配置'}")
        
        # 测试配置验证
        print("✅ API配置测试完成")
        
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 WMZC API集成测试")
    print("=" * 60)
    
    # 运行测试
    success = test_api_integration()
    
    # 运行配置测试
    test_api_configuration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有API集成测试通过！")
        print("✅ 系统已准备好进行实际交易")
    else:
        print("❌ 部分测试失败，请检查配置")
    print("=" * 60)
