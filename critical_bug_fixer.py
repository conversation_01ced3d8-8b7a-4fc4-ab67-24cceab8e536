#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC系统严重BUG批量修复器
专门修复time.sleep()阻塞操作问题
"""

import re
import json

def fix_time_sleep_bugs():
    """批量修复time.sleep()阻塞操作"""
    
    print("🔧 开始批量修复time.sleep()阻塞操作...")
    
    # 读取BUG报告
    with open('bug_detection_report.json', 'r', encoding='utf-8') as f:
        bug_report = json.load(f)
    
    # 筛选严重级别的time.sleep BUG
    time_sleep_bugs = [
        bug for bug in bug_report['bugs'] 
        if bug['severity'] == '🔴 严重' and 'time.sleep()' in bug['description']
    ]
    
    print(f"发现 {len(time_sleep_bugs)} 个time.sleep()严重BUG")
    
    # 读取WMZC.py文件
    with open('WMZC.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    fixed_count = 0
    
    # 批量修复每个BUG
    for bug in time_sleep_bugs:
        line_num = bug['line']
        if line_num <= len(lines):
            original_line = lines[line_num - 1]
            
            # 检查是否包含time.sleep
            if 'time.sleep(' in original_line:
                # 替换time.sleep为await asyncio.sleep
                fixed_line = original_line.replace('time.sleep(', 'await asyncio.sleep(')
                lines[line_num - 1] = fixed_line
                fixed_count += 1
                print(f"✅ 修复第{line_num}行: time.sleep -> await asyncio.sleep")
    
    # 确保导入asyncio
    if fixed_count > 0:
        # 检查是否已有asyncio导入
        has_asyncio_import = any('import asyncio' in line for line in lines[:50])
        
        if not has_asyncio_import:
            # 在文件开头添加asyncio导入
            for i, line in enumerate(lines):
                if line.startswith('import ') or line.startswith('from '):
                    lines.insert(i, 'import asyncio')
                    break
            print("✅ 添加了asyncio导入")
    
    # 写回文件
    with open('WMZC.py', 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    print(f"🎉 批量修复完成！共修复 {fixed_count} 个time.sleep()BUG")
    
    return fixed_count

def fix_function_signatures():
    """修复函数签名，添加async关键字"""
    
    print("🔧 开始修复函数签名...")
    
    with open('WMZC.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要添加async的函数模式
    patterns_to_fix = [
        (r'(\s+)def (optimize_loop\(\):)', r'\1async def \2'),
        (r'(\s+)def (monitoring_loop\(\):)', r'\1async def \2'),
        (r'(\s+)def (_adaptive_optimization\(\):)', r'\1async def \2'),
        (r'(\s+)def (_cleanup_expired_items\(\):)', r'\1async def \2'),
        (r'(\s+)def (_check_consistency_health\(\):)', r'\1async def \2'),
    ]
    
    fixed_count = 0
    for pattern, replacement in patterns_to_fix:
        new_content, count = re.subn(pattern, replacement, content)
        if count > 0:
            content = new_content
            fixed_count += count
            print(f"✅ 修复了 {count} 个函数签名")
    
    # 写回文件
    with open('WMZC.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"🎉 函数签名修复完成！共修复 {fixed_count} 个函数")
    
    return fixed_count

def verify_fixes():
    """验证修复结果"""
    
    print("🔍 验证修复结果...")
    
    with open('WMZC.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否还有time.sleep
    remaining_time_sleep = content.count('time.sleep(')
    print(f"剩余time.sleep()调用: {remaining_time_sleep}")
    
    # 检查asyncio.sleep数量
    asyncio_sleep_count = content.count('await asyncio.sleep(')
    print(f"await asyncio.sleep()调用: {asyncio_sleep_count}")
    
    # 检查是否有asyncio导入
    has_asyncio = 'import asyncio' in content
    print(f"asyncio导入状态: {'✅ 已导入' if has_asyncio else '❌ 未导入'}")
    
    return {
        'remaining_time_sleep': remaining_time_sleep,
        'asyncio_sleep_count': asyncio_sleep_count,
        'has_asyncio_import': has_asyncio
    }

if __name__ == "__main__":
    print("🚀 启动WMZC系统严重BUG批量修复器...")
    
    # 执行修复
    time_sleep_fixed = fix_time_sleep_bugs()
    function_fixed = fix_function_signatures()
    
    # 验证结果
    verification = verify_fixes()
    
    print("\n📊 修复总结:")
    print(f"- 修复time.sleep()调用: {time_sleep_fixed}个")
    print(f"- 修复函数签名: {function_fixed}个")
    print(f"- 剩余time.sleep(): {verification['remaining_time_sleep']}个")
    print(f"- 新增await asyncio.sleep(): {verification['asyncio_sleep_count']}个")
    
    if verification['remaining_time_sleep'] == 0:
        print("🎉 所有严重的time.sleep()BUG已修复！")
    else:
        print("⚠️ 仍有time.sleep()需要手动修复")
