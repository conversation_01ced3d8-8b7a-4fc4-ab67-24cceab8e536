#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC API密钥配置脚本
设置正确的API密钥以解决长度验证警告
"""

import json
import os

class APIKeySetup:
    """API密钥设置器"""
    
    def __init__(self):
        self.config_dir = os.path.expanduser("~/.wmzc_trading")
        self.wmzc_config_path = os.path.join(self.config_dir, "wmzc_config.json")
        self.trading_config_path = os.path.join(self.config_dir, "trading_config.json")
        
    def setup_api_keys(self):
        """设置API密钥"""
        print("🔧 设置API密钥...")
        
        # 使用您系统中已有的有效API密钥
        api_keys = {
            "OKX": {
                "API_KEY": "da636867-490f-4e3e-81b2-870841afb860",
                "SECRET_KEY": "C15B6EE0CF3FFDEE5834865D3839325E", 
                "PASSPHRASE": "Mx123456@"
            },
            "GATE": {
                "API_KEY": "d5ea5faa068d66204bb68b75201c56d5",
                "SECRET_KEY": "5b516e55788fba27e61f9bd06b22ab3661b3115797076d5e73199bea3a8afb1c"
            }
        }
        
        try:
            # 更新wmzc_config.json
            with open(self.wmzc_config_path, 'r', encoding='utf-8') as f:
                wmzc_config = json.load(f)
            
            wmzc_config["API_KEY"] = api_keys["OKX"]["API_KEY"]
            wmzc_config["SECRET_KEY"] = api_keys["OKX"]["SECRET_KEY"]
            wmzc_config["PASSPHRASE"] = api_keys["OKX"]["PASSPHRASE"]
            
            with open(self.wmzc_config_path, 'w', encoding='utf-8') as f:
                json.dump(wmzc_config, f, indent=2, ensure_ascii=False)
            
            print("✅ wmzc_config.json API密钥已更新")
            
            # 更新trading_config.json
            with open(self.trading_config_path, 'r', encoding='utf-8') as f:
                trading_config = json.load(f)
            
            trading_config["API_KEY"] = api_keys["OKX"]["API_KEY"]
            trading_config["API_SECRET"] = api_keys["OKX"]["SECRET_KEY"]
            trading_config["PASSPHRASE"] = api_keys["OKX"]["PASSPHRASE"]
            
            with open(self.trading_config_path, 'w', encoding='utf-8') as f:
                json.dump(trading_config, f, indent=2, ensure_ascii=False)
            
            print("✅ trading_config.json API密钥已更新")
            
            return True
            
        except Exception as e:
            print(f"❌ 设置API密钥失败: {e}")
            return False
    
    def validate_api_keys(self):
        """验证API密钥"""
        print("🔍 验证API密钥...")
        
        try:
            with open(self.wmzc_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            api_key = config.get("API_KEY", "")
            passphrase = config.get("PASSPHRASE", "")
            
            print(f"API_KEY 长度: {len(api_key)}")
            print(f"PASSPHRASE 长度: {len(passphrase)}")
            
            if len(api_key) >= 10:
                print("✅ API_KEY 长度验证通过")
            else:
                print(f"❌ API_KEY 长度不足: {len(api_key)} < 10")
                return False
            
            if len(passphrase) >= 3:
                print("✅ PASSPHRASE 长度验证通过")
            else:
                print(f"❌ PASSPHRASE 长度不足: {len(passphrase)} < 3")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 验证API密钥失败: {e}")
            return False
    
    def run_setup(self):
        """运行设置"""
        print("🚀 开始API密钥设置...")
        
        if not self.setup_api_keys():
            return False
        
        if not self.validate_api_keys():
            return False
        
        print("🎉 API密钥设置完成！")
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 WMZC API密钥设置工具")
    print("=" * 60)
    
    setup = APIKeySetup()
    
    try:
        success = setup.run_setup()
        
        if success:
            print("\n✅ API密钥设置成功！")
            print("💡 现在可以重新启动系统，配置警告应该消失")
            return True
        else:
            print("\n❌ API密钥设置失败")
            return False
            
    except Exception as e:
        print(f"\n💥 设置异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
