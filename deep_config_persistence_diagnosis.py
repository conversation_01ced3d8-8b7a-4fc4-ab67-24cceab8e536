#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC配置持久化问题深度诊断脚本
全面分析为什么API密钥配置仍然丢失的根本原因
"""

import os
import json
import sys
import time
import subprocess
from datetime import datetime
import traceback

class DeepConfigPersistenceDiagnoser:
    """深度配置持久化诊断器"""
    
    def __init__(self):
        self.config_files = {
            'trading_config.json': 'trading_config.json',
            'wmzc_config.json': 'wmzc_config.json',
            'user_settings.json': 'user_settings.json'
        }
        self.wmzc_file = 'WMZC.py'
        self.issues_found = []
        self.diagnosis_results = {}
        
    def run_deep_diagnosis(self):
        """运行深度诊断"""
        print("🔍 WMZC配置持久化问题深度诊断")
        print("=" * 60)
        
        # 1. 检查当前配置状态
        self.check_current_config_state()
        
        # 2. 分析WMZC.py中的配置加载逻辑
        self.analyze_wmzc_config_loading()
        
        # 3. 检查配置覆盖源
        self.check_config_override_sources()
        
        # 4. 模拟系统启动过程
        self.simulate_startup_process()
        
        # 5. 检查文件权限和路径
        self.check_file_permissions()
        
        # 6. 分析配置保护机制
        self.analyze_protection_mechanisms()
        
        # 7. 生成诊断报告
        self.generate_diagnosis_report()
        
        return self.issues_found
    
    def check_current_config_state(self):
        """检查当前配置状态"""
        print("\n📋 检查当前配置状态...")
        
        for name, path in self.config_files.items():
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    print(f"\n📄 {name}:")
                    print(f"  文件大小: {os.path.getsize(path)} 字节")
                    print(f"  配置项数: {len(config)}")
                    
                    # 检查API配置
                    if name == 'trading_config.json':
                        api_fields = ['API_KEY', 'API_SECRET', 'PASSPHRASE', 'OKX_API_KEY', 'OKX_SECRET_KEY', 'OKX_PASSPHRASE']
                    elif name == 'wmzc_config.json':
                        api_fields = ['okx_api_key', 'okx_secret_key', 'okx_passphrase']
                    else:
                        api_fields = []
                    
                    api_status = {}
                    for field in api_fields:
                        value = config.get(field, '')
                        api_status[field] = 'SET' if value else 'EMPTY'
                        if value:
                            print(f"  ✅ {field}: {value[:10]}...")
                        else:
                            print(f"  ❌ {field}: 空值")
                    
                    # 检查保护标记
                    if config.get('_CONFIG_PROTECTED'):
                        print(f"  🛡️ 配置保护: 已启用")
                        print(f"  📅 保护时间: {config.get('_PROTECTION_ENABLED', 'N/A')}")
                    else:
                        print(f"  ⚠️ 配置保护: 未启用")
                        self.issues_found.append(f"{name} 配置保护未启用")
                    
                    # 检查最后修改时间
                    mtime = os.path.getmtime(path)
                    mtime_str = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
                    print(f"  🕒 最后修改: {mtime_str}")
                    
                    self.diagnosis_results[name] = {
                        'exists': True,
                        'size': os.path.getsize(path),
                        'config_count': len(config),
                        'api_status': api_status,
                        'protected': config.get('_CONFIG_PROTECTED', False),
                        'last_modified': mtime_str
                    }
                    
                except Exception as e:
                    print(f"  ❌ 读取失败: {e}")
                    self.issues_found.append(f"{name} 读取失败: {e}")
            else:
                print(f"\n📄 {name}: ❌ 文件不存在")
                self.issues_found.append(f"{name} 文件不存在")
    
    def analyze_wmzc_config_loading(self):
        """分析WMZC.py中的配置加载逻辑"""
        print("\n🔧 分析WMZC.py中的配置加载逻辑...")
        
        if not os.path.exists(self.wmzc_file):
            print("  ❌ WMZC.py 文件不存在")
            self.issues_found.append("WMZC.py 文件不存在")
            return
        
        try:
            with open(self.wmzc_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键函数
            key_functions = [
                'load_config_from_file',
                '_ensure_config_defaults',
                'get_default_config',
                '_auto_init_config'
            ]
            
            for func in key_functions:
                if f"def {func}" in content:
                    print(f"  ✅ 找到函数: {func}")
                else:
                    print(f"  ❌ 缺少函数: {func}")
                    self.issues_found.append(f"WMZC.py 缺少函数: {func}")
            
            # 检查配置加载调用
            config_load_patterns = [
                'config = load_config_from_file()',
                '_auto_init_config()',
                'get_default_config()'
            ]
            
            for pattern in config_load_patterns:
                if pattern in content:
                    print(f"  ✅ 找到调用: {pattern}")
                else:
                    print(f"  ⚠️ 未找到调用: {pattern}")
            
            # 检查是否有硬编码的配置重置
            reset_patterns = [
                "API_KEY': ''",
                "API_SECRET': ''",
                "PASSPHRASE': ''",
                'config = {}',
                'config = get_default_config()'
            ]
            
            reset_found = []
            for pattern in reset_patterns:
                if pattern in content:
                    reset_found.append(pattern)
            
            if reset_found:
                print(f"  ⚠️ 发现可能的配置重置: {reset_found}")
                self.issues_found.append(f"WMZC.py 包含配置重置代码: {reset_found}")
            
        except Exception as e:
            print(f"  ❌ 分析WMZC.py失败: {e}")
            self.issues_found.append(f"分析WMZC.py失败: {e}")
    
    def check_config_override_sources(self):
        """检查配置覆盖源"""
        print("\n🚫 检查配置覆盖源...")
        
        # 检查可能的覆盖脚本
        potential_override_files = [
            'wmzc_config_warning_silencer.py',
            'wmzc_final_config_optimizer.py', 
            'wmzc_config_fixer.py',
            'config_reset.py',
            'init_config.py',
            'setup_config.py'
        ]
        
        for script in potential_override_files:
            if os.path.exists(script):
                print(f"  ⚠️ 发现脚本: {script}")
                try:
                    with open(script, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否包含配置写入
                    if 'json.dump' in content or 'with open' in content:
                        print(f"    ❌ {script} 包含文件写入操作")
                        self.issues_found.append(f"{script} 可能覆盖配置文件")
                    
                    # 检查是否包含硬编码API
                    if 'da636867-490f-4e3e-81b2-870841afb860' in content:
                        print(f"    ❌ {script} 包含硬编码API密钥")
                        self.issues_found.append(f"{script} 包含硬编码API密钥")
                        
                except Exception as e:
                    print(f"    ❌ 检查 {script} 失败: {e}")
            else:
                print(f"  ✅ {script}: 不存在")
        
        # 检查是否有.disabled文件
        disabled_files = [f for f in os.listdir('.') if f.endswith('.disabled')]
        if disabled_files:
            print(f"  ✅ 发现已禁用的脚本: {disabled_files}")
        
    def simulate_startup_process(self):
        """模拟系统启动过程"""
        print("\n🔄 模拟系统启动过程...")
        
        # 创建测试配置
        test_config = {
            "API_KEY": "test_startup_key_123",
            "API_SECRET": "test_startup_secret_456", 
            "PASSPHRASE": "test_startup_pass_789",
            "_CONFIG_PROTECTED": True,
            "_TEST_MARKER": datetime.now().isoformat()
        }
        
        print("  💾 创建测试配置...")
        try:
            with open('trading_config.json', 'w', encoding='utf-8') as f:
                json.dump(test_config, f, indent=2, ensure_ascii=False)
            print("    ✅ 测试配置已保存")
        except Exception as e:
            print(f"    ❌ 保存测试配置失败: {e}")
            return
        
        # 模拟导入WMZC模块（不实际导入，避免副作用）
        print("  🔍 检查WMZC模块导入影响...")
        
        # 检查配置是否被修改
        time.sleep(1)  # 等待文件系统同步
        
        try:
            with open('trading_config.json', 'r', encoding='utf-8') as f:
                current_config = json.load(f)
            
            if current_config.get('API_KEY') == test_config['API_KEY']:
                print("    ✅ 测试配置保持不变")
            else:
                print("    ❌ 测试配置被修改")
                self.issues_found.append("配置在启动过程中被修改")
            
            if current_config.get('_TEST_MARKER') == test_config['_TEST_MARKER']:
                print("    ✅ 测试标记保持不变")
            else:
                print("    ❌ 测试标记被修改或删除")
                self.issues_found.append("配置文件被重写")
                
        except Exception as e:
            print(f"    ❌ 检查测试配置失败: {e}")
    
    def check_file_permissions(self):
        """检查文件权限和路径"""
        print("\n🔒 检查文件权限和路径...")
        
        for name, path in self.config_files.items():
            print(f"\n📄 {name}:")
            
            # 检查文件是否存在
            if os.path.exists(path):
                print(f"  ✅ 文件存在: {os.path.abspath(path)}")
                
                # 检查读权限
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        f.read(1)
                    print("  ✅ 读权限: 正常")
                except Exception as e:
                    print(f"  ❌ 读权限: 失败 - {e}")
                    self.issues_found.append(f"{name} 读权限问题: {e}")
                
                # 检查写权限
                try:
                    with open(path, 'a', encoding='utf-8') as f:
                        pass  # 只是测试打开，不写入内容
                    print("  ✅ 写权限: 正常")
                except Exception as e:
                    print(f"  ❌ 写权限: 失败 - {e}")
                    self.issues_found.append(f"{name} 写权限问题: {e}")
                
                # 检查文件锁定
                try:
                    import msvcrt
                    with open(path, 'r+', encoding='utf-8') as f:
                        msvcrt.locking(f.fileno(), msvcrt.LK_NBLCK, 1)
                        msvcrt.locking(f.fileno(), msvcrt.LK_UNLCK, 1)
                    print("  ✅ 文件锁定: 正常")
                except Exception as e:
                    print(f"  ⚠️ 文件锁定检查: {e}")
                    
            else:
                print(f"  ❌ 文件不存在: {os.path.abspath(path)}")
    
    def analyze_protection_mechanisms(self):
        """分析配置保护机制"""
        print("\n🛡️ 分析配置保护机制...")
        
        # 检查保护脚本
        if os.path.exists('protect_user_config.py'):
            print("  ✅ 保护脚本存在: protect_user_config.py")
        else:
            print("  ❌ 保护脚本不存在")
            self.issues_found.append("保护脚本不存在")
        
        # 检查配置文件中的保护标记
        protected_files = 0
        for name, path in self.config_files.items():
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    if config.get('_CONFIG_PROTECTED'):
                        protected_files += 1
                        print(f"  ✅ {name}: 保护标记存在")
                    else:
                        print(f"  ❌ {name}: 保护标记缺失")
                        
                except Exception as e:
                    print(f"  ❌ 检查 {name} 保护标记失败: {e}")
        
        if protected_files == 0:
            self.issues_found.append("所有配置文件都缺少保护标记")
    
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n📊 诊断报告生成...")
        
        report = {
            'diagnosis_time': datetime.now().isoformat(),
            'issues_found': self.issues_found,
            'diagnosis_results': self.diagnosis_results,
            'recommendations': []
        }
        
        # 生成建议
        if any('配置保护未启用' in issue for issue in self.issues_found):
            report['recommendations'].append("重新运行配置保护脚本")
        
        if any('包含硬编码API密钥' in issue for issue in self.issues_found):
            report['recommendations'].append("清理或禁用包含硬编码API的脚本")
        
        if any('配置重置代码' in issue for issue in self.issues_found):
            report['recommendations'].append("修复WMZC.py中的配置重置逻辑")
        
        if any('权限问题' in issue for issue in self.issues_found):
            report['recommendations'].append("修复文件权限问题")
        
        # 保存报告
        try:
            with open('config_diagnosis_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print("  ✅ 诊断报告已保存: config_diagnosis_report.json")
        except Exception as e:
            print(f"  ❌ 保存诊断报告失败: {e}")

def main():
    """主函数"""
    print("🔍 WMZC配置持久化问题深度诊断工具")
    print("=" * 60)
    
    diagnoser = DeepConfigPersistenceDiagnoser()
    
    try:
        issues = diagnoser.run_deep_diagnosis()
        
        print("\n" + "=" * 60)
        print("📊 深度诊断结果总结:")
        
        if issues:
            print(f"❌ 发现 {len(issues)} 个问题:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
            
            print("\n🔧 建议的修复步骤:")
            print("  1. 检查并修复WMZC.py中的配置加载逻辑")
            print("  2. 重新运行配置保护脚本")
            print("  3. 清理所有配置覆盖脚本")
            print("  4. 修复文件权限问题")
            print("  5. 实施更强的配置保护机制")
            
        else:
            print("✅ 未发现明显问题，可能需要更深入的分析")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ 诊断过程中发生异常: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
