#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC内存泄漏专家级分析器
基于336MB内存增长自动退出问题的深度诊断和修复
"""

import os
import gc
import sys
import time
import psutil
import threading
import traceback
from datetime import datetime
from collections import defaultdict, deque

class WMZCMemoryLeakExpertAnalyzer:
    """WMZC内存泄漏专家级分析器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.baseline_memory = self.process.memory_info().rss
        self.memory_snapshots = deque(maxlen=1000)
        self.leak_patterns = {}
        self.critical_threshold = 336 * 1024 * 1024  # 336MB临界阈值
        self.analysis_results = {}
        
        print("🔍 WMZC内存泄漏专家级分析器初始化")
        print(f"📊 基线内存: {self.baseline_memory / 1024 / 1024:.1f}MB")
        print(f"🚨 临界阈值: 336MB (系统自动退出点)")
    
    def run_comprehensive_analysis(self):
        """运行全面的内存泄漏分析"""
        print("\n" + "=" * 80)
        print("🔍 开始WMZC内存泄漏专家级分析")
        print("=" * 80)
        
        # 第一步：连接到WMZC系统
        wmzc_connected = self._connect_to_wmzc()
        
        # 第二步：内存使用模式分析
        self._analyze_memory_patterns()
        
        # 第三步：识别内存泄漏源
        self._identify_leak_sources()
        
        # 第四步：缓存系统分析
        self._analyze_cache_systems()
        
        # 第五步：对象生命周期分析
        self._analyze_object_lifecycle()
        
        # 第六步：线程和异步任务分析
        self._analyze_threading_memory()
        
        # 第七步：提供专业修复方案
        self._provide_expert_solutions()
        
        return self.analysis_results
    
    def _connect_to_wmzc(self):
        """连接到WMZC系统进行实时分析"""
        print("\n🔌 连接到WMZC系统...")
        
        try:
            import WMZC
            if hasattr(WMZC, 'app') and WMZC.app:
                self.wmzc_app = WMZC.app
                print("✅ 成功连接到WMZC应用实例")
                
                # 检查内存泄漏检测器状态
                if hasattr(WMZC, 'memory_leak_detector'):
                    detector = WMZC.memory_leak_detector
                    stats = detector.get_leak_stats()
                    print(f"📊 当前内存使用: {stats['current_memory_mb']:.1f}MB")
                    print(f"📈 内存增长: {stats['memory_growth_mb']:.1f}MB")
                    print(f"🚨 检测到泄漏次数: {stats['total_leaks_detected']}")
                    
                    self.analysis_results['wmzc_detector_stats'] = stats
                    return True
                else:
                    print("⚠️ WMZC内存泄漏检测器未找到")
                    
            else:
                print("❌ 未找到WMZC应用实例")
                return False
                
        except ImportError:
            print("❌ 无法导入WMZC模块，将进行离线分析")
            return False
        except Exception as e:
            print(f"❌ 连接WMZC失败: {e}")
            return False
    
    def _analyze_memory_patterns(self):
        """分析内存使用模式"""
        print("\n📊 分析内存使用模式...")
        
        # 获取当前内存快照
        current_memory = self.process.memory_info().rss
        memory_growth = current_memory - self.baseline_memory
        
        print(f"📈 当前内存: {current_memory / 1024 / 1024:.1f}MB")
        print(f"📊 内存增长: {memory_growth / 1024 / 1024:.1f}MB")
        print(f"🚨 距离336MB阈值: {(self.critical_threshold - memory_growth) / 1024 / 1024:.1f}MB")
        
        # 分析内存增长趋势
        if memory_growth > 100 * 1024 * 1024:  # 超过100MB
            print("⚠️ 检测到显著内存增长")
            self.analysis_results['memory_growth_significant'] = True
        
        if memory_growth > 200 * 1024 * 1024:  # 超过200MB
            print("🚨 检测到严重内存增长")
            self.analysis_results['memory_growth_critical'] = True
        
        # 记录内存快照
        snapshot = {
            'timestamp': time.time(),
            'memory_rss': current_memory,
            'memory_vms': self.process.memory_info().vms,
            'memory_growth': memory_growth,
            'num_threads': self.process.num_threads(),
            'num_fds': self.process.num_fds() if hasattr(self.process, 'num_fds') else 0
        }
        self.memory_snapshots.append(snapshot)
        
        self.analysis_results['current_memory_snapshot'] = snapshot
    
    def _identify_leak_sources(self):
        """识别内存泄漏源"""
        print("\n🔍 识别内存泄漏源...")
        
        leak_sources = []
        
        # 1. 检查垃圾回收统计
        gc_stats = gc.get_stats()
        print(f"🗑️ 垃圾回收统计: {len(gc_stats)} 代")
        
        for i, stat in enumerate(gc_stats):
            print(f"  第{i}代: 收集{stat['collections']}次, 对象{stat.get('collected', 0)}个")
            if stat.get('uncollectable', 0) > 0:
                leak_sources.append(f"第{i}代垃圾回收有{stat['uncollectable']}个不可回收对象")
        
        # 2. 检查引用计数
        import sys
        ref_counts = defaultdict(int)
        for obj in gc.get_objects():
            obj_type = type(obj).__name__
            ref_counts[obj_type] += 1
        
        # 找出对象数量最多的类型
        top_objects = sorted(ref_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        print("📊 对象数量排行榜:")
        for obj_type, count in top_objects:
            print(f"  {obj_type}: {count:,} 个")
            if count > 10000:  # 超过1万个对象
                leak_sources.append(f"{obj_type}对象数量过多: {count:,}个")
        
        # 3. 检查循环引用
        referrers = gc.get_referrers
        circular_refs = 0
        for obj in gc.get_objects():
            if len(referrers(obj)) > 10:  # 被超过10个对象引用
                circular_refs += 1
        
        if circular_refs > 1000:
            leak_sources.append(f"检测到{circular_refs}个可能的循环引用")
        
        self.analysis_results['leak_sources'] = leak_sources
        
        if leak_sources:
            print("🚨 发现潜在泄漏源:")
            for source in leak_sources:
                print(f"  • {source}")
        else:
            print("✅ 未发现明显的泄漏源")
    
    def _analyze_cache_systems(self):
        """分析缓存系统内存使用"""
        print("\n💾 分析缓存系统...")
        
        cache_analysis = {}
        
        try:
            import WMZC
            
            # 检查智能缓存管理器
            if hasattr(WMZC, 'smart_cache_manager'):
                cache_manager = WMZC.smart_cache_manager
                cache_stats = cache_manager.get_cache_statistics()
                print(f"📊 智能缓存统计:")
                print(f"  缓存项数量: {cache_stats.get('total_items', 0)}")
                print(f"  内存使用: {cache_stats.get('memory_usage_mb', 0):.1f}MB")
                print(f"  命中率: {cache_stats.get('hit_rate', 0):.1f}%")
                
                cache_analysis['smart_cache'] = cache_stats
                
                # 检查是否缓存过大
                if cache_stats.get('memory_usage_mb', 0) > 50:
                    print("⚠️ 智能缓存内存使用过高")
                    cache_analysis['smart_cache_high_memory'] = True
            
            # 检查全局状态缓存
            if hasattr(WMZC, 'global_state'):
                global_state = WMZC.global_state
                if hasattr(global_state, '_enhanced_kline_cache'):
                    kline_cache_size = len(global_state._enhanced_kline_cache)
                    print(f"📈 K线缓存项数: {kline_cache_size}")
                    cache_analysis['kline_cache_items'] = kline_cache_size
                    
                    if kline_cache_size > 1000:
                        print("⚠️ K线缓存项数过多")
                        cache_analysis['kline_cache_high_items'] = True
            
            # 检查指标缓存
            if hasattr(WMZC, 'indicator_cache'):
                indicator_cache = WMZC.indicator_cache
                if hasattr(indicator_cache, '__len__'):
                    indicator_cache_size = len(indicator_cache)
                    print(f"📊 指标缓存项数: {indicator_cache_size}")
                    cache_analysis['indicator_cache_items'] = indicator_cache_size
        
        except Exception as e:
            print(f"❌ 缓存系统分析失败: {e}")
        
        self.analysis_results['cache_analysis'] = cache_analysis
    
    def _analyze_object_lifecycle(self):
        """分析对象生命周期"""
        print("\n🔄 分析对象生命周期...")
        
        # 强制垃圾回收并统计
        before_gc = len(gc.get_objects())
        collected = gc.collect()
        after_gc = len(gc.get_objects())
        
        print(f"🗑️ 垃圾回收前对象数: {before_gc:,}")
        print(f"🗑️ 垃圾回收后对象数: {after_gc:,}")
        print(f"🗑️ 回收对象数: {collected:,}")
        print(f"🗑️ 无法回收对象数: {before_gc - after_gc - collected:,}")
        
        # 检查弱引用
        import weakref
        weak_refs = [obj for obj in gc.get_objects() if isinstance(obj, weakref.ref)]
        print(f"🔗 弱引用数量: {len(weak_refs)}")
        
        # 检查大对象
        large_objects = []
        for obj in gc.get_objects():
            try:
                size = sys.getsizeof(obj)
                if size > 1024 * 1024:  # 超过1MB的对象
                    large_objects.append((type(obj).__name__, size))
            except:
                pass
        
        if large_objects:
            print("📦 大对象列表:")
            for obj_type, size in sorted(large_objects, key=lambda x: x[1], reverse=True)[:10]:
                print(f"  {obj_type}: {size / 1024 / 1024:.1f}MB")
        
        self.analysis_results['object_lifecycle'] = {
            'objects_before_gc': before_gc,
            'objects_after_gc': after_gc,
            'collected_objects': collected,
            'weak_refs_count': len(weak_refs),
            'large_objects_count': len(large_objects)
        }
    
    def _analyze_threading_memory(self):
        """分析线程和异步任务内存使用"""
        print("\n🧵 分析线程和异步任务...")
        
        # 检查线程数量
        thread_count = threading.active_count()
        print(f"🧵 活跃线程数: {thread_count}")
        
        # 检查线程列表
        threads = threading.enumerate()
        print("🧵 线程列表:")
        for thread in threads:
            print(f"  {thread.name}: {'活跃' if thread.is_alive() else '已停止'}")
        
        # 检查异步任务（如果有asyncio）
        try:
            import asyncio
            try:
                loop = asyncio.get_running_loop()
                tasks = asyncio.all_tasks(loop)
                print(f"⚡ 异步任务数: {len(tasks)}")
                
                # 检查未完成的任务
                pending_tasks = [task for task in tasks if not task.done()]
                print(f"⏳ 待完成任务数: {len(pending_tasks)}")
                
                if len(pending_tasks) > 100:
                    print("⚠️ 待完成异步任务过多，可能存在任务泄漏")
                
            except RuntimeError:
                print("⚡ 当前无运行中的事件循环")
        except ImportError:
            print("⚡ asyncio模块不可用")
        
        self.analysis_results['threading_analysis'] = {
            'active_threads': thread_count,
            'thread_names': [t.name for t in threads]
        }
    
    def _provide_expert_solutions(self):
        """提供专家级修复方案"""
        print("\n" + "=" * 80)
        print("🔧 专家级内存泄漏修复方案")
        print("=" * 80)
        
        solutions = []
        
        # 基于分析结果提供针对性解决方案
        if self.analysis_results.get('memory_growth_critical'):
            solutions.append({
                'priority': 'P0-CRITICAL',
                'issue': '内存增长超过200MB，接近336MB自动退出阈值',
                'solution': '立即执行内存清理和系统重启',
                'action': 'immediate_cleanup_and_restart'
            })
        
        if self.analysis_results.get('cache_analysis', {}).get('smart_cache_high_memory'):
            solutions.append({
                'priority': 'P1-HIGH',
                'issue': '智能缓存内存使用过高',
                'solution': '清理缓存并调整缓存策略',
                'action': 'optimize_cache_strategy'
            })
        
        if self.analysis_results.get('leak_sources'):
            for source in self.analysis_results['leak_sources']:
                solutions.append({
                    'priority': 'P1-HIGH',
                    'issue': f'检测到泄漏源: {source}',
                    'solution': '执行针对性清理和优化',
                    'action': 'targeted_cleanup'
                })
        
        # 显示解决方案
        for i, solution in enumerate(solutions, 1):
            print(f"\n🔧 解决方案 {i} [{solution['priority']}]")
            print(f"   问题: {solution['issue']}")
            print(f"   方案: {solution['solution']}")
            print(f"   操作: {solution['action']}")
        
        # 提供立即可执行的修复代码
        self._generate_fix_code()
        
        self.analysis_results['solutions'] = solutions
    
    def _generate_fix_code(self):
        """生成立即可执行的修复代码"""
        print(f"\n💻 生成修复代码...")
        
        fix_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 WMZC内存泄漏紧急修复脚本
基于专家分析结果的自动修复
"""

import gc
import time

def emergency_memory_cleanup():
    """紧急内存清理"""
    print("🚨 执行紧急内存清理...")
    
    try:
        import WMZC
        
        # 1. 清理缓存系统
        if hasattr(WMZC, 'smart_cache_manager'):
            WMZC.smart_cache_manager.clear_all_caches()
            print("✅ 智能缓存已清理")
        
        # 2. 清理全局状态缓存
        if hasattr(WMZC, 'global_state'):
            if hasattr(WMZC.global_state, '_enhanced_kline_cache'):
                WMZC.global_state._enhanced_kline_cache.clear()
                print("✅ K线缓存已清理")
        
        # 3. 强制垃圾回收
        collected = gc.collect()
        print(f"✅ 垃圾回收完成，清理{collected}个对象")
        
        # 4. 重置内存基线
        if hasattr(WMZC, 'memory_leak_detector'):
            WMZC.memory_leak_detector.reset_baseline()
            print("✅ 内存基线已重置")
        
        return True
        
    except Exception as e:
        print(f"❌ 紧急清理失败: {e}")
        return False

if __name__ == "__main__":
    emergency_memory_cleanup()
'''
        
        with open('wmzc_emergency_memory_fix.py', 'w', encoding='utf-8') as f:
            f.write(fix_code)
        
        print("✅ 紧急修复脚本已生成: wmzc_emergency_memory_fix.py")

def main():
    """主函数"""
    analyzer = WMZCMemoryLeakExpertAnalyzer()
    
    try:
        results = analyzer.run_comprehensive_analysis()
        
        print("\n" + "=" * 80)
        print("📋 分析完成总结")
        print("=" * 80)
        
        current_memory = analyzer.process.memory_info().rss / 1024 / 1024
        memory_growth = current_memory - analyzer.baseline_memory / 1024 / 1024
        
        print(f"📊 当前内存使用: {current_memory:.1f}MB")
        print(f"📈 内存增长: {memory_growth:.1f}MB")
        print(f"🚨 距离336MB阈值: {336 - memory_growth:.1f}MB")
        
        if memory_growth > 250:
            print("🚨 警告: 内存使用接近临界值，建议立即执行修复")
            print("💻 运行命令: python wmzc_emergency_memory_fix.py")
        elif memory_growth > 150:
            print("⚠️ 注意: 内存使用较高，建议监控并考虑清理")
        else:
            print("✅ 内存使用在正常范围内")
        
        return results
        
    except KeyboardInterrupt:
        print("\n🛑 分析被中断")
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
