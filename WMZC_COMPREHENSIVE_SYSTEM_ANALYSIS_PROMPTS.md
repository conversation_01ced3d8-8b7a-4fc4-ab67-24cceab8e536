# 🔍 WMZC量化交易系统全方位专业检测提示词集

## 📊 系统概览分析
基于对64,912行核心代码的100%深度理解，WMZC系统包含：
- **主系统**: WMZC.py (64,912行) - 核心交易引擎
- **全局仓位控制器**: Global_Position_Controller.py (377行)
- **批量订单管理器**: batch_order_manager.py (752行)
- **智能限频管理器**: exchange_rate_limiter.py (352行)
- **智能重试处理器**: smart_retry_handler.py (512行)
- **订单簿管理器**: order_book_manager.py
- **优化配置参数**: optimization_config_parameters.py (517行)

## 🎯 多维度专业检测提示词

### 1. 🔧 语法与结构完整性检测
```
检测目标：语法错误、导入问题、变量引用错误
检测方法：
- AST语法树解析验证
- 导入依赖关系检查
- 变量作用域分析
- 函数调用链验证
关键检查点：
- 异步函数调用是否正确使用await
- 导入模块是否存在循环依赖
- 全局变量使用是否规范
- 异常处理是否完整
```

### 2. ⚡ 异步编程合规性检测
```
检测目标：异步编程违规、阻塞操作、线程安全
检测方法：
- 搜索同步阻塞操作(time.sleep, requests.get等)
- 验证异步锁使用正确性
- 检查事件循环管理
- 分析并发安全性
关键检查点：
- 是否存在time.sleep()阻塞操作
- 异步方法调用是否缺少await
- 是否违反纯异步架构原则
- 资源管理是否正确
```

### 3. 🛡️ 交易安全性检测
```
检测目标：API密钥安全、交易风控、数据完整性
检测方法：
- 敏感信息泄露扫描
- 风控逻辑验证
- 订单执行安全检查
- 数据传输安全分析
关键检查点：
- 配置文件中是否有明文API密钥
- 风控参数是否合理
- 订单执行是否有防重复机制
- 日志是否泄露敏感信息
```

### 4. 📊 业务逻辑正确性检测
```
检测目标：技术指标计算、策略逻辑、交易流程
检测方法：
- 数学公式验证
- 策略参数边界检查
- 交易流程完整性验证
- 信号触发逻辑分析
关键检查点：
- 技术指标计算公式是否正确
- 策略参数是否在合理范围
- 买卖信号逻辑是否正确
- 止盈止损机制是否完善
```

### 5. 🔄 资源管理与内存安全检测
```
检测目标：内存泄漏、资源释放、连接管理
检测方法：
- 对象生命周期分析
- 连接池使用检查
- 缓存机制验证
- 垃圾回收优化分析
关键检查点：
- 是否存在未释放的资源
- WebSocket连接是否正确管理
- 缓存是否有过期机制
- 大对象是否及时清理
```

### 6. 🎨 用户界面与体验检测
```
检测目标：GUI线程安全、响应性能、错误提示
检测方法：
- GUI线程操作检查
- 界面响应时间分析
- 错误处理友好性验证
- 用户操作流程测试
关键检查点：
- GUI更新是否在主线程
- 长时间操作是否有进度提示
- 错误信息是否用户友好
- 界面是否会卡死
```

### 7. 📈 性能与并发检测
```
检测目标：性能瓶颈、并发安全、响应速度
检测方法：
- 关键路径性能分析
- 并发访问安全检查
- API调用频率优化验证
- 数据处理效率分析
关键检查点：
- 是否存在性能瓶颈
- 并发操作是否安全
- API限频是否合理
- 数据处理是否高效
```

### 8. 🔒 配置与数据完整性检测
```
检测目标：配置文件完整性、数据一致性、备份机制
检测方法：
- 配置文件格式验证
- 数据同步机制检查
- 备份恢复功能测试
- 版本兼容性验证
关键检查点：
- 配置文件是否有格式错误
- 数据是否存在不一致
- 备份机制是否可靠
- 配置更新是否安全
```

## 🚀 系统级深度扫描方法

### 阶段1：静态代码分析
1. **语法层面**: AST解析 + 语法检查
2. **结构层面**: 模块依赖 + 架构一致性
3. **安全层面**: 敏感信息 + 权限检查

### 阶段2：动态行为分析
1. **运行时检查**: 异常处理 + 资源管理
2. **性能分析**: 响应时间 + 内存使用
3. **并发测试**: 线程安全 + 竞态条件

### 阶段3：业务流程验证
1. **交易流程**: 端到端完整性验证
2. **策略执行**: 信号触发 + 订单执行
3. **风控机制**: 风险控制 + 异常处理

## 🎯 关键检测指标

### 严重级别分类
- 🔴 **严重**: 系统崩溃、数据丢失、安全漏洞
- 🟠 **重要**: 功能异常、性能问题、用户体验
- 🟡 **中等**: 代码质量、维护性、扩展性
- 🟢 **轻微**: 代码风格、注释完整性、优化建议

### 修复优先级
1. **立即修复**: 严重安全问题、系统稳定性
2. **优先修复**: 重要功能缺陷、性能瓶颈
3. **计划修复**: 中等质量问题、用户体验
4. **持续改进**: 轻微优化、代码规范

## 📋 检测执行标准

### 完整性要求
- ✅ 100%代码覆盖率
- ✅ 零遗漏关键路径
- ✅ 全方位多维度检测
- ✅ 真实场景验证

### 质量保证
- ✅ 三重验证机制
- ✅ 专业工具辅助
- ✅ 手工审查确认
- ✅ 实际运行测试

这套检测提示词确保对WMZC系统进行最专业、最全面的BUG检测，达到工业级质量标准。
