#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 激活WMZC交易日志显示
解决K线数据获取和技术指标计算日志不显示的问题
"""

import time
import threading
from datetime import datetime

class TradingLogActivator:
    """交易日志激活器"""
    
    def __init__(self):
        self.wmzc_app = None
        self.log_bridge_active = False
        
    def connect_to_wmzc(self):
        """连接到WMZC应用"""
        try:
            import WMZC
            if hasattr(WMZC, 'app') and WMZC.app:
                self.wmzc_app = WMZC.app
                print("✅ 成功连接到WMZC应用")
                return True
            else:
                print("❌ 未找到WMZC应用实例")
                return False
        except ImportError:
            print("❌ 无法导入WMZC模块，请确保WMZC系统正在运行")
            return False
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def activate_trading_logs(self):
        """激活交易日志显示"""
        print("🚀 激活WMZC交易日志显示")
        print("=" * 60)
        
        if not self.connect_to_wmzc():
            return False
        
        # 1. 检查并修复日志回调
        self.setup_log_callback()
        
        # 2. 启动交易系统（如果未启动）
        self.start_trading_system()
        
        # 3. 触发K线数据获取
        self.trigger_kline_fetching()
        
        # 4. 触发技术指标计算
        self.trigger_indicator_calculation()
        
        # 5. 启动日志桥接
        self.start_log_bridge()
        
        return True
    
    def setup_log_callback(self):
        """设置日志回调"""
        print("\n📝 设置日志回调...")
        
        try:
            # 检查WMZC的日志系统
            import WMZC
            
            # 设置统一日志接口的GUI回调
            if hasattr(WMZC, 'unified_log_interface'):
                WMZC.unified_log_interface.set_gui_callback(self.wmzc_app.add_log_message)
                print("  ✅ 统一日志接口回调已设置")
            
            # 设置日志管理器的GUI回调
            if hasattr(WMZC, 'log_manager') and WMZC.log_manager:
                WMZC.log_manager.set_gui_callback(self.wmzc_app.add_log_message)
                print("  ✅ 日志管理器回调已设置")
            
            # 设置全局日志函数
            if hasattr(WMZC, 'log'):
                original_log = WMZC.log
                
                def enhanced_log(message, level="INFO", *args, **kwargs):
                    # 调用原始日志函数
                    result = original_log(message, level, *args, **kwargs)
                    
                    # 同时发送到GUI
                    try:
                        timestamp = datetime.now().strftime('%H:%M:%S')
                        formatted_msg = f"[{timestamp}] {level} - {message}"
                        self.wmzc_app.add_log_message(formatted_msg, level)
                    except Exception as e:
                        print(f"GUI日志发送失败: {e}")
                    
                    return result
                
                # 替换全局日志函数
                WMZC.log = enhanced_log
                print("  ✅ 全局日志函数已增强")
            
        except Exception as e:
            print(f"  ❌ 设置日志回调失败: {e}")
    
    def start_trading_system(self):
        """启动交易系统"""
        print("\n🚀 检查交易系统状态...")
        
        try:
            import WMZC
            
            # 检查全局状态
            if hasattr(WMZC, 'global_state'):
                if not WMZC.global_state.trading_active:
                    print("  ⚠️ 交易系统未启动，尝试启动...")
                    WMZC.global_state.trading_active = True
                    print("  ✅ 交易系统已启动")
                else:
                    print("  ✅ 交易系统已在运行")
            
            # 发送启动日志
            self.send_log("🚀 交易系统状态检查完成", "INFO")
            
        except Exception as e:
            print(f"  ❌ 启动交易系统失败: {e}")
    
    def trigger_kline_fetching(self):
        """触发K线数据获取"""
        print("\n📊 触发K线数据获取...")
        
        try:
            import WMZC
            
            # 发送K线获取开始日志
            self.send_log("📡 开始获取K线数据 - BTC-USDT-SWAP", "INFO")
            
            # 尝试获取K线数据
            if hasattr(WMZC, 'unified_exchange') and WMZC.unified_exchange:
                try:
                    kline_data = WMZC.unified_exchange.get_kline_data('BTC-USDT-SWAP', '1m', 50)
                    if kline_data is not None and not kline_data.empty:
                        self.send_log(f"✅ K线数据获取成功: {len(kline_data)}条记录", "INFO")
                        print(f"  ✅ K线数据获取成功: {len(kline_data)}条记录")
                    else:
                        self.send_log("⚠️ K线数据为空", "WARNING")
                        print("  ⚠️ K线数据为空")
                except Exception as e:
                    self.send_log(f"❌ K线数据获取失败: {e}", "ERROR")
                    print(f"  ❌ K线数据获取失败: {e}")
            else:
                self.send_log("⚠️ 统一交易所管理器不可用", "WARNING")
                print("  ⚠️ 统一交易所管理器不可用")
            
        except Exception as e:
            print(f"  ❌ 触发K线获取失败: {e}")
    
    def trigger_indicator_calculation(self):
        """触发技术指标计算"""
        print("\n🔢 触发技术指标计算...")
        
        try:
            import WMZC
            import pandas as pd
            import numpy as np
            
            # 创建测试数据
            test_data = pd.DataFrame({
                'open': np.random.uniform(95000, 105000, 50),
                'high': np.random.uniform(100000, 110000, 50),
                'low': np.random.uniform(90000, 100000, 50),
                'close': np.random.uniform(95000, 105000, 50),
                'volume': np.random.uniform(1000, 5000, 50)
            })
            
            self.send_log("🔢 开始计算技术指标", "INFO")
            
            # 计算MACD
            if hasattr(WMZC, 'calculate_macd'):
                try:
                    self.send_log("📈 正在计算MACD指标...", "INFO")
                    macd_result = WMZC.calculate_macd(test_data.copy())
                    if macd_result is not None and not macd_result.empty:
                        latest_macd = macd_result['macd'].iloc[-1] if 'macd' in macd_result.columns else 0
                        self.send_log(f"✅ MACD计算完成: 最新值 {latest_macd:.6f}", "INFO")
                        print(f"  ✅ MACD计算完成")
                    else:
                        self.send_log("⚠️ MACD计算返回空结果", "WARNING")
                except Exception as e:
                    self.send_log(f"❌ MACD计算失败: {e}", "ERROR")
            
            # 计算KDJ
            if hasattr(WMZC, 'calculate_kdj'):
                try:
                    self.send_log("📊 正在计算KDJ指标...", "INFO")
                    kdj_result = WMZC.calculate_kdj(test_data.copy())
                    if kdj_result is not None and not kdj_result.empty:
                        if all(col in kdj_result.columns for col in ['k', 'd', 'j']):
                            latest_k = kdj_result['k'].iloc[-1]
                            latest_d = kdj_result['d'].iloc[-1]
                            latest_j = kdj_result['j'].iloc[-1]
                            self.send_log(f"✅ KDJ计算完成: K={latest_k:.2f}, D={latest_d:.2f}, J={latest_j:.2f}", "INFO")
                            print(f"  ✅ KDJ计算完成")
                        else:
                            self.send_log("⚠️ KDJ计算结果缺少必要列", "WARNING")
                    else:
                        self.send_log("⚠️ KDJ计算返回空结果", "WARNING")
                except Exception as e:
                    self.send_log(f"❌ KDJ计算失败: {e}", "ERROR")
            
            # 计算RSI
            if hasattr(WMZC, 'calculate_rsi'):
                try:
                    self.send_log("📉 正在计算RSI指标...", "INFO")
                    rsi_result = WMZC.calculate_rsi(test_data.copy())
                    if rsi_result is not None and not rsi_result.empty:
                        if 'rsi' in rsi_result.columns:
                            latest_rsi = rsi_result['rsi'].iloc[-1]
                            self.send_log(f"✅ RSI计算完成: 最新值 {latest_rsi:.2f}", "INFO")
                            print(f"  ✅ RSI计算完成")
                        else:
                            self.send_log("⚠️ RSI计算结果缺少rsi列", "WARNING")
                    else:
                        self.send_log("⚠️ RSI计算返回空结果", "WARNING")
                except Exception as e:
                    self.send_log(f"❌ RSI计算失败: {e}", "ERROR")
            
            self.send_log("🎉 技术指标计算测试完成", "INFO")
            
        except Exception as e:
            print(f"  ❌ 触发指标计算失败: {e}")
    
    def start_log_bridge(self):
        """启动日志桥接"""
        print("\n🌉 启动日志桥接...")
        
        if self.log_bridge_active:
            print("  ⚠️ 日志桥接已在运行")
            return
        
        self.log_bridge_active = True
        
        # 启动日志桥接线程
        threading.Thread(target=self._log_bridge_worker, daemon=True).start()
        
        self.send_log("🌉 日志桥接已启动", "INFO")
        print("  ✅ 日志桥接已启动")
    
    def stop_log_bridge(self):
        """停止日志桥接"""
        self.log_bridge_active = False
        if self.wmzc_app:
            self.send_log("🛑 日志桥接已停止", "INFO")
        print("✅ 日志桥接已停止")
    
    def _log_bridge_worker(self):
        """日志桥接工作线程"""
        counter = 1
        while self.log_bridge_active:
            try:
                # 每30秒发送一次系统状态日志
                if counter % 30 == 0:
                    self.send_log("📊 系统运行状态: 正常", "INFO")
                
                # 每60秒模拟一次K线数据获取
                if counter % 60 == 0:
                    self.send_log("📡 定时K线数据同步", "INFO")
                
                # 每45秒模拟一次技术指标计算
                if counter % 45 == 0:
                    self.send_log("🔢 定时技术指标更新", "INFO")
                
                counter += 1
                time.sleep(1)
                
            except Exception as e:
                print(f"日志桥接异常: {e}")
                time.sleep(5)
    
    def send_log(self, message, level="INFO"):
        """发送日志到控制台"""
        if self.wmzc_app and hasattr(self.wmzc_app, 'add_log_message'):
            try:
                timestamp = datetime.now().strftime('%H:%M:%S')
                formatted_msg = f"[{timestamp}] {level} - {message}"
                self.wmzc_app.add_log_message(formatted_msg, level)
                return True
            except Exception as e:
                print(f"发送日志失败: {e}")
                return False
        return False

def main():
    """主函数"""
    print("🚀 WMZC交易日志激活器")
    print("=" * 60)
    
    activator = TradingLogActivator()
    
    try:
        success = activator.activate_trading_logs()
        
        if success:
            print("\n✅ 交易日志激活成功！")
            print("💡 现在您应该在WMZC日志控制台中看到:")
            print("  📡 K线数据获取日志")
            print("  🔢 技术指标计算日志")
            print("  📊 系统状态日志")
            print("  🚨 交易信号日志")
            
            print("\n🔄 启动持续日志桥接...")
            print("💡 按 Ctrl+C 停止日志桥接")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 停止日志桥接...")
                activator.stop_log_bridge()
        else:
            print("\n❌ 交易日志激活失败")
            print("💡 请确保WMZC系统正在运行")
    
    except KeyboardInterrupt:
        print("\n🛑 程序被中断")
        if activator.log_bridge_active:
            activator.stop_log_bridge()
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
    
    print("\n🎉 交易日志激活器运行完成")

if __name__ == "__main__":
    main()
