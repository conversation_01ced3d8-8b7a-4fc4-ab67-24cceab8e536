{"system_health_score": 0, "total_issues": 94, "critical_issues": 1, "major_issues": 24, "minor_issues": 69, "suggestions": 0, "test_coverage": 0, "security_score": "C", "performance_score": "C", "maintainability_score": "C", "detection_timestamp": "2025-07-29T16:27:03.304854", "issues": [{"id": "ARCH-100", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 216, "description": "发现阻塞操作: requests.get(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 requests.get(", "priority": "HIGH"}, {"id": "ARCH-101", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 218, "description": "发现阻塞操作: requests.post(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 requests.post(", "priority": "HIGH"}, {"id": "ARCH-102", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 266, "description": "发现阻塞操作: urllib.request.urlopen(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 urllib.request.urlopen(", "priority": "HIGH"}, {"id": "ARCH-103", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 2526, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-104", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 2554, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-105", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 3105, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-106", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 3835, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-107", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 4456, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-108", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 5241, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-109", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 5245, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-110", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 6864, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-111", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 6868, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-112", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 8274, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-113", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 8947, "description": "发现阻塞操作: requests.post(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 requests.post(", "priority": "HIGH"}, {"id": "ARCH-114", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 10816, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-115", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 10828, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-116", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 13297, "description": "发现阻塞操作: threading.Lock()", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 threading.Lock()", "priority": "HIGH"}, {"id": "ARCH-117", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 13591, "description": "发现阻塞操作: threading.Lock()", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 threading.Lock()", "priority": "HIGH"}, {"id": "ARCH-118", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 21094, "description": "发现阻塞操作: time.sleep(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 time.sleep(", "priority": "HIGH"}, {"id": "ARCH-119", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 27153, "description": "发现阻塞操作: requests.get(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 requests.get(", "priority": "HIGH"}, {"id": "ARCH-120", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 27229, "description": "发现阻塞操作: requests.get(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 requests.get(", "priority": "HIGH"}, {"id": "ARCH-121", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 62237, "description": "发现阻塞操作: requests.get(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 requests.get(", "priority": "HIGH"}, {"id": "ARCH-122", "severity": "MAJOR", "category": "Architecture", "file": "WMZC.py", "line": 62262, "description": "发现阻塞操作: requests.post(", "impact": "违反异步架构原则，可能导致界面卡死", "solution": "使用异步替代方案替换 requests.post(", "priority": "HIGH"}, {"id": "SEC-001", "severity": "CRITICAL", "category": "Security", "file": "trading_config.json", "description": "配置文件包含明文API密钥: api_key", "impact": "API密钥泄露风险，可能导致资金损失", "solution": "使用环境变量或加密存储API密钥", "priority": "IMMEDIATE"}, {"id": "SEC-100", "severity": "MAJOR", "category": "Security", "file": "WMZC.py", "description": "缺少订单唯一标识机制", "impact": "可能导致重复下单", "solution": "添加订单唯一标识和重复检查", "priority": "HIGH"}, {"id": "PERF-001", "severity": "MINOR", "category": "Performance", "file": "WMZC.py", "description": "发现 14813 个全局变量", "impact": "可能存在线程安全问题", "solution": "使用线程安全的数据结构或锁机制", "priority": "MEDIUM"}, {"id": "PERF-002", "severity": "MINOR", "category": "Performance", "file": "Global_Position_Controller.py", "description": "发现 56 个全局变量", "impact": "可能存在线程安全问题", "solution": "使用线程安全的数据结构或锁机制", "priority": "MEDIUM"}, {"id": "PERF-003", "severity": "MINOR", "category": "Performance", "file": "batch_order_manager.py", "description": "发现 172 个全局变量", "impact": "可能存在线程安全问题", "solution": "使用线程安全的数据结构或锁机制", "priority": "MEDIUM"}, {"id": "PERF-004", "severity": "MINOR", "category": "Performance", "file": "exchange_rate_limiter.py", "description": "发现 69 个全局变量", "impact": "可能存在线程安全问题", "solution": "使用线程安全的数据结构或锁机制", "priority": "MEDIUM"}, {"id": "PERF-005", "severity": "MINOR", "category": "Performance", "file": "smart_retry_handler.py", "description": "发现 124 个全局变量", "impact": "可能存在线程安全问题", "solution": "使用线程安全的数据结构或锁机制", "priority": "MEDIUM"}, {"id": "PERF-006", "severity": "MINOR", "category": "Performance", "file": "order_book_manager.py", "description": "发现 132 个全局变量", "impact": "可能存在线程安全问题", "solution": "使用线程安全的数据结构或锁机制", "priority": "MEDIUM"}, {"id": "PERF-007", "severity": "MINOR", "category": "Performance", "file": "optimization_config_parameters.py", "description": "发现 264 个全局变量", "impact": "可能存在线程安全问题", "solution": "使用线程安全的数据结构或锁机制", "priority": "MEDIUM"}, {"id": "QUAL-050", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 598, "description": "函数 async analyze_market_signals 过长 (122 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-051", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 16000, "description": "函数 async perform_health_check 过长 (101 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-052", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 16609, "description": "函数 __init__ 过长 (130 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-053", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 17116, "description": "函数 _get_default_config 过长 (131 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-054", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 17480, "description": "函数 _define_quality_rules 过长 (124 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-055", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 19540, "description": "函数 calculate_macd 过长 (132 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-056", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 19722, "description": "函数 calculate_kdj 过长 (112 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-057", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 20078, "description": "函数 validate_indicators_with_okx 过长 (127 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-058", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 20205, "description": "函数 sync_okx_indicator_params 过长 (146 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-059", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 20654, "description": "函数 get_recommended_data_length 过长 (134 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-060", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 21824, "description": "函数 _register_strategies 过长 (161 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-061", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 22786, "description": "函数 _get_kline_rest 过长 (109 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-062", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 24449, "description": "函数 place_order 过长 (172 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-063", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 28766, "description": "函数 check_macd_trend_confirmation 过长 (112 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-064", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 31112, "description": "函数 async safe_init_okx 过长 (118 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-065", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 31506, "description": "函数 get_balance 过长 (191 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-066", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 32408, "description": "函数 train_enhanced_lstm_model 过长 (111 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-067", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 33502, "description": "函数 get_okx_taker_volume 过长 (107 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-068", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 33821, "description": "函数 get_odaily_news 过长 (135 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-069", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 33959, "description": "函数 _fetch_odaily_github_news 过长 (142 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-070", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 34104, "description": "函数 _fetch_jin10_news 过长 (146 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-071", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 34253, "description": "函数 _fetch_coinglass_news 过长 (169 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-072", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 36102, "description": "函数 _unbind_mousewheel 过长 (351 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-073", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 37313, "description": "函数 setup_strategy_market_frame 过长 (601 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-074", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 38600, "description": "函数 _run_sync_check 过长 (172 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-075", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 39377, "description": "函数 setup_trading_records_frame 过长 (124 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-076", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 39501, "description": "函数 setup_news_frame 过长 (146 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-077", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 40204, "description": "函数 create_exchange_indicator_config 过长 (118 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-078", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 40593, "description": "函数 update_result 过长 (166 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-079", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 41091, "description": "函数 setup_rsi_strategy_frame 过长 (375 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-080", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 41796, "description": "函数 setup_stop_profit_loss_frame 过长 (180 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-081", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 41976, "description": "函数 setup_equal_position_frame 过长 (106 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-082", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 42202, "description": "函数 setup_banking_risk_frame 过长 (180 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-083", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 42731, "description": "函数 update_banking_risk_panel 过长 (168 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-084", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 44113, "description": "函数 _on_mousewheel 过长 (228 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-085", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 44623, "description": "函数 _run_backtest 过长 (387 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-086", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 45068, "description": "函数 on_frame_configure 过长 (185 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-087", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 45638, "description": "函数 _run_optimization 过长 (104 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-088", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 45742, "description": "函数 _run_kdj_optimization 过长 (108 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-089", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 45850, "description": "函数 _run_comprehensive_optimization 过长 (133 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-090", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 46105, "description": "函数 _run_lstm_prediction 过长 (133 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-091", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 46338, "description": "函数 optimization_process 过长 (155 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-092", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 46681, "description": "函数 update_stats 过长 (220 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-093", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 47201, "description": "函数 _on_mousewheel 过长 (138 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-094", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 48053, "description": "函数 show_optimization_stats 过长 (162 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-095", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 48771, "description": "函数 load_config 过长 (269 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-096", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 49091, "description": "函数 save_config 过长 (340 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-097", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 51770, "description": "函数 stop_strategy_loop 过长 (113 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-098", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 52952, "description": "函数 force_complete_restart 过长 (117 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-099", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 53595, "description": "函数 update_status 过长 (124 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-100", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 53785, "description": "函数 update_market_data 过长 (185 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-101", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 53970, "description": "函数 update_monitor_panel 过长 (419 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-102", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 56408, "description": "函数 check_rsi_signal 过长 (225 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-103", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 57532, "description": "函数 load_rsi_config 过长 (124 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-104", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 58013, "description": "函数 execute_triple_confirmation_strategy 过长 (131 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-105", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 59214, "description": "函数 execute_strategy_market 过长 (103 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-106", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 59317, "description": "函数 validate_indicator_calculations 过长 (134 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-107", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 59608, "description": "函数 async trading_loop 过长 (746 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-108", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 61303, "description": "函数 main 过长 (170 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-109", "severity": "MINOR", "category": "Quality", "file": "WMZC.py", "line": 61588, "description": "函数 record_trading_signal 过长 (182 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-110", "severity": "MINOR", "category": "Quality", "file": "smart_retry_handler.py", "line": 251, "description": "函数 async execute_with_retry 过长 (120 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}, {"id": "QUAL-111", "severity": "MINOR", "category": "Quality", "file": "optimization_config_parameters.py", "line": 48, "description": "函数 _initialize_parameters 过长 (333 行)", "impact": "代码可读性和维护性差", "solution": "考虑拆分为更小的函数", "priority": "LOW"}], "detailed_analysis": {}, "detection_duration": "63.10s"}