#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 配置同步脚本
确保trading_config.json和wmzc_config.json中的API配置保持一致
"""

import os
import json
from datetime import datetime

def sync_api_config():
    """同步API配置"""
    print("🔄 开始同步API配置...")
    
    # 读取两个配置文件
    trading_config = {}
    wmzc_config = {}
    
    if os.path.exists('trading_config.json'):
        try:
            with open('trading_config.json', 'r', encoding='utf-8') as f:
                trading_config = json.load(f)
        except Exception as e:
            print(f"❌ 读取trading_config.json失败: {e}")
    
    if os.path.exists('wmzc_config.json'):
        try:
            with open('wmzc_config.json', 'r', encoding='utf-8') as f:
                wmzc_config = json.load(f)
        except Exception as e:
            print(f"❌ 读取wmzc_config.json失败: {e}")
    
    # 确定主要的API配置源
    trading_api = (trading_config.get('API_KEY', ''),
                  trading_config.get('API_SECRET', ''),
                  trading_config.get('PASSPHRASE', ''))
    
    wmzc_api = (wmzc_config.get('okx_api_key', ''),
               wmzc_config.get('okx_secret_key', ''),
               wmzc_config.get('okx_passphrase', ''))
    
    # 选择更完整的API配置
    if all(trading_api) and not all(wmzc_api):
        # 使用trading_config的API配置
        master_api = {
            'API_KEY': trading_api[0],
            'API_SECRET': trading_api[1],
            'PASSPHRASE': trading_api[2]
        }
        print("✅ 使用trading_config.json的API配置作为主配置")
    elif all(wmzc_api) and not all(trading_api):
        # 使用wmzc_config的API配置
        master_api = {
            'API_KEY': wmzc_api[0],
            'API_SECRET': wmzc_api[1],
            'PASSPHRASE': wmzc_api[2]
        }
        print("✅ 使用wmzc_config.json的API配置作为主配置")
    elif all(trading_api) and all(wmzc_api):
        # 两个都有，使用最近修改的
        trading_mtime = os.path.getmtime('trading_config.json') if os.path.exists('trading_config.json') else 0
        wmzc_mtime = os.path.getmtime('wmzc_config.json') if os.path.exists('wmzc_config.json') else 0
        
        if trading_mtime >= wmzc_mtime:
            master_api = {
                'API_KEY': trading_api[0],
                'API_SECRET': trading_api[1],
                'PASSPHRASE': trading_api[2]
            }
            print("✅ 使用trading_config.json的API配置（更新）")
        else:
            master_api = {
                'API_KEY': wmzc_api[0],
                'API_SECRET': wmzc_api[1],
                'PASSPHRASE': wmzc_api[2]
            }
            print("✅ 使用wmzc_config.json的API配置（更新）")
    else:
        print("⚠️ 两个配置文件都没有完整的API配置")
        return False
    
    # 同步到两个文件
    try:
        # 更新trading_config.json
        trading_config.update(master_api)
        trading_config['OKX_API_KEY'] = master_api['API_KEY']
        trading_config['OKX_SECRET_KEY'] = master_api['API_SECRET']
        trading_config['OKX_PASSPHRASE'] = master_api['PASSPHRASE']
        trading_config['_LAST_SYNC'] = datetime.now().isoformat()
        
        with open('trading_config.json', 'w', encoding='utf-8') as f:
            json.dump(trading_config, f, indent=2, ensure_ascii=False)
        
        print("✅ trading_config.json 已同步")
        
        # 更新wmzc_config.json
        wmzc_config['okx_api_key'] = master_api['API_KEY']
        wmzc_config['okx_secret_key'] = master_api['API_SECRET']
        wmzc_config['okx_passphrase'] = master_api['PASSPHRASE']
        wmzc_config['_LAST_SYNC'] = datetime.now().isoformat()
        
        with open('wmzc_config.json', 'w', encoding='utf-8') as f:
            json.dump(wmzc_config, f, indent=2, ensure_ascii=False)
        
        print("✅ wmzc_config.json 已同步")
        
        print(f"🎉 API配置同步完成: {master_api['API_KEY'][:15]}...")
        return True
        
    except Exception as e:
        print(f"❌ 同步API配置失败: {e}")
        return False

if __name__ == "__main__":
    sync_api_config()
