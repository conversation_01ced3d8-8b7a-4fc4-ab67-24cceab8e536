#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 WMZC日志控制台实时测试工具
实时测试和验证日志控制台功能
"""

import time
import threading
from datetime import datetime

def test_wmzc_log_console():
    """测试WMZC日志控制台"""
    print("🧪 开始测试WMZC日志控制台...")
    
    try:
        # 导入WMZC模块
        import WMZC
        
        # 检查是否有GUI应用实例
        if hasattr(WMZC, 'app') and WMZC.app:
            app = WMZC.app
            print("✅ 找到WMZC应用实例")
            
            # 检查日志控制台方法
            if hasattr(app, 'add_log_message'):
                print("✅ 找到add_log_message方法")
                
                # 测试日志输出
                test_messages = [
                    ("🧪 日志控制台测试开始", "INFO"),
                    ("📊 测试技术指标计算", "INFO"),
                    ("📈 测试交易信号生成", "INFO"),
                    ("⚠️ 测试警告消息显示", "WARNING"),
                    ("❌ 测试错误消息显示", "ERROR"),
                    ("✅ 日志控制台测试完成", "INFO")
                ]
                
                for message, level in test_messages:
                    timestamp = datetime.now().strftime('%H:%M:%S')
                    formatted_message = f"[{timestamp}] {level} - {message}"
                    
                    try:
                        app.add_log_message(formatted_message, level)
                        print(f"✅ 发送日志: {message}")
                        time.sleep(1)
                    except Exception as e:
                        print(f"❌ 发送日志失败: {e}")
                
                # 测试批量日志
                print("
🔄 测试批量日志输出...")
                for i in range(5):
                    timestamp = datetime.now().strftime('%H:%M:%S')
                    message = f"[{timestamp}] INFO - 📊 批量测试消息 #{i+1}"
                    try:
                        app.add_log_message(message, "INFO")
                        time.sleep(0.5)
                    except Exception as e:
                        print(f"❌ 批量日志失败: {e}")
                
                print("🎉 日志控制台测试完成！")
                return True
                
            else:
                print("❌ 未找到add_log_message方法")
                return False
        else:
            print("❌ 未找到WMZC应用实例")
            return False
            
    except ImportError:
        print("❌ 无法导入WMZC模块")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        return False

def continuous_log_test():
    """持续日志测试"""
    print("🔄 启动持续日志测试...")
    
    def log_worker():
        counter = 1
        while True:
            try:
                import WMZC
                if hasattr(WMZC, 'app') and WMZC.app and hasattr(WMZC.app, 'add_log_message'):
                    timestamp = datetime.now().strftime('%H:%M:%S')
                    message = f"[{timestamp}] INFO - 🔄 持续测试消息 #{counter}"
                    WMZC.app.add_log_message(message, "INFO")
                    counter += 1
                
                time.sleep(10)  # 每10秒发送一条消息
                
            except Exception as e:
                print(f"持续测试异常: {e}")
                time.sleep(5)
    
    # 启动后台线程
    thread = threading.Thread(target=log_worker, daemon=True)
    thread.start()
    print("✅ 持续日志测试已启动（每10秒一条消息）")

if __name__ == "__main__":
    print("🧪 WMZC日志控制台测试工具")
    print("=" * 50)
    
    # 基础测试
    success = test_wmzc_log_console()
    
    if success:
        print("\n💡 测试成功！您应该在日志控制台中看到测试消息")
        
        # 询问是否启动持续测试
        try:
            choice = input("\n是否启动持续日志测试？(y/N): ").strip().lower()
            if choice == 'y':
                continuous_log_test()
                print("持续测试已启动，按Ctrl+C停止")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n持续测试已停止")
        except:
            pass
    else:
        print("\n❌ 测试失败，请检查WMZC系统是否正常运行")
