#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 WMZC现代化GUI测试脚本
全面测试现代化GUI的功能完整性和性能
"""

import unittest
import time
import threading
import json
import os
import sys
from unittest.mock import Mock, patch

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestModernWMZCGUI(unittest.TestCase):
    """现代化GUI测试类"""
    
    def setUp(self):
        """测试前设置"""
        try:
            from modern_wmzc_gui import ModernWMZCGUI
            self.gui_class = ModernWMZCGUI
        except ImportError as e:
            self.skipTest(f"无法导入ModernWMZCGUI: {e}")
            
    def test_gui_initialization(self):
        """测试GUI初始化"""
        try:
            # 创建GUI实例（不显示窗口）
            gui = self.gui_class()
            
            # 检查基本属性
            self.assertIsNotNone(gui.root)
            self.assertIsNotNone(gui.sidebar)
            self.assertIsNotNone(gui.main_content)
            self.assertEqual(gui.theme_mode, "dark")
            
            print("✅ GUI初始化测试通过")
            
        except Exception as e:
            self.fail(f"GUI初始化失败: {e}")
            
    def test_tab_creation(self):
        """测试标签页创建"""
        try:
            gui = self.gui_class()
            
            # 检查标签页按钮是否创建
            expected_tabs = [
                "main_config", "strategy_market", "trading_records", "news",
                "indicators", "ai", "advanced_macd", "pin_strategy", "rsi_strategy",
                "stop_profit_loss", "equal_position", "risk_control", "indicator_sync",
                "backtest", "optimization", "lstm_prediction", "ai_assistant",
                "system_settings", "misc_config", "log_console"
            ]
            
            for tab_id in expected_tabs:
                self.assertIn(tab_id, gui.tab_buttons)
                
            print("✅ 标签页创建测试通过")
            
        except Exception as e:
            self.fail(f"标签页创建测试失败: {e}")
            
    def test_theme_switching(self):
        """测试主题切换"""
        try:
            gui = self.gui_class()
            
            # 测试主题切换
            original_theme = gui.theme_mode
            gui.toggle_theme()
            self.assertNotEqual(gui.theme_mode, original_theme)
            
            # 再次切换回来
            gui.toggle_theme()
            self.assertEqual(gui.theme_mode, original_theme)
            
            print("✅ 主题切换测试通过")
            
        except Exception as e:
            self.fail(f"主题切换测试失败: {e}")
            
    def test_tab_switching(self):
        """测试标签页切换"""
        try:
            gui = self.gui_class()
            
            # 测试切换到不同标签页
            test_tabs = ["main_config", "trading_records", "indicators", "ai"]
            
            for tab_id in test_tabs:
                gui.switch_tab(tab_id)
                self.assertEqual(gui.current_tab, tab_id)
                
            print("✅ 标签页切换测试通过")
            
        except Exception as e:
            self.fail(f"标签页切换测试失败: {e}")
            
    def test_responsive_layout(self):
        """测试响应式布局"""
        try:
            gui = self.gui_class()
            
            # 模拟窗口大小变化
            gui.window_width = 1000  # 小屏幕
            gui.adjust_sidebar_for_screen_size()
            self.assertTrue(gui.is_sidebar_collapsed)
            
            gui.window_width = 1500  # 大屏幕
            gui.adjust_sidebar_for_screen_size()
            self.assertFalse(gui.is_sidebar_collapsed)
            
            print("✅ 响应式布局测试通过")
            
        except Exception as e:
            self.fail(f"响应式布局测试失败: {e}")

class TestWMZCIntegration(unittest.TestCase):
    """WMZC集成测试类"""
    
    def setUp(self):
        """测试前设置"""
        try:
            from wmzc_integration import WMZCIntegrationBridge
            self.bridge_class = WMZCIntegrationBridge
        except ImportError as e:
            self.skipTest(f"无法导入WMZCIntegrationBridge: {e}")
            
    def test_bridge_initialization(self):
        """测试桥接器初始化"""
        try:
            # 创建模拟对象
            mock_gui = Mock()
            mock_wmzc = Mock()
            
            # 创建桥接器
            bridge = self.bridge_class(mock_gui, mock_wmzc)
            
            # 检查基本属性
            self.assertEqual(bridge.modern_gui, mock_gui)
            self.assertEqual(bridge.original_wmzc, mock_wmzc)
            self.assertIsInstance(bridge.data_cache, dict)
            
            print("✅ 桥接器初始化测试通过")
            
        except Exception as e:
            self.fail(f"桥接器初始化失败: {e}")
            
    def test_data_sync(self):
        """测试数据同步"""
        try:
            mock_gui = Mock()
            mock_wmzc = Mock()
            
            # 设置模拟方法
            mock_wmzc.get_current_price.return_value = 42500.0
            mock_wmzc.get_account_balance.return_value = {"USDT": 1000.0}
            
            bridge = self.bridge_class(mock_gui, mock_wmzc)
            
            # 执行数据同步
            bridge.sync_ticker_data()
            bridge.sync_account_info()
            
            # 检查数据缓存
            self.assertIn("ticker_data", bridge.data_cache)
            self.assertIn("account_info", bridge.data_cache)
            
            print("✅ 数据同步测试通过")
            
        except Exception as e:
            self.fail(f"数据同步测试失败: {e}")

class TestConfigurationMigration(unittest.TestCase):
    """配置迁移测试类"""
    
    def test_config_save_load(self):
        """测试配置保存和加载"""
        try:
            from modern_wmzc_gui import ModernWMZCGUI
            
            # 创建测试配置
            test_config = {
                "exchange": "OKX",
                "api_key": "test_key",
                "secret_key": "test_secret",
                "symbol": "BTC-USDT",
                "amount": "100"
            }
            
            # 保存配置
            config_file = "test_modern_wmzc_config.json"
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(test_config, f, indent=2, ensure_ascii=False)
                
            # 加载配置
            with open(config_file, "r", encoding="utf-8") as f:
                loaded_config = json.load(f)
                
            # 验证配置
            self.assertEqual(loaded_config["exchange"], "OKX")
            self.assertEqual(loaded_config["symbol"], "BTC-USDT")
            
            # 清理测试文件
            os.remove(config_file)
            
            print("✅ 配置保存加载测试通过")
            
        except Exception as e:
            self.fail(f"配置保存加载测试失败: {e}")

class TestPerformance(unittest.TestCase):
    """性能测试类"""
    
    def test_gui_startup_time(self):
        """测试GUI启动时间"""
        try:
            from modern_wmzc_gui import ModernWMZCGUI
            
            start_time = time.time()
            gui = ModernWMZCGUI()
            end_time = time.time()
            
            startup_time = end_time - start_time
            
            # GUI启动时间应该小于5秒
            self.assertLess(startup_time, 5.0)
            
            print(f"✅ GUI启动时间测试通过: {startup_time:.2f}秒")
            
        except Exception as e:
            self.fail(f"GUI启动时间测试失败: {e}")
            
    def test_tab_switching_performance(self):
        """测试标签页切换性能"""
        try:
            from modern_wmzc_gui import ModernWMZCGUI
            
            gui = ModernWMZCGUI()
            
            # 测试多次标签页切换
            test_tabs = ["main_config", "trading_records", "indicators", "ai"]
            
            start_time = time.time()
            for _ in range(10):
                for tab_id in test_tabs:
                    gui.switch_tab(tab_id)
            end_time = time.time()
            
            switch_time = end_time - start_time
            
            # 40次切换应该在2秒内完成
            self.assertLess(switch_time, 2.0)
            
            print(f"✅ 标签页切换性能测试通过: {switch_time:.2f}秒")
            
        except Exception as e:
            self.fail(f"标签页切换性能测试失败: {e}")

def run_dependency_check():
    """运行依赖检查"""
    print("🔍 检查依赖库...")
    
    required_packages = [
        ('customtkinter', 'CustomTkinter'),
        ('tkinter', 'tkinter'),
        ('threading', 'threading'),
        ('json', 'json'),
        ('time', 'time'),
        ('datetime', 'datetime')
    ]
    
    missing_packages = []
    
    for package, display_name in required_packages:
        try:
            __import__(package)
            print(f"✅ {display_name} - 已安装")
        except ImportError:
            missing_packages.append(display_name)
            print(f"❌ {display_name} - 未安装")
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下依赖包: {', '.join(missing_packages)}")
        return False
    
    print("\n✅ 所有依赖库检查通过")
    return True

def run_functional_tests():
    """运行功能测试"""
    print("\n🧪 开始功能测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestModernWMZCGUI))
    test_suite.addTest(unittest.makeSuite(TestWMZCIntegration))
    test_suite.addTest(unittest.makeSuite(TestConfigurationMigration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

def run_performance_tests():
    """运行性能测试"""
    print("\n⚡ 开始性能测试...")
    
    # 创建性能测试套件
    perf_suite = unittest.TestSuite()
    perf_suite.addTest(unittest.makeSuite(TestPerformance))
    
    # 运行性能测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(perf_suite)
    
    return result.wasSuccessful()

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 WMZC现代化GUI全面测试")
    print("=" * 60)
    
    # 1. 依赖检查
    if not run_dependency_check():
        print("\n❌ 依赖检查失败，无法继续测试")
        return False
    
    # 2. 功能测试
    if not run_functional_tests():
        print("\n❌ 功能测试失败")
        return False
    
    # 3. 性能测试
    if not run_performance_tests():
        print("\n❌ 性能测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！WMZC现代化GUI准备就绪")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)
