#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 WMZC日志控制台测试工具
测试日志控制台的各项功能
"""

import tkinter as tk
from tkinter import ttk
import logging
import threading
import time
from datetime import datetime

class LogConsoleTestTool:
    """日志控制台测试工具"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("WMZC日志控制台测试工具")
        self.root.geometry("800x600")
        
        self.setup_ui()
        self.setup_logging()
        
    def setup_ui(self):
        """设置用户界面"""
        # 控制面板
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Button(control_frame, text="测试INFO日志", 
                  command=lambda: self.test_log("INFO")).pack(side="left", padx=5)
        ttk.But<PERSON>(control_frame, text="测试WARNING日志", 
                  command=lambda: self.test_log("WARNING")).pack(side="left", padx=5)
        ttk.Button(control_frame, text="测试ERROR日志", 
                  command=lambda: self.test_log("ERROR")).pack(side="left", padx=5)
        ttk.Button(control_frame, text="批量测试", 
                  command=self.batch_test).pack(side="left", padx=5)
        ttk.Button(control_frame, text="清空日志", 
                  command=self.clear_log).pack(side="left", padx=5)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.root, text="日志输出")
        log_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=20, wrap=tk.WORD,
                               font=("Consolas", 9), bg="black", fg="white")
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # 配置日志颜色
        self.log_text.tag_config("INFO", foreground="lightgreen")
        self.log_text.tag_config("WARNING", foreground="yellow")
        self.log_text.tag_config("ERROR", foreground="red")
        self.log_text.tag_config("DEBUG", foreground="lightblue")
        
        self.log_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief="sunken")
        status_bar.pack(fill="x", side="bottom")
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建自定义日志处理器
        class GUILogHandler(logging.Handler):
            def __init__(self, gui_callback):
                super().__init__()
                self.gui_callback = gui_callback
            
            def emit(self, record):
                try:
                    msg = self.format(record)
                    self.gui_callback(msg, record.levelname)
                except:
                    pass
        
        # 配置日志
        self.logger = logging.getLogger('wmzc_test')
        self.logger.setLevel(logging.DEBUG)
        
        # 添加GUI处理器
        gui_handler = GUILogHandler(self.add_log_message)
        formatter = logging.Formatter('[%(asctime)s] %(levelname)s - %(message)s',
                                    datefmt='%H:%M:%S')
        gui_handler.setFormatter(formatter)
        self.logger.addHandler(gui_handler)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def add_log_message(self, message, level="INFO"):
        """添加日志消息到GUI"""
        try:
            self.log_text.config(state=tk.NORMAL)
            
            # 插入消息
            self.log_text.insert(tk.END, message + "\n")
            
            # 设置颜色
            line_start = self.log_text.index("end-2c linestart")
            line_end = self.log_text.index("end-2c lineend")
            self.log_text.tag_add(level, line_start, line_end)
            
            # 自动滚动
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)
            
            # 更新状态
            self.status_var.set(f"最后日志: {level} - {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            print(f"GUI日志更新失败: {e}")
    
    def test_log(self, level):
        """测试指定级别的日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        message = f"测试{level}级别日志消息 - {timestamp}"
        
        if level == "INFO":
            self.logger.info(message)
        elif level == "WARNING":
            self.logger.warning(message)
        elif level == "ERROR":
            self.logger.error(message)
        elif level == "DEBUG":
            self.logger.debug(message)
    
    def batch_test(self):
        """批量测试日志"""
        def run_batch():
            for i in range(5):
                self.test_log("INFO")
                time.sleep(0.2)
                self.test_log("WARNING")
                time.sleep(0.2)
                self.test_log("ERROR")
                time.sleep(0.2)
        
        threading.Thread(target=run_batch, daemon=True).start()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete("1.0", tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.add_log_message("🗑️ 日志已清空", "INFO")
    
    def run(self):
        """运行测试工具"""
        self.add_log_message("🧪 日志控制台测试工具已启动", "INFO")
        self.add_log_message("💡 点击按钮测试不同级别的日志输出", "INFO")
        self.root.mainloop()

def main():
    """主函数"""
    print("🧪 启动WMZC日志控制台测试工具...")
    
    try:
        test_tool = LogConsoleTestTool()
        test_tool.run()
    except Exception as e:
        print(f"❌ 测试工具启动失败: {e}")

if __name__ == "__main__":
    main()
