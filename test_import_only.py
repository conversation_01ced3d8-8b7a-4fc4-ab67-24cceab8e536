#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试仅导入WMZC模块
"""

import asyncio
import threading
import time
import sys

def test_import():
    """测试导入"""
    print("🔧 测试WMZC导入...")
    
    try:
        # 设置事件循环
        print("1. 创建事件循环...")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 在后台线程中运行事件循环
        def run_event_loop():
            try:
                print("   事件循环开始运行...")
                loop.run_forever()
            except Exception as e:
                print(f"   事件循环异常: {e}")
        
        loop_thread = threading.Thread(target=run_event_loop, daemon=True)
        loop_thread.start()
        print("✅ 事件循环已启动")
        
        # 等待一下确保事件循环启动
        time.sleep(0.5)
        
        # 测试导入WMZC
        print("2. 开始导入WMZC...")
        sys.stdout.flush()  # 强制刷新输出
        
        import WMZC
        print("✅ WMZC导入成功")
        
        print("3. 测试完成，清理资源...")
        loop.call_soon_threadsafe(loop.stop)
        loop_thread.join(timeout=2)
        print("✅ 测试完成")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_import()
