#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版WMZC系统BUG检测器
优化正则表达式，减少误报，提高检测精度
"""

import ast
import re
import os
import json
import time
from typing import List, Dict, Any

class ImprovedWMZCBugDetector:
    """改进版WMZC系统BUG检测器"""
    
    def __init__(self):
        self.bugs_found = []
        self.files_to_check = [
            'WMZC.py',
            'Global_Position_Controller.py', 
            'batch_order_manager.py',
            'exchange_rate_limiter.py',
            'smart_retry_handler.py',
            'order_book_manager.py',
            'optimization_config_parameters.py'
        ]
        self.config_files = [
            'wmzc_config.json',
            'trading_config.json',
            'user_settings.json'
        ]
    
    def add_bug(self, bug_type: str, severity: str, description: str, file_name: str, line_num: int = None):
        """添加发现的BUG"""
        self.bugs_found.append({
            'type': bug_type,
            'severity': severity,
            'description': description,
            'file': file_name,
            'line': line_num,
            'timestamp': time.time()
        })
    
    def check_syntax_and_structure(self):
        """检测语法与结构完整性"""
        print("📋 阶段1: 语法与结构完整性检测")
        
        for file_name in self.files_to_check:
            if not os.path.exists(file_name):
                continue
                
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 语法检查
                try:
                    ast.parse(content)
                    print(f"✅ {file_name} 语法检查通过")
                except SyntaxError as e:
                    self.add_bug(
                        "语法错误", "🔴 严重",
                        f"语法错误: {e.msg}",
                        file_name, e.lineno
                    )
                
                # 检查异步编程违规 - 改进版
                self._check_async_violations_improved(content, file_name)
                
            except Exception as e:
                self.add_bug(
                    "文件读取错误", "🟠 重要",
                    f"无法读取文件: {e}",
                    file_name
                )
    
    def _check_async_violations_improved(self, content: str, file_name: str):
        """改进版异步编程违规检测"""
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # 改进的time.sleep检测 - 排除注释和字符串
            if self._is_real_time_sleep(line):
                self.add_bug(
                    "异步编程违规", "🔴 严重",
                    "使用time.sleep()阻塞操作，应使用await asyncio.sleep()",
                    file_name, i
                )
            
            # 改进的异步调用检测 - 更精确的模式
            if self._is_missing_await_call(line, lines, i):
                self.add_bug(
                    "异步调用错误", "🟡 中等",
                    "可能缺少await的异步方法调用",
                    file_name, i
                )
    
    def _is_real_time_sleep(self, line: str) -> bool:
        """检测真正的time.sleep调用，排除误报"""
        # 去除注释
        line_without_comment = line.split('#')[0].strip()
        
        # 排除空行
        if not line_without_comment:
            return False
        
        # 排除字符串中的time.sleep
        if self._is_in_string(line_without_comment, 'time.sleep'):
            return False
        
        # 精确匹配time.sleep调用
        pattern = r'\btime\.sleep\s*\('
        return bool(re.search(pattern, line_without_comment))
    
    def _is_missing_await_call(self, line: str, lines: List[str], line_num: int) -> bool:
        """检测可能缺少await的异步调用，减少误报"""
        line_clean = line.split('#')[0].strip()
        
        # 排除空行和注释
        if not line_clean:
            return False
        
        # 排除已有await的行
        if 'await' in line_clean:
            return False
        
        # 排除函数定义行
        if line_clean.strip().startswith('def ') or line_clean.strip().startswith('async def '):
            return False
        
        # 排除import语句
        if 'import' in line_clean:
            return False
        
        # 检查是否在async函数内
        if not self._is_in_async_function(lines, line_num):
            return False
        
        # 改进的异步方法调用模式
        async_patterns = [
            r'\w+\.(fetch_|get_|post_|put_|delete_|send_|execute_|run_|process_|handle_)\w*\s*\(',
            r'\w+\.(async_\w+)\s*\(',
            r'\w+\.(\w*_async)\s*\(',
        ]
        
        for pattern in async_patterns:
            if re.search(pattern, line_clean):
                return True
        
        return False
    
    def _is_in_string(self, line: str, target: str) -> bool:
        """检查目标字符串是否在字符串字面量中"""
        # 简单的字符串检测，可以进一步改进
        in_single_quote = False
        in_double_quote = False
        
        for i, char in enumerate(line):
            if char == "'" and not in_double_quote:
                in_single_quote = not in_single_quote
            elif char == '"' and not in_single_quote:
                in_double_quote = not in_double_quote
            
            # 检查是否在字符串中找到目标
            if (in_single_quote or in_double_quote) and line[i:i+len(target)] == target:
                return True
        
        return False
    
    def _is_in_async_function(self, lines: List[str], current_line: int) -> bool:
        """检查当前行是否在async函数内"""
        # 向上查找最近的函数定义
        for i in range(current_line - 1, -1, -1):
            line = lines[i].strip()
            if line.startswith('async def '):
                return True
            elif line.startswith('def ') and not line.startswith('def async'):
                return False
            elif line.startswith('class '):
                return False
        return False
    
    def check_resource_management_improved(self):
        """改进版资源管理检测"""
        print("🔄 阶段2: 资源管理检测（改进版）")
        
        for file_name in self.files_to_check:
            if not os.path.exists(file_name):
                continue
                
            with open(file_name, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                # 改进的文件操作检测 - 只检测真正的文件open
                if self._is_real_file_open(line, lines, i):
                    self.add_bug(
                        "资源泄漏", "🟠 重要",
                        "文件打开后可能未正确关闭",
                        file_name, i
                    )
    
    def _is_real_file_open(self, line: str, lines: List[str], line_num: int) -> bool:
        """检测真正的文件open操作，排除误报"""
        line_clean = line.split('#')[0].strip()
        
        # 排除空行
        if not line_clean:
            return False
        
        # 排除webbrowser.open
        if 'webbrowser.open' in line_clean:
            return False
        
        # 排除函数名包含open的情况
        if re.search(r'\w+_open\s*\(', line_clean) or re.search(r'open_\w+\s*\(', line_clean):
            return False
        
        # 排除已经在with语句中的open
        if 'with' in line_clean and 'open(' in line_clean:
            return False
        
        # 检测真正的文件open调用
        file_open_pattern = r'\bopen\s*\(\s*["\'].*["\']'
        if re.search(file_open_pattern, line_clean):
            # 检查是否在with语句的上下文中
            context_lines = lines[max(0, line_num-3):line_num+2]
            context = ' '.join(context_lines)
            if 'with' not in context:
                return True
        
        return False
    
    def check_business_logic_improved(self):
        """改进版业务逻辑检测"""
        print("📊 阶段3: 业务逻辑检测（改进版）")
        
        if os.path.exists('WMZC.py'):
            with open('WMZC.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                # 改进的除零检测 - 排除已有保护的情况
                if self._is_real_division_risk(line, lines, i):
                    self.add_bug(
                        "除零风险", "🟡 中等",
                        "可能存在除零错误风险",
                        'WMZC.py', i
                    )
    
    def _is_real_division_risk(self, line: str, lines: List[str], line_num: int) -> bool:
        """检测真正的除零风险，排除已有保护的情况"""
        line_clean = line.split('#')[0].strip()
        
        if not line_clean:
            return False
        
        # 检测除法操作
        division_pattern = r'\/\s*[a-zA-Z_]\w*(?!\s*[!=<>])'
        if not re.search(division_pattern, line_clean):
            return False
        
        # 检查上下文是否已有保护
        context_lines = lines[max(0, line_num-5):line_num+2]
        context = ' '.join(context_lines).lower()
        
        # 如果已有保护措施，则不报告
        protection_patterns = [
            r'if.*!=\s*0',
            r'if.*>\s*0',
            r'if.*<\s*0',
            r'try:',
            r'except.*zerodivision',
            r'abs\(',
            r'math\.fabs\('
        ]
        
        for pattern in protection_patterns:
            if re.search(pattern, context):
                return False
        
        return True
    
    def run_improved_detection(self):
        """运行改进版全面检测"""
        print("🚀 开始改进版WMZC系统BUG检测...")
        print(f"检测文件数量: {len(self.files_to_check)}")
        
        start_time = time.time()
        
        # 执行各阶段检测
        self.check_syntax_and_structure()
        self.check_resource_management_improved()
        self.check_business_logic_improved()
        
        end_time = time.time()
        
        # 生成检测报告
        self.generate_improved_report(end_time - start_time)
    
    def generate_improved_report(self, detection_time: float):
        """生成改进版检测报告"""
        print(f"\n📊 改进版检测完成，耗时: {detection_time:.2f}秒")
        print(f"发现BUG总数: {len(self.bugs_found)}")
        
        if not self.bugs_found:
            print("🎉 恭喜！未发现任何BUG，系统状态良好！")
            return
        
        # 按严重程度分类
        severity_counts = {}
        for bug in self.bugs_found:
            severity = bug['severity']
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        print("\n📋 BUG分类统计:")
        for severity, count in severity_counts.items():
            print(f"  {severity}: {count}个")
        
        print("\n🔍 详细BUG列表:")
        for i, bug in enumerate(self.bugs_found, 1):
            line_info = f" (行{bug['line']})" if bug['line'] else ""
            print(f"{i}. {bug['severity']} {bug['type']}")
            print(f"   文件: {bug['file']}{line_info}")
            print(f"   描述: {bug['description']}")
            print()
        
        # 保存报告到文件
        report_data = {
            'detection_time': detection_time,
            'total_bugs': len(self.bugs_found),
            'severity_counts': severity_counts,
            'bugs': self.bugs_found,
            'timestamp': time.time(),
            'detector_version': 'improved_v2.0'
        }
        
        with open('improved_bug_detection_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print("📄 改进版详细报告已保存到: improved_bug_detection_report.json")

if __name__ == "__main__":
    detector = ImprovedWMZCBugDetector()
    detector.run_improved_detection()
