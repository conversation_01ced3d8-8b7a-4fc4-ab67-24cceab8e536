#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试异步修复的简单脚本
"""

import asyncio
import threading
import time

def test_async_fix():
    """测试异步修复"""
    print("🔧 测试异步修复...")
    
    try:
        # 设置事件循环
        print("1. 创建事件循环...")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 在后台线程中运行事件循环
        def run_event_loop():
            try:
                print("   事件循环开始运行...")
                loop.run_forever()
            except Exception as e:
                print(f"   事件循环异常: {e}")
        
        loop_thread = threading.Thread(target=run_event_loop, daemon=True)
        loop_thread.start()
        print("✅ 事件循环已启动")
        
        # 等待一下确保事件循环启动
        time.sleep(0.5)
        
        # 测试导入WMZC
        print("2. 测试导入WMZC...")
        try:
            import WMZC
            print("✅ WMZC导入成功")
            
            # 测试启动延迟任务
            print("3. 测试启动延迟任务...")
            if hasattr(WMZC, 'start_delayed_async_tasks'):
                future = asyncio.run_coroutine_threadsafe(WMZC.start_delayed_async_tasks(), loop)
                try:
                    future.result(timeout=5)
                    print("✅ 延迟任务启动成功")
                except Exception as e:
                    print(f"⚠️ 延迟任务启动失败: {e}")
            else:
                print("⚠️ start_delayed_async_tasks函数不存在")
            
            print("4. 测试完成，清理资源...")
            loop.call_soon_threadsafe(loop.stop)
            loop_thread.join(timeout=2)
            print("✅ 测试完成")
            
        except Exception as e:
            print(f"❌ WMZC导入失败: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_async_fix()
