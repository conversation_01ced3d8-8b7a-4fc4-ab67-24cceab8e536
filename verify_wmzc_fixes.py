#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
✅ WMZC修复效果验证脚本
验证所有修复是否生效，确保系统稳定性
"""

import os
import json
import subprocess
from datetime import datetime

class WMZCFixVerifier:
    """WMZC修复验证器"""
    
    def __init__(self):
        self.current_dir = os.getcwd()
        self.wmzc_config_dir = os.path.expanduser("~/.wmzc_trading")
        
        self.config_files = [
            'trading_config.json',
            'wmzc_config.json',
            'user_settings.json',
            'misc_optimization_config.json',
            'ai_config.json'
        ]
        
        self.test_api_patterns = [
            'da636867-490f-4e3e-81b2-870841afb860',
            'C15B6EE0CF3FFDEE5834865D3839325E',
            'Mx123456@'
        ]
        
        self.verification_results = {}
    
    def run_comprehensive_verification(self):
        """运行综合验证"""
        print("✅ WMZC修复效果验证")
        print("=" * 60)
        
        # 1. 验证配置持久化修复
        self.verify_config_persistence()
        
        # 2. 验证配置警告清理
        self.verify_config_warnings_cleaned()
        
        # 3. 验证系统稳定性修复
        self.verify_system_stability()
        
        # 4. 验证配置文件统一
        self.verify_config_unification()
        
        # 5. 生成验证报告
        self.generate_verification_report()
        
        return self.calculate_overall_score()
    
    def verify_config_persistence(self):
        """验证配置持久化修复"""
        print("\n🔧 验证配置持久化修复...")
        
        persistence_score = 0
        max_score = 4
        
        # 检查1: WMZC.py中的修复
        if os.path.exists('WMZC.py'):
            try:
                with open('WMZC.py', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if '🔒 检测到用户配置，跳过自动初始化' in content:
                    print("  ✅ _auto_init_config函数修复已应用")
                    persistence_score += 1
                else:
                    print("  ❌ _auto_init_config函数修复未应用")
            except Exception as e:
                print(f"  ❌ 检查WMZC.py失败: {e}")
        
        # 检查2: 统一配置管理器
        if os.path.exists('unified_config_manager.py'):
            print("  ✅ 统一配置管理器已创建")
            persistence_score += 1
        else:
            print("  ❌ 统一配置管理器缺失")
        
        # 检查3: 配置同步脚本
        if os.path.exists('sync_configs.py'):
            print("  ✅ 配置同步脚本已创建")
            persistence_score += 1
        else:
            print("  ❌ 配置同步脚本缺失")
        
        # 检查4: 配置保护标记
        protected_configs = 0
        for config_file in self.config_files:
            if self.check_config_protection(config_file):
                protected_configs += 1
        
        if protected_configs >= 3:
            print(f"  ✅ 配置保护标记已添加 ({protected_configs}/{len(self.config_files)})")
            persistence_score += 1
        else:
            print(f"  ⚠️ 配置保护标记不足 ({protected_configs}/{len(self.config_files)})")
        
        self.verification_results['config_persistence'] = {
            'score': persistence_score,
            'max_score': max_score,
            'percentage': (persistence_score / max_score) * 100
        }
        
        print(f"  📊 配置持久化修复评分: {persistence_score}/{max_score} ({(persistence_score/max_score)*100:.1f}%)")
    
    def verify_config_warnings_cleaned(self):
        """验证配置警告清理"""
        print("\n🧹 验证配置警告清理...")
        
        warning_score = 0
        max_score = 3
        
        # 检查1: 当前目录配置文件
        current_clean = self.check_directory_clean(self.current_dir, "当前目录")
        if current_clean:
            warning_score += 1
        
        # 检查2: WMZC配置目录
        wmzc_clean = self.check_directory_clean(self.wmzc_config_dir, "WMZC配置目录")
        if wmzc_clean:
            warning_score += 1
        
        # 检查3: 清理标记存在
        cleaned_files = 0
        for config_file in self.config_files:
            if self.check_cleaning_marker(config_file):
                cleaned_files += 1
        
        if cleaned_files >= 3:
            print(f"  ✅ 清理标记已添加 ({cleaned_files}/{len(self.config_files)})")
            warning_score += 1
        else:
            print(f"  ⚠️ 清理标记不足 ({cleaned_files}/{len(self.config_files)})")
        
        self.verification_results['config_warnings'] = {
            'score': warning_score,
            'max_score': max_score,
            'percentage': (warning_score / max_score) * 100
        }
        
        print(f"  📊 配置警告清理评分: {warning_score}/{max_score} ({(warning_score/max_score)*100:.1f}%)")
    
    def verify_system_stability(self):
        """验证系统稳定性修复"""
        print("\n🛡️ 验证系统稳定性修复...")
        
        stability_score = 0
        max_score = 3
        
        # 检查1: 安全技术指标模块
        if os.path.exists('safe_indicators.py'):
            print("  ✅ 安全技术指标模块已创建")
            stability_score += 1
        else:
            print("  ❌ 安全技术指标模块缺失")
        
        # 检查2: 异常处理保护
        if os.path.exists('safe_indicators.py'):
            try:
                with open('safe_indicators.py', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'safe_dataframe_value' in content and 'safe_api_call' in content:
                    print("  ✅ 异常处理保护函数已实现")
                    stability_score += 1
                else:
                    print("  ⚠️ 异常处理保护函数不完整")
            except Exception as e:
                print(f"  ❌ 检查异常处理保护失败: {e}")
        
        # 检查3: 配置备份
        backup_dirs = [d for d in os.listdir('.') if d.startswith('wmzc_comprehensive_backup_')]
        if backup_dirs:
            print(f"  ✅ 配置备份已创建: {len(backup_dirs)} 个")
            stability_score += 1
        else:
            print("  ❌ 配置备份缺失")
        
        self.verification_results['system_stability'] = {
            'score': stability_score,
            'max_score': max_score,
            'percentage': (stability_score / max_score) * 100
        }
        
        print(f"  📊 系统稳定性修复评分: {stability_score}/{max_score} ({(stability_score/max_score)*100:.1f}%)")
    
    def verify_config_unification(self):
        """验证配置文件统一"""
        print("\n🔄 验证配置文件统一...")
        
        unification_score = 0
        max_score = 3
        
        # 检查1: 统一管理器存在
        if os.path.exists('unified_config_manager.py'):
            print("  ✅ 统一配置管理器存在")
            unification_score += 1
        else:
            print("  ❌ 统一配置管理器缺失")
        
        # 检查2: 配置文件同步状态
        sync_status = self.check_config_sync_status()
        if sync_status:
            print("  ✅ 配置文件同步状态良好")
            unification_score += 1
        else:
            print("  ⚠️ 配置文件同步状态需要改善")
        
        # 检查3: WMZC配置目录结构
        if os.path.exists(self.wmzc_config_dir):
            wmzc_files = [f for f in self.config_files if os.path.exists(os.path.join(self.wmzc_config_dir, f))]
            if len(wmzc_files) >= 4:
                print(f"  ✅ WMZC配置目录结构完整 ({len(wmzc_files)}/{len(self.config_files)})")
                unification_score += 1
            else:
                print(f"  ⚠️ WMZC配置目录结构不完整 ({len(wmzc_files)}/{len(self.config_files)})")
        else:
            print("  ❌ WMZC配置目录不存在")
        
        self.verification_results['config_unification'] = {
            'score': unification_score,
            'max_score': max_score,
            'percentage': (unification_score / max_score) * 100
        }
        
        print(f"  📊 配置文件统一评分: {unification_score}/{max_score} ({(unification_score/max_score)*100:.1f}%)")
    
    def check_config_protection(self, config_file):
        """检查配置保护标记"""
        file_path = os.path.join(self.current_dir, config_file)
        
        if not os.path.exists(file_path):
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config.get('_CONFIG_PROTECTED', False)
        except:
            return False
    
    def check_directory_clean(self, directory, dir_name):
        """检查目录是否干净"""
        if not os.path.exists(directory):
            return False
        
        clean_files = 0
        total_files = 0
        
        for config_file in self.config_files:
            file_path = os.path.join(directory, config_file)
            if os.path.exists(file_path):
                total_files += 1
                if self.is_config_clean(file_path):
                    clean_files += 1
        
        if total_files == 0:
            return False
        
        clean_percentage = (clean_files / total_files) * 100
        print(f"  📊 {dir_name}: {clean_files}/{total_files} 个文件干净 ({clean_percentage:.1f}%)")
        
        return clean_percentage >= 80
    
    def is_config_clean(self, file_path):
        """检查配置文件是否干净"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            for key, value in config.items():
                if isinstance(value, str):
                    for pattern in self.test_api_patterns:
                        if pattern in value:
                            return False
            return True
        except:
            return False
    
    def check_cleaning_marker(self, config_file):
        """检查清理标记"""
        file_path = os.path.join(self.current_dir, config_file)
        
        if not os.path.exists(file_path):
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config.get('_TEST_API_CLEANED') is not None
        except:
            return False
    
    def check_config_sync_status(self):
        """检查配置同步状态"""
        try:
            # 检查主配置文件
            main_config_path = os.path.join(self.current_dir, 'trading_config.json')
            if not os.path.exists(main_config_path):
                return False
            
            with open(main_config_path, 'r', encoding='utf-8') as f:
                main_config = json.load(f)
            
            main_api_key = main_config.get('API_KEY', '')
            
            # 检查WMZC配置文件
            wmzc_config_path = os.path.join(self.wmzc_config_dir, 'wmzc_config.json')
            if os.path.exists(wmzc_config_path):
                with open(wmzc_config_path, 'r', encoding='utf-8') as f:
                    wmzc_config = json.load(f)
                
                wmzc_api_key = wmzc_config.get('okx_api_key', '')
                
                # 如果都为空或都相同，认为同步良好
                if (not main_api_key and not wmzc_api_key) or (main_api_key == wmzc_api_key):
                    return True
            
            return False
        except:
            return False
    
    def calculate_overall_score(self):
        """计算总体评分"""
        total_score = 0
        total_max = 0
        
        for category, result in self.verification_results.items():
            total_score += result['score']
            total_max += result['max_score']
        
        overall_percentage = (total_score / total_max) * 100 if total_max > 0 else 0
        
        print(f"\n📊 总体修复效果评分: {total_score}/{total_max} ({overall_percentage:.1f}%)")
        
        if overall_percentage >= 90:
            print("🎉 修复效果优秀！")
            return "EXCELLENT"
        elif overall_percentage >= 75:
            print("✅ 修复效果良好！")
            return "GOOD"
        elif overall_percentage >= 60:
            print("⚠️ 修复效果一般，建议进一步优化")
            return "FAIR"
        else:
            print("❌ 修复效果不佳，需要重新修复")
            return "POOR"
    
    def generate_verification_report(self):
        """生成验证报告"""
        print("\n📋 生成验证报告...")
        
        report = {
            'verification_time': datetime.now().isoformat(),
            'verification_results': self.verification_results,
            'overall_score': self.calculate_overall_score(),
            'recommendations': self.generate_recommendations()
        }
        
        try:
            with open('wmzc_verification_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print("  ✅ 验证报告已保存: wmzc_verification_report.json")
        except Exception as e:
            print(f"  ❌ 保存验证报告失败: {e}")
    
    def generate_recommendations(self):
        """生成建议"""
        recommendations = []
        
        for category, result in self.verification_results.items():
            if result['percentage'] < 100:
                if category == 'config_persistence':
                    recommendations.append("完善配置持久化机制")
                elif category == 'config_warnings':
                    recommendations.append("彻底清理配置警告")
                elif category == 'system_stability':
                    recommendations.append("加强系统稳定性保护")
                elif category == 'config_unification':
                    recommendations.append("完善配置文件统一管理")
        
        if not recommendations:
            recommendations.append("所有修复都已完美应用，继续保持！")
        
        return recommendations

def main():
    """主函数"""
    print("✅ WMZC修复效果验证工具")
    print("=" * 60)
    
    verifier = WMZCFixVerifier()
    
    try:
        overall_result = verifier.run_comprehensive_verification()
        
        print("\n" + "=" * 60)
        print("🎯 验证完成！")
        
        if overall_result in ['EXCELLENT', 'GOOD']:
            print("💡 建议下一步操作:")
            print("  1. 重新启动WMZC系统")
            print("  2. 在主配置页面填写真实API密钥")
            print("  3. 测试配置持久化效果")
            print("  4. 验证系统稳定性")
        else:
            print("💡 建议改进措施:")
            print("  1. 重新运行综合修复脚本")
            print("  2. 手动检查未通过的验证项")
            print("  3. 联系技术支持获取帮助")
        
        return overall_result in ['EXCELLENT', 'GOOD']
        
    except Exception as e:
        print(f"❌ 验证过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
