#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC优化效果简单验证
避免循环导入，直接测试优化模块
"""

import time
import psutil
import gc
import sys
import os

def test_performance_optimizer():
    """测试性能优化器"""
    print("🚀 测试性能优化器...")
    
    try:
        # 直接导入性能优化器
        sys.path.insert(0, os.getcwd())
        from performance_optimizer import AdvancedCacheManager, MemoryMonitor, PerformanceProfiler
        
        # 测试缓存管理器
        cache_manager = AdvancedCacheManager(max_memory_mb=10)
        
        # 缓存写入测试
        start_time = time.time()
        for i in range(100):
            cache_manager.set("test_cache", f"key_{i}", f"value_{i}", ttl=60)
        write_time = time.time() - start_time
        
        # 缓存读取测试
        start_time = time.time()
        hit_count = 0
        for i in range(100):
            value = cache_manager.get("test_cache", f"key_{i}")
            if value:
                hit_count += 1
        read_time = time.time() - start_time
        
        print(f"  ✅ 缓存写入: {100/write_time:.0f} ops/sec")
        print(f"  ✅ 缓存读取: {100/read_time:.0f} ops/sec")
        print(f"  ✅ 缓存命中率: {hit_count/100*100:.1f}%")
        
        # 测试内存监控
        memory_monitor = MemoryMonitor()
        memory_stats = memory_monitor.get_memory_stats()
        print(f"  ✅ 内存监控: {memory_stats.percent:.1f}% 使用率")
        
        # 测试性能分析器
        profiler = PerformanceProfiler()
        profiler.start_profile("test_operation")
        time.sleep(0.01)  # 模拟操作
        duration = profiler.end_profile("test_operation")
        print(f"  ✅ 性能分析: {duration*1000:.1f} ms")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 性能优化器测试失败: {e}")
        return False

def test_monitoring_system():
    """测试监控系统"""
    print("📊 测试监控系统...")
    
    try:
        from monitoring_system import AdvancedLogger, MetricsCollector, HealthChecker
        
        # 测试日志系统
        logger = AdvancedLogger()
        logger.start()
        
        # 记录测试日志
        logger.log("测试信息日志", "INFO")
        logger.log("测试警告日志", "WARNING")
        logger.log("测试错误日志", "ERROR")
        
        time.sleep(1)  # 等待日志处理
        
        # 获取日志统计
        log_stats = logger.get_stats()
        print(f"  ✅ 日志统计: {log_stats}")
        
        # 测试指标收集器
        metrics = MetricsCollector()
        
        # 记录测试指标
        for i in range(10):
            metrics.record_metric("test_latency", i * 10, "ms")
            metrics.record_metric("test_throughput", 100 - i, "ops/sec")
        
        # 获取指标摘要
        latency_summary = metrics.get_metric_summary("test_latency", 1)
        print(f"  ✅ 延迟指标: 平均 {latency_summary.get('avg', 0):.1f} ms")
        
        # 测试健康检查器
        health_checker = HealthChecker()
        
        def test_health_check():
            return {'status': 'healthy', 'message': '测试组件正常'}
        
        health_checker.register_check('test_component', test_health_check, 5)
        health_checker.start()
        
        time.sleep(1)  # 等待健康检查
        
        health_status = health_checker.get_health_status()
        print(f"  ✅ 健康状态: {health_status['overall']}")
        
        # 清理
        logger.stop()
        health_checker.stop()
        
        return True
        
    except Exception as e:
        print(f"  ❌ 监控系统测试失败: {e}")
        return False

def test_trading_loop_modules():
    """测试交易循环模块"""
    print("🔄 测试交易循环模块...")
    
    try:
        from trading_loop_modules import TradingLoopState
        
        # 测试状态管理
        state = TradingLoopState()
        print(f"  ✅ 状态管理: 心跳时间 {state.last_heartbeat}")
        print(f"  ✅ 错误计数: {state.error_count}")
        print(f"  ✅ 循环计数: {state.loop_count}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 交易循环模块测试失败: {e}")
        return False

def memory_usage_test():
    """内存使用测试"""
    print("💾 内存使用测试...")
    
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"  初始内存: {initial_memory:.1f} MB")
    
    # 创建一些数据
    test_data = []
    for i in range(1000):
        test_data.append(f"test_data_{i}" * 100)
    
    mid_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"  数据创建后: {mid_memory:.1f} MB (+{mid_memory - initial_memory:.1f} MB)")
    
    # 清理数据
    del test_data
    gc.collect()
    
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"  清理后: {final_memory:.1f} MB ({final_memory - initial_memory:+.1f} MB)")
    
    return True

def performance_benchmark():
    """性能基准测试"""
    print("⚡ 性能基准测试...")
    
    # CPU密集型测试
    start_time = time.time()
    result = sum(i * i for i in range(100000))
    cpu_time = time.time() - start_time
    print(f"  ✅ CPU测试: {cpu_time*1000:.1f} ms (结果: {result})")
    
    # 内存密集型测试
    start_time = time.time()
    data = [i for i in range(100000)]
    memory_time = time.time() - start_time
    print(f"  ✅ 内存测试: {memory_time*1000:.1f} ms (大小: {len(data)})")
    
    # I/O测试
    start_time = time.time()
    with open("test_file.tmp", "w") as f:
        for i in range(1000):
            f.write(f"test line {i}\n")
    
    with open("test_file.tmp", "r") as f:
        lines = f.readlines()
    
    os.remove("test_file.tmp")
    io_time = time.time() - start_time
    print(f"  ✅ I/O测试: {io_time*1000:.1f} ms (行数: {len(lines)})")
    
    return True

def generate_optimization_summary():
    """生成优化总结"""
    print("\n📋 优化总结")
    print("=" * 50)
    
    # 运行所有测试
    test_results = {
        'performance_optimizer': test_performance_optimizer(),
        'monitoring_system': test_monitoring_system(),
        'trading_loop_modules': test_trading_loop_modules(),
        'memory_usage': memory_usage_test(),
        'performance_benchmark': performance_benchmark()
    }
    
    # 计算成功率
    success_count = sum(test_results.values())
    total_count = len(test_results)
    success_rate = (success_count / total_count) * 100
    
    print(f"\n🎯 测试结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体成功率: {success_rate:.1f}% ({success_count}/{total_count})")
    
    if success_rate >= 90:
        status = "🎉 优秀"
        recommendation = "优化系统运行完美，所有模块正常工作"
    elif success_rate >= 70:
        status = "👍 良好"
        recommendation = "优化系统基本正常，建议修复失败的模块"
    elif success_rate >= 50:
        status = "⚠️ 一般"
        recommendation = "优化系统部分正常，需要进一步调试"
    else:
        status = "❌ 需要改进"
        recommendation = "优化系统存在严重问题，需要重新检查"
    
    print(f"系统状态: {status}")
    print(f"建议: {recommendation}")
    
    # 保存结果
    try:
        import json
        report = {
            'timestamp': time.time(),
            'test_results': test_results,
            'success_rate': success_rate,
            'status': status,
            'recommendation': recommendation
        }
        
        with open('optimization_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 测试报告已保存到 optimization_test_report.json")
        
    except Exception as e:
        print(f"⚠️ 保存报告失败: {e}")
    
    return test_results

if __name__ == "__main__":
    print("🎯 WMZC优化效果验证 (简化版)")
    print("=" * 50)
    
    try:
        results = generate_optimization_summary()
        print("\n🎉 验证完成!")
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
