#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置验证失败修复脚本
自动修复所有配置文件中的参数值，确保满足最小值要求
"""

import json
import os
import shutil
from datetime import datetime

def backup_config_file(file_path):
    """备份配置文件"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(file_path, backup_path)
        print(f"✅ 已备份配置文件: {backup_path}")
        return backup_path
    return None

def fix_config_values(config_data):
    """修复配置值，确保满足最小值要求"""
    fixes_applied = []
    
    # 定义最小值要求（基于WMZC.py中的配置验证规则）
    min_value_requirements = {
        'STOP_LOSS_PCT': 0.1,
        'TAKE_PROFIT_PCT': 0.1,
        'PROFIT_TARGET_PCT': 0.1,
        'MAX_POSITION_SIZE': 1,
        'RISK_PER_TRADE': 0.1,
        'max_position_size': 1,  # 字符串格式的配置
        'stop_loss_percent': 0.1,
        'take_profit_percent': 0.1,
    }
    
    # 递归修复配置值
    def fix_recursive(data, path=""):
        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                
                # 检查是否需要修复
                if key in min_value_requirements:
                    min_val = min_value_requirements[key]
                    
                    # 处理字符串格式的数值
                    if isinstance(value, str):
                        try:
                            numeric_value = float(value)
                            if numeric_value < min_val:
                                data[key] = str(min_val)
                                fixes_applied.append(f"{current_path}: {value} -> {min_val}")
                        except (ValueError, TypeError):
                            pass
                    
                    # 处理数值格式
                    elif isinstance(value, (int, float)):
                        if value < min_val:
                            data[key] = min_val
                            fixes_applied.append(f"{current_path}: {value} -> {min_val}")
                
                # 递归处理嵌套字典
                elif isinstance(value, dict):
                    fix_recursive(value, current_path)
                elif isinstance(value, list):
                    for i, item in enumerate(value):
                        if isinstance(item, dict):
                            fix_recursive(item, f"{current_path}[{i}]")
    
    fix_recursive(config_data)
    
    # 确保必需的字段存在
    required_fields = {
        'STOP_LOSS_PCT': 1.0,
        'TAKE_PROFIT_PCT': 2.0,
        'MAX_POSITION_SIZE': 1000.0,
        'RISK_PER_TRADE': 2.0,
    }
    
    for field, default_value in required_fields.items():
        if field not in config_data:
            config_data[field] = default_value
            fixes_applied.append(f"添加缺失字段 {field}: {default_value}")
    
    return fixes_applied

def fix_config_file(file_path):
    """修复单个配置文件"""
    print(f"\n🔧 修复配置文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"⚠️ 文件不存在: {file_path}")
        return False
    
    try:
        # 备份原文件
        backup_path = backup_config_file(file_path)
        
        # 读取配置文件
        with open(file_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 修复配置值
        fixes_applied = fix_config_values(config_data)
        
        if fixes_applied:
            # 保存修复后的配置
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 已修复 {len(fixes_applied)} 个配置项:")
            for fix in fixes_applied:
                print(f"   • {fix}")
        else:
            print("✅ 配置文件无需修复")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复配置验证失败问题")
    print("=" * 60)
    
    # 需要修复的配置文件列表
    config_files = [
        'wmzc_config.json',
        'trading_config.json',
        'user_settings.json',
        'misc_optimization_config.json',
        'ai_config.json'
    ]
    
    # 检查用户目录下的配置文件
    user_config_dir = os.path.expanduser('~/.wmzc_trading')
    if os.path.exists(user_config_dir):
        user_config_files = [
            os.path.join(user_config_dir, 'wmzc_config.json'),
            os.path.join(user_config_dir, 'trading_config.json'),
            os.path.join(user_config_dir, 'user_settings.json'),
            os.path.join(user_config_dir, 'misc_optimization_config.json'),
            os.path.join(user_config_dir, 'ai_config.json')
        ]
        config_files.extend(user_config_files)
    
    success_count = 0
    total_count = 0
    
    for config_file in config_files:
        if os.path.exists(config_file):
            total_count += 1
            if fix_config_file(config_file):
                success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎉 修复完成: {success_count}/{total_count} 个配置文件修复成功")
    
    if success_count == total_count:
        print("✅ 所有配置文件已修复，配置验证失败问题应该已解决")
    else:
        print("⚠️ 部分配置文件修复失败，请检查错误信息")
    
    print("\n💡 建议:")
    print("1. 重新启动WMZC系统")
    print("2. 检查是否还有配置验证失败的警告")
    print("3. 如果问题仍然存在，请检查配置文件格式")

if __name__ == "__main__":
    main()
