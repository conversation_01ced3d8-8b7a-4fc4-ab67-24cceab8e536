#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面Bug修复验证脚本
验证P0和P1级别Bug修复的完整性和有效性
"""

import time
import asyncio
import sys
import traceback

def test_p0_infinite_recursion_fix():
    """测试P0-2: 无限递归风险修复"""
    print("🔍 测试P0-2: 无限递归风险修复...")
    
    try:
        import WMZC
        
        # 测试1: 内存管理器的_estimate_size函数
        print("📦 测试1: 内存管理器递归深度限制...")
        if hasattr(WMZC, 'MemoryManager'):
            memory_manager = WMZC.MemoryManager()
            
            # 创建深度嵌套的数据结构
            nested_data = {'level': 0}
            current = nested_data
            for i in range(20):  # 创建20层嵌套
                current['next'] = {'level': i + 1}
                current = current['next']
            
            # 测试递归深度限制
            try:
                size = memory_manager._estimate_size(nested_data)
                if size > 0:
                    print(f"✅ 内存管理器递归深度限制正常，估算大小: {size}字节")
                else:
                    print("❌ 内存管理器递归深度限制失败")
                    return False
            except RecursionError:
                print("❌ 仍然存在无限递归风险")
                return False
        else:
            print("❌ MemoryManager类不存在")
            return False
        
        # 测试2: 缓存管理器的_estimate_size函数
        print("📦 测试2: 缓存管理器递归深度限制...")
        if hasattr(WMZC, 'SmartCacheManager'):
            cache_manager = WMZC.SmartCacheManager()
            
            # 创建循环引用的数据结构
            circular_data = {'name': 'test'}
            circular_data['self'] = circular_data  # 循环引用
            
            try:
                size = cache_manager._estimate_size(circular_data)
                if size > 0:
                    print(f"✅ 缓存管理器递归深度限制正常，估算大小: {size}字节")
                else:
                    print("❌ 缓存管理器递归深度限制失败")
                    return False
            except RecursionError:
                print("❌ 仍然存在无限递归风险")
                return False
        else:
            print("❌ SmartCacheManager类不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 无限递归测试失败: {e}")
        traceback.print_exc()
        return False

def test_p0_resource_leak_fix():
    """测试P0-3: 资源泄漏风险修复"""
    print("\n🔍 测试P0-3: 资源泄漏风险修复...")
    
    try:
        import WMZC
        
        # 测试1: 高性能同步包装器资源管理
        print("📦 测试1: 高性能同步包装器资源管理...")
        if hasattr(WMZC, 'HighPerformanceSyncWrapper'):
            wrapper = WMZC.HighPerformanceSyncWrapper()
            
            # 检查初始状态
            if hasattr(wrapper, '_is_shutdown') and not wrapper._is_shutdown:
                print("✅ 同步包装器初始状态正常")
            else:
                print("❌ 同步包装器初始状态异常")
                return False
            
            # 测试资源清理
            wrapper.cleanup()
            if wrapper._is_shutdown:
                print("✅ 同步包装器资源清理正常")
            else:
                print("❌ 同步包装器资源清理失败")
                return False
        else:
            print("❌ HighPerformanceSyncWrapper类不存在")
            return False
        
        # 测试2: 全局任务管理器
        print("📦 测试2: 全局任务管理器...")
        if hasattr(WMZC, 'GlobalTaskManager'):
            task_manager = WMZC.GlobalTaskManager()
            
            # 检查统计信息
            stats = task_manager.get_stats()
            if isinstance(stats, dict) and 'total_tasks' in stats:
                print(f"✅ 全局任务管理器正常，当前任务: {stats['total_tasks']}")
            else:
                print("❌ 全局任务管理器统计信息异常")
                return False
        else:
            print("❌ GlobalTaskManager类不存在")
            return False
        
        # 测试3: 全局实例存在性
        print("📦 测试3: 全局实例存在性...")
        if hasattr(WMZC, '_global_task_manager'):
            print("✅ 全局任务管理器实例存在")
        else:
            print("❌ 全局任务管理器实例不存在")
            return False
            
        if hasattr(WMZC, '_high_performance_sync_wrapper'):
            print("✅ 全局同步包装器实例存在")
        else:
            print("❌ 全局同步包装器实例不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 资源泄漏测试失败: {e}")
        traceback.print_exc()
        return False

def test_p1_exception_handling_fix():
    """测试P1-1: 异常处理不完善修复"""
    print("\n🔍 测试P1-1: 异常处理不完善修复...")
    
    try:
        import WMZC
        
        # 测试1: 检查代码中是否还有裸露的except块
        print("📦 测试1: 检查裸露except块修复...")
        
        # 读取源代码文件
        with open('WMZC.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找裸露的except块
        import re
        bare_except_pattern = r'except:\s*$'
        matches = re.findall(bare_except_pattern, content, re.MULTILINE)
        
        if len(matches) < 30:  # 应该显著减少裸露except块
            print(f"✅ 裸露except块已减少到{len(matches)}个")
        else:
            print(f"❌ 仍有{len(matches)}个裸露except块")
            return False
        
        # 测试2: 检查具体异常处理
        print("📦 测试2: 检查具体异常处理...")
        specific_except_pattern = r'except\s+\([^)]+\)\s+as\s+\w+:'
        specific_matches = re.findall(specific_except_pattern, content)
        
        if len(specific_matches) > 10:  # 应该有更多具体的异常处理
            print(f"✅ 具体异常处理增加到{len(specific_matches)}个")
        else:
            print(f"❌ 具体异常处理仍然不足: {len(specific_matches)}个")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 异常处理测试失败: {e}")
        traceback.print_exc()
        return False

def test_p1_config_validation_fix():
    """测试P1-2: 配置验证不足修复"""
    print("\n🔍 测试P1-2: 配置验证不足修复...")
    
    try:
        import WMZC
        
        # 测试1: 配置验证器增强
        print("📦 测试1: 配置验证器增强...")
        if hasattr(WMZC, 'ConfigValidator'):
            validator = WMZC.ConfigValidator()
            
            # 测试明显的测试值检测
            test_config = {
                'OKX_API_KEY': 'test_api_key',
                'OKX_SECRET_KEY': 'demo_secret',
                'OKX_PASSPHRASE': 'your_passphrase'
            }
            
            result = validator.validate_config(test_config)
            
            if 'errors' in result and len(result['errors']) > 0:
                print(f"✅ 测试值检测正常，发现{len(result['errors'])}个错误")
            else:
                print("❌ 测试值检测失败")
                return False
            
            # 测试可疑模式检测
            suspicious_config = {
                'OKX_API_KEY': 'test123',
                'OKX_SECRET_KEY': 'demo_key_123',
                'OKX_PASSPHRASE': 'xxxxxxxxxx'
            }
            
            result2 = validator.validate_config(suspicious_config)
            
            if 'warnings' in result2 and len(result2['warnings']) > 0:
                print(f"✅ 可疑模式检测正常，发现{len(result2['warnings'])}个警告")
            else:
                print("❌ 可疑模式检测失败")
                return False
        else:
            print("❌ ConfigValidator类不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        traceback.print_exc()
        return False

async def test_async_task_management():
    """测试异步任务管理"""
    print("\n🔍 测试异步任务管理...")
    
    try:
        import WMZC
        
        if hasattr(WMZC, '_global_task_manager'):
            task_manager = WMZC._global_task_manager
            
            # 创建测试任务
            async def test_task():
                await asyncio.sleep(0.1)
                return "test_complete"
            
            # 使用任务管理器创建任务
            task = task_manager.create_task(test_task(), name="test_task")
            
            if task is not None:
                print("✅ 任务创建成功")
                
                # 等待任务完成
                result = await task
                if result == "test_complete":
                    print("✅ 任务执行成功")
                else:
                    print("❌ 任务执行失败")
                    return False
            else:
                print("❌ 任务创建失败")
                return False
        else:
            print("❌ 全局任务管理器不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 异步任务管理测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 全面Bug修复验证")
    print("=" * 60)
    
    # P0级别Bug修复验证
    print("\n🔴 P0级别Bug修复验证")
    print("-" * 40)
    
    success_p0_2 = test_p0_infinite_recursion_fix()
    success_p0_3 = test_p0_resource_leak_fix()
    
    # P1级别Bug修复验证
    print("\n🟡 P1级别Bug修复验证")
    print("-" * 40)
    
    success_p1_1 = test_p1_exception_handling_fix()
    success_p1_2 = test_p1_config_validation_fix()
    
    # 异步功能验证
    print("\n🔄 异步功能验证")
    print("-" * 40)
    
    try:
        success_async = asyncio.run(test_async_task_management())
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        success_async = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 全面Bug修复验证总结")
    print("=" * 60)
    
    all_success = success_p0_2 and success_p0_3 and success_p1_1 and success_p1_2 and success_async
    
    if all_success:
        print("🎉 所有Bug修复验证成功！")
        print("✅ 修复内容:")
        print("   P0-2: ✅ 无限递归风险 - 递归深度限制")
        print("   P0-3: ✅ 资源泄漏风险 - 全局任务管理")
        print("   P1-1: ✅ 异常处理不完善 - 具体异常处理")
        print("   P1-2: ✅ 配置验证不足 - 增强测试值检测")
        print("   异步: ✅ 任务管理优化 - 防止任务泄漏")
        print("\n🚀 系统稳定性和安全性显著提升！")
    else:
        print("❌ 部分Bug修复需要进一步检查")
        print("📋 验证结果:")
        print(f"   P0-2 无限递归: {'✅' if success_p0_2 else '❌'}")
        print(f"   P0-3 资源泄漏: {'✅' if success_p0_3 else '❌'}")
        print(f"   P1-1 异常处理: {'✅' if success_p1_1 else '❌'}")
        print(f"   P1-2 配置验证: {'✅' if success_p1_2 else '❌'}")
        print(f"   异步任务管理: {'✅' if success_async else '❌'}")
    
    print("=" * 60)
    return all_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
