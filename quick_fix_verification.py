#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复验证 - 直接检查代码修复情况
"""

import os
import re

def check_liquidation_monitoring_fixes():
    """检查预估强平价监控修复"""
    print("🔍 检查预估强平价监控修复...")
    
    wmzc_file = "WMZC.py"
    if not os.path.exists(wmzc_file):
        print("❌ WMZC.py文件不存在")
        return False
    
    with open(wmzc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否还有self.root.after的调用
    root_after_matches = re.findall(r'self\.root\.after', content)
    
    if root_after_matches:
        print(f"❌ 仍有 {len(root_after_matches)} 个 self.root.after 调用未修复")
        
        # 显示前几个未修复的位置
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if 'self.root.after' in line:
                print(f"  行 {i}: {line.strip()}")
                if len([l for l in lines[:i] if 'self.root.after' in l]) >= 3:
                    print("  ...")
                    break
        return False
    else:
        print("✅ 所有 self.root.after 调用已修复为 self.after")
    
    # 检查是否有修复注释
    fix_comments = re.findall(r'# 🔧 修复.*TradingApp继承自tk\.Tk', content)
    if fix_comments:
        print(f"✅ 找到 {len(fix_comments)} 个修复注释")
    else:
        print("⚠️ 未找到修复注释")
    
    return True

def check_config_schema_fixes():
    """检查配置架构修复"""
    print("\n🔍 检查配置架构修复...")
    
    wmzc_file = "WMZC.py"
    with open(wmzc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查新增的配置字段
    required_fields = [
        'ENABLE_RSI', 'ENABLE_LOGGING', 'LOG_TO_CONSOLE',
        'KDJ_PERIOD', 'MACD_FAST', 'MACD_SLOW', 'MACD_SIGNAL',
        'STOP_LOSS', 'TAKE_PROFIT', 'api_key'
    ]
    
    missing_fields = []
    found_fields = []
    
    for field in required_fields:
        # 在CONFIG_SCHEMA中查找字段定义
        pattern = f"'{field}'.*:.*{{.*'type'"
        if re.search(pattern, content):
            found_fields.append(field)
        else:
            missing_fields.append(field)
    
    if not missing_fields:
        print(f"✅ 所有 {len(required_fields)} 个必要配置字段都已添加")
        return True
    else:
        print(f"❌ 仍缺少 {len(missing_fields)} 个配置字段: {missing_fields}")
        return False

def check_dataframe_safety_fixes():
    """检查DataFrame安全性修复"""
    print("\n🔍 检查DataFrame安全性修复...")
    
    wmzc_file = "WMZC.py"
    with open(wmzc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查_safe_dataframe_check函数是否存在
    if '_safe_dataframe_check' in content:
        print("✅ _safe_dataframe_check函数存在")
    else:
        print("❌ _safe_dataframe_check函数不存在")
        return False
    
    # 检查是否有直接的DataFrame布尔值判断
    dangerous_patterns = [
        r'if\s+df\s*:',  # if df:
        r'if\s+.*dataframe.*:',  # if dataframe:
        r'if\s+.*_result\s*:',  # if result:
    ]
    
    dangerous_matches = 0
    for pattern in dangerous_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        dangerous_matches += len(matches)
    
    if dangerous_matches > 0:
        print(f"⚠️ 发现 {dangerous_matches} 个可能的危险DataFrame判断")
    else:
        print("✅ 未发现危险的DataFrame布尔值判断")
    
    # 检查_safe_dataframe_check的使用频率
    safe_check_usage = len(re.findall(r'_safe_dataframe_check', content))
    print(f"✅ _safe_dataframe_check函数使用了 {safe_check_usage} 次")
    
    return True

def check_async_task_fixes():
    """检查异步任务修复"""
    print("\n🔍 检查异步任务修复...")
    
    wmzc_file = "WMZC.py"
    with open(wmzc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否使用了asyncio.get_running_loop()
    if 'asyncio.get_running_loop()' in content:
        print("✅ 使用了安全的 asyncio.get_running_loop()")
    else:
        print("⚠️ 未找到 asyncio.get_running_loop() 的使用")
    
    # 检查延迟启动机制
    if '_delayed_log_consumer_start' in content:
        print("✅ 日志消费者延迟启动机制存在")
    else:
        print("⚠️ 日志消费者延迟启动机制不存在")
    
    if '_delayed_trading_log_start' in content:
        print("✅ 交易日志延迟启动机制存在")
    else:
        print("⚠️ 交易日志延迟启动机制不存在")
    
    return True

def check_error_patterns():
    """检查常见错误模式"""
    print("\n🔍 检查常见错误模式...")
    
    wmzc_file = "WMZC.py"
    with open(wmzc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查可能导致错误的模式
    error_patterns = [
        (r'self\.root\.after', "self.root.after调用"),
        (r'if\s+df\s*:', "直接DataFrame布尔判断"),
        (r'asyncio\.get_event_loop\(\)', "过时的事件循环获取"),
    ]
    
    total_issues = 0
    for pattern, description in error_patterns:
        matches = re.findall(pattern, content)
        if matches:
            print(f"❌ 发现 {len(matches)} 个 {description}")
            total_issues += len(matches)
        else:
            print(f"✅ 未发现 {description}")
    
    if total_issues == 0:
        print("🎉 未发现常见错误模式")
        return True
    else:
        print(f"⚠️ 总共发现 {total_issues} 个潜在问题")
        return False

def main():
    """主验证函数"""
    print("=" * 60)
    print("🔧 WMZC系统快速修复验证")
    print("=" * 60)
    
    # 执行所有检查
    checks = [
        ("预估强平价监控修复", check_liquidation_monitoring_fixes),
        ("配置架构修复", check_config_schema_fixes),
        ("DataFrame安全性修复", check_dataframe_safety_fixes),
        ("异步任务修复", check_async_task_fixes),
        ("常见错误模式", check_error_patterns),
    ]
    
    results = {}
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
        except Exception as e:
            print(f"❌ 检查 {check_name} 失败: {e}")
            results[check_name] = False
    
    # 生成总结报告
    print("\n" + "=" * 60)
    print("📊 快速验证结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} - {check_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个检查通过")
    
    if passed == total:
        print("🎉 所有修复验证通过！")
        print("\n💡 建议：")
        print("1. 重新启动WMZC系统测试修复效果")
        print("2. 观察日志中是否还有DataFrame歧义性错误")
        print("3. 测试预估强平价监控功能是否正常")
        print("4. 检查配置警告是否已消除")
        return True
    else:
        print("⚠️ 部分检查失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
