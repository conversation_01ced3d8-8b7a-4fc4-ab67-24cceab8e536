#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 修复系统闪退问题
解决API认证失败和技术指标计算错误导致的系统崩溃
"""

import os
import json
import shutil
from datetime import datetime

class SystemCrashFixer:
    """系统闪退修复器"""
    
    def __init__(self):
        self.fixes_applied = 0
        
    def run_crash_fix(self):
        """运行闪退修复"""
        print("🔧 系统闪退问题修复")
        print("=" * 60)
        
        # 1. 检查API配置问题
        self.check_api_config_issues()
        
        # 2. 修复技术指标计算错误
        self.fix_technical_indicator_errors()
        
        # 3. 添加异常处理保护
        self.add_exception_protection()
        
        # 4. 创建安全启动模式
        self.create_safe_startup_mode()
        
        # 5. 提供故障排除指南
        self.provide_troubleshooting_guide()
        
        return self.fixes_applied > 0
    
    def check_api_config_issues(self):
        """检查API配置问题"""
        print("\n🔍 检查API配置问题...")
        
        config_files = ['trading_config.json', 'wmzc_config.json']
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 检查API配置
                    if config_file == 'trading_config.json':
                        api_key = config.get('API_KEY', '')
                        api_secret = config.get('API_SECRET', '')
                        passphrase = config.get('PASSPHRASE', '')
                    else:
                        api_key = config.get('okx_api_key', '')
                        api_secret = config.get('okx_secret_key', '')
                        passphrase = config.get('okx_passphrase', '')
                    
                    print(f"\n📄 {config_file}:")
                    
                    if api_key and api_secret and passphrase:
                        # 检查是否是测试API密钥
                        if 'da636867-490f-4e3e-81b2-870841afb860' in api_key:
                            print(f"  ⚠️ 检测到测试API密钥，这会导致认证失败")
                            print(f"  💡 建议：使用真实的OKX API密钥")
                        else:
                            print(f"  ✅ API配置存在: {api_key[:15]}...")
                            print(f"  💡 如果仍然认证失败，请检查:")
                            print(f"    1. API密钥是否正确")
                            print(f"    2. IP白名单是否设置")
                            print(f"    3. API权限是否足够")
                    else:
                        print(f"  ❌ API配置不完整")
                        
                except Exception as e:
                    print(f"  ❌ 读取 {config_file} 失败: {e}")
    
    def fix_technical_indicator_errors(self):
        """修复技术指标计算错误"""
        print("\n🔧 修复技术指标计算错误...")
        
        # 创建技术指标错误修复脚本
        fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 技术指标计算错误修复补丁
修复DataFrame布尔值判断错误
"""

import pandas as pd
import numpy as np

def safe_dataframe_check(df, condition_func):
    """安全的DataFrame条件检查"""
    try:
        if isinstance(df, pd.DataFrame):
            if df.empty:
                return False
            # 使用.iloc[-1]获取最后一个值进行判断
            return condition_func(df.iloc[-1])
        elif isinstance(df, pd.Series):
            if df.empty:
                return False
            return condition_func(df.iloc[-1])
        else:
            return condition_func(df)
    except Exception as e:
        print(f"⚠️ DataFrame条件检查失败: {e}")
        return False

def safe_macd_signal_check(macd_df):
    """安全的MACD信号检查"""
    try:
        if isinstance(macd_df, pd.DataFrame) and not macd_df.empty:
            # 获取最新的MACD值
            latest_macd = macd_df.iloc[-1]
            if isinstance(latest_macd, pd.Series):
                macd_value = latest_macd.get('MACD', 0)
            else:
                macd_value = latest_macd
            
            # 简单的信号判断
            if macd_value > 0:
                return "BUY"
            elif macd_value < 0:
                return "SELL"
            else:
                return "HOLD"
        return "HOLD"
    except Exception as e:
        print(f"⚠️ MACD信号检查失败: {e}")
        return "HOLD"

def safe_kdj_signal_check(kdj_df):
    """安全的KDJ信号检查"""
    try:
        if isinstance(kdj_df, pd.DataFrame) and not kdj_df.empty:
            latest_kdj = kdj_df.iloc[-1]
            k_value = latest_kdj.get('K', 50) if isinstance(latest_kdj, pd.Series) else latest_kdj
            
            if k_value < 20:
                return "BUY"
            elif k_value > 80:
                return "SELL"
            else:
                return "HOLD"
        return "HOLD"
    except Exception as e:
        print(f"⚠️ KDJ信号检查失败: {e}")
        return "HOLD"

def safe_rsi_signal_check(rsi_df):
    """安全的RSI信号检查"""
    try:
        if isinstance(rsi_df, pd.DataFrame) and not rsi_df.empty:
            latest_rsi = rsi_df.iloc[-1]
            rsi_value = latest_rsi.get('RSI', 50) if isinstance(latest_rsi, pd.Series) else latest_rsi
            
            if rsi_value < 30:
                return "BUY"
            elif rsi_value > 70:
                return "SELL"
            else:
                return "HOLD"
        return "HOLD"
    except Exception as e:
        print(f"⚠️ RSI信号检查失败: {e}")
        return "HOLD"

# 导出修复函数
__all__ = [
    'safe_dataframe_check',
    'safe_macd_signal_check', 
    'safe_kdj_signal_check',
    'safe_rsi_signal_check'
]
'''
        
        try:
            with open('technical_indicator_fix.py', 'w', encoding='utf-8') as f:
                f.write(fix_script)
            print("  ✅ 技术指标修复脚本已创建: technical_indicator_fix.py")
            self.fixes_applied += 1
        except Exception as e:
            print(f"  ❌ 创建技术指标修复脚本失败: {e}")
    
    def add_exception_protection(self):
        """添加异常处理保护"""
        print("\n🛡️ 添加异常处理保护...")
        
        # 创建异常保护脚本
        protection_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ 系统异常保护脚本
为关键函数添加异常处理保护
"""

import traceback
import logging
from functools import wraps

def safe_execution(func):
    """安全执行装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logging.error(f"❌ 函数 {func.__name__} 执行失败: {e}")
            logging.error(f"📍 错误详情: {traceback.format_exc()}")
            return None
    return wrapper

def safe_api_call(func):
    """安全API调用装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if "403" in str(e) or "Forbidden" in str(e):
                logging.warning(f"⚠️ API认证失败: {e}")
                logging.info("💡 请检查API密钥配置和权限设置")
            else:
                logging.error(f"❌ API调用失败: {e}")
            return None
    return wrapper

def safe_indicator_calculation(func):
    """安全技术指标计算装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            # 确保返回值不是DataFrame，避免布尔值判断错误
            if hasattr(result, 'iloc'):
                return result.iloc[-1] if not result.empty else 0
            return result
        except Exception as e:
            logging.error(f"❌ 技术指标计算失败: {e}")
            return 0
    return wrapper

# 导出保护装饰器
__all__ = [
    'safe_execution',
    'safe_api_call', 
    'safe_indicator_calculation'
]
'''
        
        try:
            with open('exception_protection.py', 'w', encoding='utf-8') as f:
                f.write(protection_script)
            print("  ✅ 异常保护脚本已创建: exception_protection.py")
            self.fixes_applied += 1
        except Exception as e:
            print(f"  ❌ 创建异常保护脚本失败: {e}")
    
    def create_safe_startup_mode(self):
        """创建安全启动模式"""
        print("\n🔒 创建安全启动模式...")
        
        safe_startup_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔒 WMZC安全启动模式
在安全模式下启动系统，禁用可能导致崩溃的功能
"""

import os
import sys
import json

def create_safe_config():
    """创建安全配置"""
    safe_config = {
        # 基本配置
        "API_KEY": "",
        "API_SECRET": "",
        "PASSPHRASE": "",
        "EXCHANGE": "OKX",
        "SYMBOL": "BTC-USDT-SWAP",
        "TIMEFRAME": "1m",
        "ORDER_USDT_AMOUNT": 10,
        "LEVERAGE": 3,
        "RISK_PERCENT": 1.0,
        
        # 安全模式配置
        "SAFE_MODE": True,
        "DISABLE_TRADING": True,
        "DISABLE_API_CALLS": True,
        "DISABLE_INDICATORS": False,
        "ENABLE_KDJ": False,
        "ENABLE_MACD": False,
        "ENABLE_PINBAR": False,
        "ENABLE_RSI": False,
        
        # 系统配置
        "LOG_LEVEL": "INFO",
        "TEST_MODE": True,
        "ENABLE_TRADING": False,
        
        # 保护标记
        "_CONFIG_PROTECTED": True,
        "_SAFE_MODE_ENABLED": True
    }
    
    return safe_config

def start_safe_mode():
    """启动安全模式"""
    print("🔒 启动WMZC安全模式...")
    
    # 备份当前配置
    if os.path.exists('trading_config.json'):
        import shutil
        shutil.copy2('trading_config.json', 'trading_config.json.backup')
        print("✅ 当前配置已备份")
    
    # 创建安全配置
    safe_config = create_safe_config()
    
    try:
        with open('trading_config.json', 'w', encoding='utf-8') as f:
            json.dump(safe_config, f, indent=2, ensure_ascii=False)
        print("✅ 安全配置已创建")
        
        # 启动系统
        print("🚀 在安全模式下启动WMZC...")
        os.system('python "2019启动ZC.py"')
        
    except Exception as e:
        print(f"❌ 安全模式启动失败: {e}")

def restore_normal_mode():
    """恢复正常模式"""
    print("🔄 恢复正常模式...")
    
    if os.path.exists('trading_config.json.backup'):
        import shutil
        shutil.copy2('trading_config.json.backup', 'trading_config.json')
        print("✅ 配置已恢复")
    else:
        print("❌ 未找到备份配置")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "restore":
        restore_normal_mode()
    else:
        start_safe_mode()
'''
        
        try:
            with open('safe_startup.py', 'w', encoding='utf-8') as f:
                f.write(safe_startup_script)
            print("  ✅ 安全启动脚本已创建: safe_startup.py")
            self.fixes_applied += 1
        except Exception as e:
            print(f"  ❌ 创建安全启动脚本失败: {e}")
    
    def provide_troubleshooting_guide(self):
        """提供故障排除指南"""
        print("\n📖 提供故障排除指南...")
        
        guide = """
🔧 WMZC系统闪退问题故障排除指南

🚨 常见闪退原因:

1. 📡 API认证失败 (403 Forbidden)
   原因: API密钥错误、IP白名单未设置、权限不足
   解决: 检查API密钥配置，确保IP在白名单中

2. 📊 技术指标计算错误
   原因: DataFrame布尔值判断异常
   解决: 使用修复后的技术指标计算函数

3. 💾 内存不足或资源耗尽
   原因: 长时间运行导致内存泄漏
   解决: 定期重启系统，优化内存使用

4. 🔌 网络连接问题
   原因: 网络不稳定导致API调用失败
   解决: 检查网络连接，使用稳定的网络环境

🔧 解决步骤:

1. 🔒 使用安全模式启动
   python safe_startup.py
   
2. 📋 检查系统日志
   查看控制台输出，找到具体错误信息
   
3. 🔧 修复API配置
   - 确保使用真实的API密钥
   - 检查IP白名单设置
   - 验证API权限
   
4. 🛡️ 应用修复补丁
   - 使用技术指标修复脚本
   - 启用异常保护机制
   
5. 🔄 逐步启用功能
   - 先在安全模式下测试
   - 逐步启用各项功能
   - 确认系统稳定后恢复正常模式

💡 预防措施:

1. 定期备份配置文件
2. 使用测试模式验证新配置
3. 监控系统资源使用情况
4. 保持网络连接稳定
5. 及时更新系统补丁

🆘 紧急恢复:

如果系统持续闪退:
1. python safe_startup.py restore  # 恢复备份配置
2. 删除可能损坏的配置文件
3. 重新配置API密钥
4. 在安全模式下重新启动
"""
        
        try:
            with open('CRASH_TROUBLESHOOTING_GUIDE.md', 'w', encoding='utf-8') as f:
                f.write(guide)
            print("  ✅ 故障排除指南已创建: CRASH_TROUBLESHOOTING_GUIDE.md")
        except Exception as e:
            print(f"  ❌ 创建故障排除指南失败: {e}")

def main():
    """主函数"""
    print("🔧 WMZC系统闪退问题修复工具")
    print("=" * 60)
    
    fixer = SystemCrashFixer()
    
    try:
        success = fixer.run_crash_fix()
        
        print("\n" + "=" * 60)
        if success:
            print(f"🎉 闪退问题修复完成！共应用了 {fixer.fixes_applied} 个修复")
            print("\n💡 修复内容:")
            print("  1. ✅ 检查了API配置问题")
            print("  2. ✅ 修复了技术指标计算错误")
            print("  3. ✅ 添加了异常处理保护")
            print("  4. ✅ 创建了安全启动模式")
            print("  5. ✅ 提供了故障排除指南")
            
            print("\n🚀 建议的解决步骤:")
            print("  1. 使用安全模式启动: python safe_startup.py")
            print("  2. 检查API密钥配置是否正确")
            print("  3. 确认网络连接稳定")
            print("  4. 在安全模式下测试系统稳定性")
            print("  5. 逐步启用各项功能")
            
        else:
            print("❌ 闪退问题修复失败")
            print("💡 请查看故障排除指南手动修复")
        
        return success
        
    except Exception as e:
        print(f"❌ 修复过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
