#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔒 WMZC安全启动模式
在安全模式下启动系统，禁用可能导致崩溃的功能
"""

import os
import sys
import json

def create_safe_config():
    """创建安全配置"""
    safe_config = {
        # 基本配置
        "API_KEY": "",
        "API_SECRET": "",
        "PASSPHRASE": "",
        "EXCHANGE": "OKX",
        "SYMBOL": "BTC-USDT-SWAP",
        "TIMEFRAME": "1m",
        "ORDER_USDT_AMOUNT": 10,
        "LEVERAGE": 3,
        "RISK_PERCENT": 1.0,
        
        # 安全模式配置
        "SAFE_MODE": True,
        "DISABLE_TRADING": True,
        "DISABLE_API_CALLS": True,
        "DISABLE_INDICATORS": False,
        "ENABLE_KDJ": False,
        "ENABLE_MACD": False,
        "ENABLE_PINBAR": False,
        "ENABLE_RSI": False,
        
        # 系统配置
        "LOG_LEVEL": "INFO",
        "TEST_MODE": True,
        "ENABLE_TRADING": False,
        
        # 保护标记
        "_CONFIG_PROTECTED": True,
        "_SAFE_MODE_ENABLED": True
    }
    
    return safe_config

def start_safe_mode():
    """启动安全模式"""
    print("🔒 启动WMZC安全模式...")
    
    # 备份当前配置
    if os.path.exists('trading_config.json'):
        import shutil
        shutil.copy2('trading_config.json', 'trading_config.json.backup')
        print("✅ 当前配置已备份")
    
    # 创建安全配置
    safe_config = create_safe_config()
    
    try:
        with open('trading_config.json', 'w', encoding='utf-8') as f:
            json.dump(safe_config, f, indent=2, ensure_ascii=False)
        print("✅ 安全配置已创建")
        
        # 启动系统
        print("🚀 在安全模式下启动WMZC...")
        os.system('python "2019启动ZC.py"')
        
    except Exception as e:
        print(f"❌ 安全模式启动失败: {e}")

def restore_normal_mode():
    """恢复正常模式"""
    print("🔄 恢复正常模式...")
    
    if os.path.exists('trading_config.json.backup'):
        import shutil
        shutil.copy2('trading_config.json.backup', 'trading_config.json')
        print("✅ 配置已恢复")
    else:
        print("❌ 未找到备份配置")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "restore":
        restore_normal_mode()
    else:
        start_safe_mode()
