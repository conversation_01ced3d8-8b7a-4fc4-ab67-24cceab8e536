#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的语法检查脚本
"""

import ast
import sys

def check_syntax(filename):
    """检查Python文件的语法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content, filename=filename)
        print(f"✅ {filename} 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   文件: {e.filename}")
        print(f"   行号: {e.lineno}")
        print(f"   位置: {e.offset}")
        print(f"   文本: {e.text}")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    filename = "WMZC.py"
    success = check_syntax(filename)
    sys.exit(0 if success else 1)
