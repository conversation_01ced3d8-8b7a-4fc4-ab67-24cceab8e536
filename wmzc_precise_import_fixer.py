#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC精确导入修复器
恢复被错误注释的必要导入语句
"""

import re
import shutil
from datetime import datetime

class PreciseImportFixer:
    """精确导入修复器"""
    
    def __init__(self, file_path: str = "WMZC.py"):
        self.file_path = file_path
        self.backup_path = f"WMZC_import_fix_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        self.fixes_applied = 0
        self.code_lines = []
        
    def create_backup(self) -> bool:
        """创建备份"""
        try:
            shutil.copy2(self.file_path, self.backup_path)
            print(f"✅ 备份已创建: {self.backup_path}")
            return True
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    
    def load_code(self) -> bool:
        """加载代码"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.code_lines = f.readlines()
            print(f"✅ 成功加载 {len(self.code_lines)} 行代码")
            return True
        except Exception as e:
            print(f"❌ 加载代码失败: {e}")
            return False
    
    def fix_function_level_imports(self):
        """修复函数级别的导入"""
        print("🔧 修复函数级别的必要导入...")
        
        fixes = 0
        
        # 需要恢复的函数级导入模式
        function_imports = [
            'import hashlib',
            'import time', 
            'import json',
            'import sys',
            'import gc',
            'import psutil',
            'import threading',
            'import asyncio',
            'import re',
            'import os',
            'import pickle',
            'import sqlite3',
            'import requests',
            'import websockets',
            'import ssl',
            'import webbrowser',
            'import csv',
            'from tkinter import filedialog',
            'from bs4 import BeautifulSoup',
            'from cryptography.fernet import Fernet',
            'from sklearn.preprocessing import MinMaxScaler',
            'from datetime import datetime'
        ]
        
        for i, line in enumerate(self.code_lines):
            # 检查是否是被注释的导入
            if line.strip().startswith('# 🔧 智能修复: 重复导入 -'):
                # 提取原始导入语句
                import_match = re.search(r'# 🔧 智能修复: 重复导入 -\s*(.*)', line)
                if import_match:
                    original_import = import_match.group(1).strip()
                    
                    # 检查是否是函数级别的必要导入
                    for needed_import in function_imports:
                        if needed_import in original_import:
                            # 检查上下文，确定是否在函数内部
                            if self._is_inside_function_or_try_block(i):
                                # 恢复导入
                                indent = self._get_line_indent(line)
                                self.code_lines[i] = f"{indent}{original_import}\n"
                                fixes += 1
                                print(f"  ✅ 恢复函数级导入: {original_import}")
                                break
        
        self.fixes_applied += fixes
        print(f"✅ 恢复了 {fixes} 个函数级导入")
    
    def _is_inside_function_or_try_block(self, line_index: int) -> bool:
        """检查是否在函数或try块内部"""
        # 向上查找，看是否在函数定义内
        for i in range(line_index - 1, max(0, line_index - 50), -1):
            line = self.code_lines[i].strip()
            
            # 如果遇到函数定义
            if re.match(r'^\s*def\s+\w+', self.code_lines[i]):
                return True
            
            # 如果遇到try块
            if re.match(r'^\s*try:', self.code_lines[i]):
                return True
            
            # 如果遇到类定义或其他顶级定义，停止查找
            if re.match(r'^(class|def)\s+\w+', self.code_lines[i]):
                break
        
        return False
    
    def _get_line_indent(self, line: str) -> str:
        """获取行的缩进"""
        match = re.match(r'^(\s*)', line)
        return match.group(1) if match else ''
    
    def fix_try_except_blocks(self):
        """修复try-except块中的导入"""
        print("🔧 修复try-except块中的导入...")
        
        fixes = 0
        i = 0
        
        while i < len(self.code_lines):
            line = self.code_lines[i]
            
            # 检查try块
            if re.match(r'^\s*try:', line):
                # 查找对应的except块
                j = i + 1
                try_indent = len(line) - len(line.lstrip())
                
                while j < len(self.code_lines):
                    next_line = self.code_lines[j]
                    next_indent = len(next_line) - len(next_line.lstrip())
                    
                    # 如果找到except块
                    if re.match(r'^\s*except', next_line) and next_indent == try_indent:
                        # 检查try块中是否有被注释的导入
                        for k in range(i + 1, j):
                            import_line = self.code_lines[k]
                            if '# 🔧 智能修复: 重复导入 -' in import_line:
                                # 恢复导入
                                import_match = re.search(r'# 🔧 智能修复: 重复导入 -\s*(.*)', import_line)
                                if import_match:
                                    original_import = import_match.group(1).strip()
                                    indent = self._get_line_indent(import_line)
                                    self.code_lines[k] = f"{indent}{original_import}\n"
                                    fixes += 1
                                    print(f"  ✅ 恢复try块导入: {original_import}")
                        break
                    
                    # 如果遇到同级别的其他语句，停止查找
                    if next_indent <= try_indent and next_line.strip():
                        break
                    
                    j += 1
            
            i += 1
        
        self.fixes_applied += fixes
        print(f"✅ 修复了 {fixes} 个try块导入")
    
    def save_fixed_code(self) -> bool:
        """保存修复后的代码"""
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                f.writelines(self.code_lines)
            print(f"✅ 修复后的代码已保存")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def run_precise_fix(self) -> bool:
        """运行精确修复"""
        print("🚀 启动WMZC精确导入修复...")
        print("=" * 80)
        
        # 1. 创建备份
        if not self.create_backup():
            return False
        
        # 2. 加载代码
        if not self.load_code():
            return False
        
        # 3. 执行修复
        self.fix_function_level_imports()
        self.fix_try_except_blocks()
        
        # 4. 保存修复后的代码
        if not self.save_fixed_code():
            return False
        
        # 5. 生成修复报告
        self.generate_fix_report()
        
        return True
    
    def generate_fix_report(self):
        """生成修复报告"""
        print("\n" + "=" * 80)
        print("📊 WMZC精确导入修复报告")
        print("=" * 80)
        
        print(f"🔧 总修复数: {self.fixes_applied}")
        print(f"📁 备份文件: {self.backup_path}")
        
        if self.fixes_applied > 0:
            print("\n✅ 修复完成！主要修复内容:")
            print("  • 恢复函数级别的必要导入")
            print("  • 修复try-except块中的导入")
            print("  • 保持正确的代码缩进")
            
            print(f"\n💡 建议:")
            print("  • 重新运行语法检查确保无错误")
            print("  • 运行功能测试验证修复效果")
        else:
            print("\n🎉 未发现需要修复的导入问题！")

def main():
    """主函数"""
    print("🔧 WMZC精确导入修复器")
    print("恢复被错误注释的必要导入语句")
    print("=" * 80)
    
    fixer = PreciseImportFixer()
    
    try:
        success = fixer.run_precise_fix()
        
        if success:
            print("\n✅ 精确导入修复完成！")
            return True
        else:
            print("\n❌ 修复过程中出现错误")
            return False
            
    except Exception as e:
        print(f"\n💥 修复异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
