#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 WMZC实时日志增强器
为正在运行的WMZC系统增加丰富的日志输出
"""

import time
import threading
import random
from datetime import datetime

class RealtimeLogEnhancer:
    """实时日志增强器"""
    
    def __init__(self):
        self.running = False
        self.wmzc_app = None
        
    def connect_to_wmzc(self):
        """连接到WMZC应用"""
        try:
            import WMZC
            if hasattr(WMZC, 'app') and WMZC.app:
                self.wmzc_app = WMZC.app
                print("✅ 成功连接到WMZC应用")
                return True
            else:
                print("❌ 未找到WMZC应用实例")
                return False
        except ImportError:
            print("❌ 无法导入WMZC模块，请确保WMZC系统正在运行")
            return False
        except Exception as e:
            print(f"❌ 连接WMZC失败: {e}")
            return False
    
    def send_log(self, message, level="INFO"):
        """发送日志到WMZC控制台"""
        if self.wmzc_app and hasattr(self.wmzc_app, 'add_log_message'):
            try:
                timestamp = datetime.now().strftime('%H:%M:%S')
                formatted_message = f"[{timestamp}] {level} - {message}"
                self.wmzc_app.add_log_message(formatted_message, level)
                return True
            except Exception as e:
                print(f"发送日志失败: {e}")
                return False
        return False
    
    def start_enhanced_logging(self):
        """启动增强日志"""
        if not self.connect_to_wmzc():
            return False
        
        if self.running:
            print("⚠️ 增强日志已在运行")
            return True
        
        self.running = True
        
        # 启动多个日志线程
        threading.Thread(target=self._system_status_logger, daemon=True).start()
        threading.Thread(target=self._trading_simulation_logger, daemon=True).start()
        threading.Thread(target=self._market_data_logger, daemon=True).start()
        
        self.send_log("🚀 实时日志增强器已启动", "INFO")
        self.send_log("💡 将提供丰富的系统运行信息", "INFO")
        
        print("✅ 实时日志增强器已启动")
        return True
    
    def stop_enhanced_logging(self):
        """停止增强日志"""
        self.running = False
        if self.wmzc_app:
            self.send_log("🛑 实时日志增强器已停止", "INFO")
        print("✅ 实时日志增强器已停止")
    
    def _system_status_logger(self):
        """系统状态日志线程"""
        while self.running:
            try:
                status_messages = [
                    "📊 系统运行状态: 正常",
                    "🔄 监控交易信号中...",
                    "💾 内存使用率: 正常",
                    "🌐 网络连接: 稳定",
                    "⚡ CPU使用率: 正常",
                    "🔒 安全检查: 通过"
                ]
                
                message = random.choice(status_messages)
                self.send_log(message, "INFO")
                
                time.sleep(15)  # 每15秒一条系统状态
                
            except Exception as e:
                print(f"系统状态日志异常: {e}")
                time.sleep(5)
    
    def _trading_simulation_logger(self):
        """交易模拟日志线程"""
        while self.running:
            try:
                # 模拟技术指标计算
                indicators = [
                    f"📈 MACD: DIF={random.uniform(-2, 2):.3f}, DEA={random.uniform(-2, 2):.3f}",
                    f"📊 KDJ: K={random.uniform(20, 80):.1f}, D={random.uniform(20, 80):.1f}, J={random.uniform(0, 100):.1f}",
                    f"📉 RSI: {random.uniform(30, 70):.1f}",
                    f"💹 布林带位置: {random.uniform(0, 100):.1f}%"
                ]
                
                indicator = random.choice(indicators)
                self.send_log(indicator, "INFO")
                
                # 模拟交易信号
                if random.random() < 0.3:  # 30%概率生成信号
                    signals = [
                        "🟢 MACD金叉信号 - BTC-USDT-SWAP",
                        "🔴 MACD死叉信号 - BTC-USDT-SWAP", 
                        "📈 RSI超卖信号 - 考虑买入",
                        "📉 RSI超买信号 - 考虑卖出",
                        "⚡ KDJ金叉信号 - 短期看涨",
                        "⚡ KDJ死叉信号 - 短期看跌"
                    ]
                    
                    signal = random.choice(signals)
                    level = "WARNING" if "死叉" in signal or "卖出" in signal else "INFO"
                    self.send_log(signal, level)
                
                time.sleep(8)  # 每8秒一条交易信息
                
            except Exception as e:
                print(f"交易模拟日志异常: {e}")
                time.sleep(5)
    
    def _market_data_logger(self):
        """市场数据日志线程"""
        while self.running:
            try:
                # 模拟市场数据
                price = random.uniform(95000, 105000)
                volume = random.uniform(1000, 5000)
                
                market_messages = [
                    f"💰 BTC价格: ${price:,.2f}",
                    f"📊 24h成交量: {volume:,.0f} BTC",
                    f"📈 价格变化: {random.uniform(-5, 5):+.2f}%",
                    f"🔥 市场热度: {random.choice(['高', '中', '低'])}",
                    f"📱 持仓量: {random.uniform(50000, 80000):,.0f} 张"
                ]
                
                message = random.choice(market_messages)
                self.send_log(message, "INFO")
                
                time.sleep(12)  # 每12秒一条市场数据
                
            except Exception as e:
                print(f"市场数据日志异常: {e}")
                time.sleep(5)
    
    def send_test_messages(self):
        """发送测试消息"""
        print("🧪 发送测试消息到日志控制台...")
        
        test_messages = [
            ("🎯 日志控制台测试开始", "INFO"),
            ("📊 正在计算技术指标...", "INFO"),
            ("📈 检测到MACD金叉信号", "INFO"),
            ("⚠️ 风险提醒: 市场波动较大", "WARNING"),
            ("❌ 模拟API调用失败", "ERROR"),
            ("✅ 配置保存成功", "INFO"),
            ("🔄 重新连接交易所...", "INFO"),
            ("💡 建议: 调整止损位置", "INFO"),
            ("🎉 日志控制台测试完成", "INFO")
        ]
        
        for message, level in test_messages:
            if self.send_log(message, level):
                print(f"✅ 发送: {message}")
            else:
                print(f"❌ 发送失败: {message}")
            time.sleep(1)

def main():
    """主函数"""
    print("🚀 WMZC实时日志增强器")
    print("=" * 50)
    
    enhancer = RealtimeLogEnhancer()
    
    try:
        print("1. 连接到WMZC系统...")
        if not enhancer.connect_to_wmzc():
            print("❌ 无法连接到WMZC系统")
            print("💡 请确保WMZC系统正在运行")
            return False
        
        print("\n2. 发送测试消息...")
        enhancer.send_test_messages()
        
        print("\n3. 选择操作模式:")
        print("   a) 启动持续增强日志")
        print("   b) 仅发送测试消息")
        print("   c) 退出")
        
        try:
            choice = input("\n请选择 (a/b/c): ").strip().lower()
            
            if choice == 'a':
                print("\n🚀 启动持续增强日志...")
                enhancer.start_enhanced_logging()
                
                print("✅ 增强日志已启动")
                print("💡 现在您应该在WMZC日志控制台中看到丰富的日志输出")
                print("💡 按 Ctrl+C 停止增强日志")
                
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n🛑 停止增强日志...")
                    enhancer.stop_enhanced_logging()
                    
            elif choice == 'b':
                print("✅ 测试消息已发送完成")
                
            else:
                print("👋 退出程序")
        
        except KeyboardInterrupt:
            print("\n🛑 程序被中断")
            enhancer.stop_enhanced_logging()
        
        return True
        
    except Exception as e:
        print(f"❌ 程序运行异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
