#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate.io API优化功能测试脚本
测试智能订单队列、数据缓存、连接池等优化功能
"""

import asyncio
import time
import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def log_test(message, level="INFO"):
    """测试日志函数"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

class GateIOOptimizationTester:
    """Gate.io API优化功能测试器"""
    
    def __init__(self):
        self.test_results = []
        self.optimizer = None
        
    async def run_all_tests(self):
        """运行所有测试"""
        log_test("=" * 80)
        log_test("🚀 Gate.io API优化功能测试开始")
        log_test("=" * 80)
        
        try:
            # 测试1: 导入优化模块
            await self.test_import_optimization_modules()
            
            # 测试2: 初始化优化器
            await self.test_optimizer_initialization()
            
            # 测试3: 测试智能订单队列
            await self.test_smart_order_queue()
            
            # 测试4: 测试数据缓存
            await self.test_data_caching()
            
            # 测试5: 测试连接池
            await self.test_connection_pool()
            
            # 测试6: 测试安全性增强
            await self.test_security_enhancement()
            
            # 测试7: 测试性能监控
            await self.test_performance_monitoring()
            
            # 生成测试报告
            self.generate_test_report()
            
        except Exception as e:
            log_test(f"❌ 测试执行失败: {e}", "ERROR")
            import traceback
            traceback.print_exc()
    
    async def test_import_optimization_modules(self):
        """测试1: 导入优化模块"""
        log_test("📋 测试1: 导入优化模块", "INFO")
        log_test("-" * 50)
        
        try:
            # 尝试导入WMZC主模块
            log_test("📦 导入WMZC主模块...", "INFO")
            import WMZC
            
            # 检查优化类是否存在
            required_classes = [
                'SmartOrderQueue',
                'LayeredDataCache', 
                'HTTPConnectionPool',
                'WebSocketLoadBalancer',
                'SecurityEnhancer',
                'GateIOAPIOptimizer'
            ]
            
            found_classes = []
            for class_name in required_classes:
                if hasattr(WMZC, class_name):
                    found_classes.append(class_name)
                    log_test(f"✅ 找到类: {class_name}", "INFO")
                else:
                    log_test(f"❌ 缺少类: {class_name}", "ERROR")
            
            success_rate = len(found_classes) / len(required_classes)
            log_test(f"📊 模块导入成功率: {success_rate:.1%} ({len(found_classes)}/{len(required_classes)})", "INFO")
            
            self.test_results.append({
                'test': '导入优化模块',
                'success': success_rate >= 0.8,
                'details': f"成功导入 {len(found_classes)}/{len(required_classes)} 个类"
            })
            
        except Exception as e:
            log_test(f"❌ 模块导入失败: {e}", "ERROR")
            self.test_results.append({
                'test': '导入优化模块',
                'success': False,
                'details': str(e)
            })
    
    async def test_optimizer_initialization(self):
        """测试2: 初始化优化器"""
        log_test("📋 测试2: 初始化优化器", "INFO")
        log_test("-" * 50)
        
        try:
            import WMZC
            
            # 创建优化器实例
            log_test("🔧 创建GateIOAPIOptimizer实例...", "INFO")
            self.optimizer = WMZC.GateIOAPIOptimizer()
            
            # 测试初始化
            log_test("⚡ 初始化优化器...", "INFO")
            init_success = await self.optimizer.initialize()
            
            if init_success:
                log_test("✅ 优化器初始化成功", "INFO")
                
                # 检查组件状态
                components = {
                    'order_queue': self.optimizer.order_queue,
                    'data_cache': self.optimizer.data_cache,
                    'connection_pool': self.optimizer.connection_pool,
                    'websocket_balancer': self.optimizer.websocket_balancer,
                    'security_enhancer': self.optimizer.security_enhancer
                }
                
                for name, component in components.items():
                    if component:
                        log_test(f"✅ {name} 组件就绪", "INFO")
                    else:
                        log_test(f"❌ {name} 组件未就绪", "ERROR")
                
                self.test_results.append({
                    'test': '初始化优化器',
                    'success': True,
                    'details': '所有组件初始化成功'
                })
            else:
                log_test("❌ 优化器初始化失败", "ERROR")
                self.test_results.append({
                    'test': '初始化优化器',
                    'success': False,
                    'details': '初始化返回False'
                })
                
        except Exception as e:
            log_test(f"❌ 优化器初始化异常: {e}", "ERROR")
            self.test_results.append({
                'test': '初始化优化器',
                'success': False,
                'details': str(e)
            })
    
    async def test_smart_order_queue(self):
        """测试3: 智能订单队列"""
        log_test("📋 测试3: 智能订单队列", "INFO")
        log_test("-" * 50)
        
        try:
            if not self.optimizer:
                log_test("⚠️ 优化器未初始化，跳过测试", "WARNING")
                return
            
            order_queue = self.optimizer.order_queue
            
            # 测试添加订单
            log_test("📦 测试批量订单处理...", "INFO")
            
            import WMZC
            test_orders = []
            for i in range(5):
                order = WMZC.OrderRequest(
                    symbol="BTC_USDT",
                    side="buy",
                    amount=10.0,
                    order_type="market"
                )
                test_orders.append(order)
            
            # 并发添加订单
            start_time = time.time()
            tasks = [order_queue.add_order(order) for order in test_orders]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # 检查结果
            successful_orders = sum(1 for result in results if isinstance(result, dict) and result.get('success'))
            processing_time = end_time - start_time
            
            log_test(f"📊 订单处理结果: {successful_orders}/{len(test_orders)} 成功", "INFO")
            log_test(f"⏱️ 处理时间: {processing_time:.3f}秒", "INFO")
            
            # 获取队列统计
            queue_stats = order_queue.get_stats()
            log_test(f"📈 队列统计: {json.dumps(queue_stats, indent=2)}", "INFO")
            
            self.test_results.append({
                'test': '智能订单队列',
                'success': successful_orders >= len(test_orders) * 0.8,
                'details': f"成功处理 {successful_orders}/{len(test_orders)} 个订单"
            })
            
        except Exception as e:
            log_test(f"❌ 订单队列测试失败: {e}", "ERROR")
            self.test_results.append({
                'test': '智能订单队列',
                'success': False,
                'details': str(e)
            })
    
    async def test_data_caching(self):
        """测试4: 数据缓存"""
        log_test("📋 测试4: 数据缓存", "INFO")
        log_test("-" * 50)
        
        try:
            if not self.optimizer:
                log_test("⚠️ 优化器未初始化，跳过测试", "WARNING")
                return
            
            data_cache = self.optimizer.data_cache
            
            # 测试缓存设置和获取
            test_data = {
                'ticker': {'symbol': 'BTC_USDT', 'price': 50000, 'volume': 1000},
                'kline': [['1640995200', '50000', '51000', '49000', '50500', '100']],
                'balance': {'USDT': 1000.0, 'BTC': 0.02}
            }
            
            # 设置缓存
            for cache_type, data in test_data.items():
                cache_key = f"test_{cache_type}"
                success = await data_cache.set(cache_type, cache_key, data)
                if success:
                    log_test(f"✅ 缓存设置成功: {cache_type}", "INFO")
                else:
                    log_test(f"❌ 缓存设置失败: {cache_type}", "ERROR")
            
            # 获取缓存
            cache_hits = 0
            for cache_type, expected_data in test_data.items():
                cache_key = f"test_{cache_type}"
                cached_data = await data_cache.get(cache_type, cache_key)
                if cached_data == expected_data:
                    cache_hits += 1
                    log_test(f"✅ 缓存命中: {cache_type}", "INFO")
                else:
                    log_test(f"❌ 缓存未命中: {cache_type}", "ERROR")
            
            # 获取缓存统计
            cache_stats = data_cache.get_cache_stats()
            log_test(f"📈 缓存统计: {json.dumps(cache_stats, indent=2)}", "INFO")
            
            cache_success_rate = cache_hits / len(test_data)
            self.test_results.append({
                'test': '数据缓存',
                'success': cache_success_rate >= 0.8,
                'details': f"缓存命中率: {cache_success_rate:.1%}"
            })
            
        except Exception as e:
            log_test(f"❌ 数据缓存测试失败: {e}", "ERROR")
            self.test_results.append({
                'test': '数据缓存',
                'success': False,
                'details': str(e)
            })
    
    async def test_connection_pool(self):
        """测试5: 连接池"""
        log_test("📋 测试5: 连接池", "INFO")
        log_test("-" * 50)
        
        try:
            if not self.optimizer:
                log_test("⚠️ 优化器未初始化，跳过测试", "WARNING")
                return
            
            connection_pool = self.optimizer.connection_pool
            
            # 测试HTTP请求
            log_test("🌐 测试HTTP连接池请求...", "INFO")
            
            # 模拟多个并发请求
            test_urls = [
                "https://httpbin.org/delay/1",
                "https://httpbin.org/json",
                "https://httpbin.org/headers"
            ]
            
            start_time = time.time()
            tasks = [connection_pool.request('GET', url) for url in test_urls]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # 分析结果
            successful_requests = sum(1 for result in results if isinstance(result, dict) and result.get('success'))
            total_time = end_time - start_time
            
            log_test(f"📊 请求结果: {successful_requests}/{len(test_urls)} 成功", "INFO")
            log_test(f"⏱️ 总耗时: {total_time:.3f}秒", "INFO")
            
            # 获取连接池统计
            pool_stats = connection_pool.get_stats()
            log_test(f"📈 连接池统计: {json.dumps(pool_stats, indent=2)}", "INFO")
            
            self.test_results.append({
                'test': '连接池',
                'success': successful_requests >= len(test_urls) * 0.6,  # 允许部分失败（网络问题）
                'details': f"成功请求: {successful_requests}/{len(test_urls)}"
            })
            
        except Exception as e:
            log_test(f"❌ 连接池测试失败: {e}", "ERROR")
            self.test_results.append({
                'test': '连接池',
                'success': False,
                'details': str(e)
            })
    
    async def test_security_enhancement(self):
        """测试6: 安全性增强"""
        log_test("📋 测试6: 安全性增强", "INFO")
        log_test("-" * 50)
        
        try:
            if not self.optimizer:
                log_test("⚠️ 优化器未初始化，跳过测试", "WARNING")
                return
            
            security_enhancer = self.optimizer.security_enhancer
            
            # 测试时间戳验证
            log_test("🛡️ 测试时间戳验证...", "INFO")
            
            current_time = time.time()
            valid_timestamp = current_time - 10  # 10秒前，应该有效
            invalid_timestamp = current_time - 60  # 60秒前，应该无效
            
            valid_result = security_enhancer.validate_timestamp(valid_timestamp)
            invalid_result = security_enhancer.validate_timestamp(invalid_timestamp)
            
            log_test(f"✅ 有效时间戳验证: {'通过' if valid_result else '失败'}", "INFO")
            log_test(f"✅ 无效时间戳验证: {'通过' if not invalid_result else '失败'}", "INFO")
            
            # 测试请求记录
            log_test("📝 测试请求记录...", "INFO")
            
            test_requests = [
                {'success': True, 'response_time': 0.5, 'endpoint': '/test1'},
                {'success': False, 'response_time': 2.0, 'endpoint': '/test2', 'error': 'timeout'},
                {'success': True, 'response_time': 0.3, 'endpoint': '/test3'}
            ]
            
            for req in test_requests:
                security_enhancer.record_request(req)
            
            # 获取安全统计
            security_stats = security_enhancer.get_security_stats()
            log_test(f"📈 安全统计: {json.dumps(security_stats, indent=2)}", "INFO")
            
            timestamp_test_passed = valid_result and not invalid_result
            self.test_results.append({
                'test': '安全性增强',
                'success': timestamp_test_passed,
                'details': f"时间戳验证: {'通过' if timestamp_test_passed else '失败'}"
            })
            
        except Exception as e:
            log_test(f"❌ 安全性增强测试失败: {e}", "ERROR")
            self.test_results.append({
                'test': '安全性增强',
                'success': False,
                'details': str(e)
            })
    
    async def test_performance_monitoring(self):
        """测试7: 性能监控"""
        log_test("📋 测试7: 性能监控", "INFO")
        log_test("-" * 50)
        
        try:
            if not self.optimizer:
                log_test("⚠️ 优化器未初始化，跳过测试", "WARNING")
                return
            
            # 获取优化统计信息
            log_test("📊 获取优化统计信息...", "INFO")
            
            optimization_stats = self.optimizer.get_optimization_stats()
            
            # 显示关键指标
            performance_metrics = optimization_stats.get('performance_metrics', {})
            log_test(f"📈 性能指标:", "INFO")
            log_test(f"  - 总优化请求数: {performance_metrics.get('total_optimized_requests', 0)}", "INFO")
            log_test(f"  - API调用节省数: {performance_metrics.get('api_calls_saved', 0)}", "INFO")
            log_test(f"  - 缓存命中率: {performance_metrics.get('cache_hit_rate', 0):.2%}", "INFO")
            log_test(f"  - 平均响应时间: {performance_metrics.get('avg_response_time', 0):.3f}秒", "INFO")
            
            # 检查各组件统计
            components = ['order_queue_stats', 'cache_stats', 'connection_stats', 'security_stats']
            available_components = sum(1 for comp in components if comp in optimization_stats)
            
            log_test(f"📊 可用组件统计: {available_components}/{len(components)}", "INFO")
            
            self.test_results.append({
                'test': '性能监控',
                'success': available_components >= len(components) * 0.8,
                'details': f"可用统计组件: {available_components}/{len(components)}"
            })
            
        except Exception as e:
            log_test(f"❌ 性能监控测试失败: {e}", "ERROR")
            self.test_results.append({
                'test': '性能监控',
                'success': False,
                'details': str(e)
            })
    
    def generate_test_report(self):
        """生成测试报告"""
        log_test("=" * 80)
        log_test("📋 Gate.io API优化功能测试报告")
        log_test("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        
        log_test(f"📊 测试总结:")
        log_test(f"  - 总测试数: {total_tests}")
        log_test(f"  - 通过测试: {passed_tests}")
        log_test(f"  - 失败测试: {total_tests - passed_tests}")
        log_test(f"  - 成功率: {success_rate:.1%}")
        
        log_test("\n📋 详细结果:")
        for i, result in enumerate(self.test_results, 1):
            status = "✅ 通过" if result['success'] else "❌ 失败"
            log_test(f"  {i}. {result['test']}: {status}")
            log_test(f"     详情: {result['details']}")
        
        # 保存测试报告到文件
        report_data = {
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'test_results': self.test_results
        }
        
        try:
            with open('gate_io_optimization_test_report.json', 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            log_test("📄 测试报告已保存到: gate_io_optimization_test_report.json")
        except Exception as e:
            log_test(f"⚠️ 测试报告保存失败: {e}", "WARNING")
        
        if success_rate >= 0.8:
            log_test("🎉 Gate.io API优化功能测试整体通过！", "INFO")
        else:
            log_test("⚠️ Gate.io API优化功能测试存在问题，需要进一步检查", "WARNING")

async def main():
    """主函数"""
    tester = GateIOOptimizationTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
