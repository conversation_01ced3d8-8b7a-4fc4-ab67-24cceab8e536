#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API连接问题修复脚本
解决"客户端不支持余额获取"和"无法获取真实余额数据"的问题
"""

import json
import os

def check_api_configuration():
    """检查API配置"""
    print("🔍 检查API配置...")
    
    config_files = [
        'trading_config.json',
        'wmzc_config.json'
    ]
    
    api_configs = {}
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查OKX配置
                okx_keys = ['OKX_API_KEY', 'OKX_SECRET_KEY', 'OKX_PASSPHRASE']
                okx_configured = all(config.get(key, '').strip() for key in okx_keys)
                
                # 检查Gate.io配置
                gate_keys = ['GATE_API_KEY', 'GATE_SECRET_KEY']
                gate_configured = all(config.get(key, '').strip() for key in gate_keys)
                
                api_configs[config_file] = {
                    'okx_configured': okx_configured,
                    'gate_configured': gate_configured,
                    'exchange': config.get('EXCHANGE', config.get('exchange_selection', 'OKX'))
                }
                
                print(f"  📄 {config_file}:")
                print(f"    OKX配置: {'✅' if okx_configured else '❌'}")
                print(f"    Gate.io配置: {'✅' if gate_configured else '❌'}")
                print(f"    当前交易所: {api_configs[config_file]['exchange']}")
                
            except Exception as e:
                print(f"  ❌ 读取{config_file}失败: {e}")
    
    return api_configs

def fix_api_client_methods():
    """修复API客户端方法问题"""
    print("\n🔧 修复API客户端方法...")
    
    # 这个问题主要是代码逻辑问题，需要在WMZC.py中修复
    # 这里提供修复建议
    
    suggestions = [
        "1. 确保API客户端正确初始化",
        "2. 添加get_balance_sync方法的兼容性检查",
        "3. 改进错误处理和回退机制",
        "4. 添加API连接状态检查"
    ]
    
    print("💡 修复建议:")
    for suggestion in suggestions:
        print(f"  {suggestion}")
    
    return True

def create_api_test_script():
    """创建API测试脚本"""
    print("\n📝 创建API测试脚本...")
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API连接测试脚本
"""

import sys
import os
sys.path.append('.')

def test_api_connection():
    """测试API连接"""
    try:
        import WMZC
        
        print("🔍 测试API连接...")
        
        # 创建统一交易所管理器
        if hasattr(WMZC, 'UnifiedExchangeManager'):
            manager = WMZC.UnifiedExchangeManager()
            
            # 测试余额获取
            print("  测试余额获取...")
            try:
                balance = manager.get_balance()
                if balance:
                    print(f"    ✅ 余额获取成功: {balance}")
                else:
                    print("    ⚠️ 余额为空")
            except Exception as e:
                print(f"    ❌ 余额获取失败: {e}")
            
            # 测试价格获取
            print("  测试价格获取...")
            try:
                price = manager.get_ticker_price("BTC-USDT-SWAP")
                if price and price > 0:
                    print(f"    ✅ 价格获取成功: ${price}")
                else:
                    print("    ⚠️ 价格获取失败")
            except Exception as e:
                print(f"    ❌ 价格获取失败: {e}")
        
        else:
            print("❌ UnifiedExchangeManager不可用")
    
    except ImportError as e:
        print(f"❌ WMZC模块导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_api_connection()
'''
    
    with open('test_api_connection.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ API测试脚本已创建: test_api_connection.py")

def suggest_api_fixes():
    """提供API修复建议"""
    print("\n💡 API连接问题修复建议:")
    print("=" * 50)
    
    fixes = [
        {
            "问题": "客户端不支持余额获取",
            "原因": "API客户端缺少get_balance_sync方法或方法调用失败",
            "解决方案": [
                "检查API密钥配置是否正确",
                "确保网络连接正常",
                "验证交易所API服务状态",
                "添加方法存在性检查"
            ]
        },
        {
            "问题": "无法获取真实余额数据",
            "原因": "API认证失败或权限不足",
            "解决方案": [
                "验证API密钥的有效性",
                "检查API权限设置（需要读取权限）",
                "确认API密钥未过期",
                "检查IP白名单设置"
            ]
        },
        {
            "问题": "内存泄漏检测",
            "原因": "系统运行时内存使用增长",
            "解决方案": [
                "这是正常的监控警告",
                "系统会自动进行内存清理",
                "可以通过重启系统释放内存",
                "监控是否持续增长"
            ]
        }
    ]
    
    for i, fix in enumerate(fixes, 1):
        print(f"\n{i}. {fix['问题']}")
        print(f"   原因: {fix['原因']}")
        print("   解决方案:")
        for solution in fix['解决方案']:
            print(f"     • {solution}")

def main():
    """主函数"""
    print("🚀 开始API连接问题诊断和修复")
    print("=" * 60)
    
    # 检查API配置
    api_configs = check_api_configuration()
    
    # 修复API客户端方法
    fix_api_client_methods()
    
    # 创建测试脚本
    create_api_test_script()
    
    # 提供修复建议
    suggest_api_fixes()
    
    print("\n" + "=" * 60)
    print("🎉 API连接问题诊断完成")
    
    # 检查是否有配置问题
    has_config_issues = False
    for config_file, config_info in api_configs.items():
        exchange = config_info['exchange']
        if exchange == 'OKX' and not config_info['okx_configured']:
            has_config_issues = True
            print(f"⚠️ {config_file}: OKX配置不完整")
        elif exchange == 'Gate.io' and not config_info['gate_configured']:
            has_config_issues = True
            print(f"⚠️ {config_file}: Gate.io配置不完整")
    
    if has_config_issues:
        print("\n💡 建议:")
        print("1. 在WMZC系统的主配置页面配置正确的API密钥")
        print("2. 确保API密钥具有读取权限")
        print("3. 检查网络连接和交易所服务状态")
    else:
        print("\n✅ API配置看起来正常，问题可能是:")
        print("1. 网络连接问题")
        print("2. 交易所API服务临时不可用")
        print("3. API密钥权限不足")
        print("4. 代码逻辑需要优化")
    
    print("\n🔧 下一步:")
    print("1. 运行 python test_api_connection.py 测试API连接")
    print("2. 检查WMZC系统日志中的详细错误信息")
    print("3. 如果问题持续，考虑重新配置API密钥")

if __name__ == "__main__":
    main()
