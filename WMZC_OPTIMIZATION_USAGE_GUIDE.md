# 🎯 WMZC优化功能使用指南

## 📋 快速开始

### 1. 文件结构
确保以下优化模块文件存在：
```
WMZC.py                          # 主程序（已集成优化）
trading_loop_modules.py          # 交易循环模块化
performance_optimizer.py         # 性能优化模块
monitoring_system.py             # 监控系统模块
```

### 2. 启动优化系统
```python
# 直接运行WMZC.py，优化系统会自动启动
python WMZC.py

# 或者手动启动优化组件
from performance_optimizer import performance_optimizer
from monitoring_system import monitoring_system

# 启动性能优化器
performance_optimizer.start()

# 启动监控系统
monitoring_system.start()
```

## 🔧 模块化交易循环使用

### 基本使用
```python
from trading_loop_modules import trading_loop_manager

# 启动模块化交易循环
await trading_loop_manager.run_trading_loop()

# 获取性能摘要
performance_summary = trading_loop_manager.get_performance_summary()
print(f"循环平均耗时: {performance_summary['loop_duration']['avg']:.2f}s")
```

### 自定义配置
```python
# 修改循环配置
config['loop_interval'] = 10  # 设置循环间隔为10秒
config['SYMBOL'] = 'ETH-USDT-SWAP'  # 设置交易对
config['TIMEFRAME'] = '5m'  # 设置时间框架
```

### 状态监控
```python
# 获取交易循环状态
state = trading_loop_manager.state
print(f"循环次数: {state.loop_count}")
print(f"错误次数: {state.error_count}")
print(f"最后心跳: {state.last_heartbeat}")
```

## 🚀 性能优化功能使用

### 1. 高级缓存使用
```python
from performance_optimizer import performance_optimizer

# 获取缓存管理器
cache = performance_optimizer.cache_manager

# 设置缓存（带TTL）
cache.set("market_data", "BTC_USDT_1m", market_data, ttl=60)

# 获取缓存
cached_data = cache.get("market_data", "BTC_USDT_1m")

# 清理过期缓存
cleaned_count = cache.cleanup_expired()
print(f"清理了 {cleaned_count} 个过期缓存项")

# 获取缓存统计
stats = cache.get_stats()
print(f"缓存命中率: {stats.hit_rate:.1f}%")
print(f"内存使用: {stats.memory_usage_mb:.2f} MB")
```

### 2. 内存监控使用
```python
# 获取内存统计
memory_stats = performance_optimizer.memory_monitor.get_memory_stats()
print(f"系统内存使用率: {memory_stats.percent:.1f}%")
print(f"进程内存使用: {memory_stats.process_mb:.1f} MB")

# 检查是否需要清理
if performance_optimizer.memory_monitor.should_cleanup():
    cleaned = performance_optimizer.memory_monitor.cleanup_memory()
    print(f"垃圾回收清理了 {cleaned} 个对象")
```

### 3. 性能分析使用
```python
from performance_optimizer import profile_performance

# 使用装饰器进行性能分析
@profile_performance("my_function")
def my_trading_function():
    # 你的交易逻辑
    time.sleep(0.1)
    return "result"

# 异步函数也支持
@profile_performance("my_async_function")
async def my_async_trading_function():
    await asyncio.sleep(0.1)
    return "async_result"

# 获取性能统计
profiler = performance_optimizer.profiler
stats = profiler.get_profile_stats("my_function")
print(f"平均执行时间: {stats['avg']*1000:.1f} ms")
print(f"调用次数: {stats['count']}")
```

### 4. 缓存装饰器使用
```python
from performance_optimizer import cached

# 缓存函数结果
@cached("api_cache", ttl=300)  # 缓存5分钟
def get_market_data(symbol, timeframe):
    # 这个函数的结果会被自动缓存
    return fetch_data_from_api(symbol, timeframe)

# 使用缓存的函数
data = get_market_data("BTC-USDT", "1m")  # 第一次调用，从API获取
data = get_market_data("BTC-USDT", "1m")  # 第二次调用，从缓存获取
```

## 📊 监控系统使用

### 1. 高级日志使用
```python
from monitoring_system import monitoring_system

# 记录不同级别的日志
monitoring_system.log("交易循环启动", "INFO")
monitoring_system.log("API响应慢", "WARNING")
monitoring_system.log("连接失败", "ERROR")

# 带额外数据的日志
monitoring_system.log("订单执行", "INFO", 
                     extra_data={"symbol": "BTC-USDT", "price": 50000})

# 获取最近日志
recent_logs = monitoring_system.logger.get_recent_logs(count=50)
for log in recent_logs:
    print(f"[{log.level}] {log.message}")

# 获取日志统计
stats = monitoring_system.logger.get_stats()
print(f"总日志数: {stats['total']}")
print(f"错误日志数: {stats['ERROR']}")
```

### 2. 性能指标记录
```python
# 记录各种性能指标
monitoring_system.record_metric("api_response_time", 150.5, "ms")
monitoring_system.record_metric("order_count", 10, "count")
monitoring_system.record_metric("profit_rate", 2.5, "percent")

# 带标签的指标
monitoring_system.record_metric("latency", 100, "ms", 
                               tags={"exchange": "gate", "symbol": "BTC-USDT"})

# 获取指标摘要
metrics = monitoring_system.metrics
summary = metrics.get_metric_summary("api_response_time", duration_minutes=60)
print(f"API响应时间 - 平均: {summary['avg']:.1f}ms, 最大: {summary['max']:.1f}ms")
```

### 3. 健康检查使用
```python
# 注册自定义健康检查
def check_trading_status():
    if global_state.trading_active:
        return {'status': 'healthy', 'message': '交易系统正常运行'}
    else:
        return {'status': 'warning', 'message': '交易系统已暂停'}

monitoring_system.health_checker.register_check('trading_system', check_trading_status, 30)

# 获取健康状态
health_status = monitoring_system.health_checker.get_health_status()
print(f"系统整体状态: {health_status['overall']}")

for component, status in health_status['components'].items():
    print(f"{component}: {status['status']} - {status['message']}")
```

### 4. 仪表板数据获取
```python
# 获取完整的仪表板数据
dashboard_data = monitoring_system.get_dashboard_data()

print("=== 系统仪表板 ===")
print(f"健康状态: {dashboard_data['health']['overall']}")
print(f"日志统计: {dashboard_data['log_stats']}")

# 性能指标摘要
metrics_summary = dashboard_data['metrics_summary']
for metric_name, summary in metrics_summary.items():
    if summary:
        print(f"{metric_name}: 平均 {summary['avg']:.2f}")
```

## 🎯 最佳实践

### 1. 性能优化最佳实践
```python
# 1. 合理使用缓存
@cached("expensive_calculation", ttl=600)
def calculate_complex_indicator(data):
    # 复杂计算逻辑
    return result

# 2. 监控关键函数性能
@profile_performance("critical_trading_function")
async def execute_trading_strategy():
    # 关键交易逻辑
    pass

# 3. 定期清理内存
if performance_optimizer.memory_monitor.should_cleanup():
    performance_optimizer.memory_monitor.cleanup_memory()
```

### 2. 监控最佳实践
```python
# 1. 记录关键业务指标
monitoring_system.record_metric("daily_profit", profit, "USD")
monitoring_system.record_metric("trade_success_rate", success_rate, "percent")

# 2. 记录详细的错误信息
try:
    result = risky_operation()
except Exception as e:
    monitoring_system.log(f"操作失败: {e}", "ERROR", 
                         extra_data={"operation": "risky_operation", "params": params})

# 3. 定期检查系统健康
health = monitoring_system.health_checker.get_health_status()
if health['overall'] != 'healthy':
    # 处理健康问题
    handle_health_issues(health)
```

### 3. 集成使用示例
```python
# 完整的优化功能集成示例
@profile_performance("optimized_trading_loop")
@cached("trading_cache", ttl=60)
async def optimized_trading_function(symbol, timeframe):
    # 记录开始
    monitoring_system.log(f"开始处理 {symbol} {timeframe}", "INFO")
    
    try:
        # 获取数据（会被缓存）
        data = await fetch_market_data(symbol, timeframe)
        
        # 记录性能指标
        monitoring_system.record_metric("data_fetch_success", 1, "count")
        
        # 执行交易逻辑
        result = await execute_strategy(data)
        
        # 记录成功
        monitoring_system.log(f"处理完成: {result}", "INFO")
        return result
        
    except Exception as e:
        # 记录错误
        monitoring_system.log(f"处理失败: {e}", "ERROR")
        monitoring_system.record_metric("processing_error", 1, "count")
        raise
```

## 🔧 故障排除

### 常见问题解决

1. **模块导入失败**
```python
# 检查模块是否存在
import os
if not os.path.exists('trading_loop_modules.py'):
    print("缺少 trading_loop_modules.py 文件")

# 检查WMZC中的集成状态
import WMZC
print(f"优化模块可用: {WMZC.OPTIMIZATION_MODULES_AVAILABLE}")
```

2. **内存使用过高**
```python
# 检查缓存使用情况
cache_stats = performance_optimizer.cache_manager.get_stats()
if cache_stats.memory_usage_mb > 100:  # 超过100MB
    performance_optimizer.cache_manager.cleanup_expired()
```

3. **性能监控数据异常**
```python
# 重置性能分析器
performance_optimizer.profiler = PerformanceProfiler()

# 清理监控数据
monitoring_system.metrics = MetricsCollector()
```

## 🎉 总结

通过使用这些优化功能，您可以：

- ✅ **提升系统性能**: 智能缓存和内存优化
- ✅ **改善代码质量**: 模块化架构和性能监控
- ✅ **增强系统监控**: 全方位的日志和指标监控
- ✅ **快速问题定位**: 详细的性能分析和健康检查

优化系统已经完全集成到WMZC中，启动程序即可自动享受所有优化功能！
