#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 WMZC DataFrame布尔值判断Bug修复验证
专门验证DataFrame布尔值判断错误的修复效果
"""

import pandas as pd
import numpy as np
import time
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

def test_dataframe_boolean_fixes():
    """测试DataFrame布尔值判断修复"""
    print("🔍 测试DataFrame布尔值判断修复...")
    
    try:
        # 导入修复后的函数
        from WMZC import calculate_macd, calculate_kdj, calculate_rsi
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'open': np.random.uniform(100, 110, 50),
            'high': np.random.uniform(110, 120, 50),
            'low': np.random.uniform(90, 100, 50),
            'close': np.random.uniform(100, 110, 50),
            'volume': np.random.uniform(1000, 2000, 50)
        })
        
        print(f"  测试数据: {len(test_data)}行")
        
        # 测试MACD计算
        print("  测试MACD计算...")
        start_time = time.time()
        macd_result = calculate_macd(test_data)
        macd_time = time.time() - start_time
        
        if isinstance(macd_result, pd.DataFrame):
            if macd_result.empty:
                print("    ⚠️ MACD返回空DataFrame")
            else:
                print(f"    ✅ MACD计算成功: {len(macd_result)}行, 耗时{macd_time*1000:.1f}ms")
                if 'macd' in macd_result.columns and 'macd_signal' in macd_result.columns:
                    print("    ✅ MACD包含必要列")
                else:
                    print("    ⚠️ MACD缺少必要列")
        else:
            print(f"    ❌ MACD返回类型错误: {type(macd_result)}")
        
        # 测试KDJ计算
        print("  测试KDJ计算...")
        start_time = time.time()
        kdj_result = calculate_kdj(test_data)
        kdj_time = time.time() - start_time
        
        if isinstance(kdj_result, pd.DataFrame):
            if kdj_result.empty:
                print("    ⚠️ KDJ返回空DataFrame")
            else:
                print(f"    ✅ KDJ计算成功: {len(kdj_result)}行, 耗时{kdj_time*1000:.1f}ms")
                if 'k' in kdj_result.columns and 'd' in kdj_result.columns:
                    print("    ✅ KDJ包含必要列")
                else:
                    print("    ⚠️ KDJ缺少必要列")
        else:
            print(f"    ❌ KDJ返回类型错误: {type(kdj_result)}")
        
        # 测试RSI计算
        print("  测试RSI计算...")
        start_time = time.time()
        rsi_result = calculate_rsi(test_data)
        rsi_time = time.time() - start_time
        
        if isinstance(rsi_result, pd.DataFrame):
            if rsi_result.empty:
                print("    ⚠️ RSI返回空DataFrame")
            else:
                print(f"    ✅ RSI计算成功: {len(rsi_result)}行, 耗时{rsi_time*1000:.1f}ms")
                if 'rsi' in rsi_result.columns:
                    print("    ✅ RSI包含必要列")
                else:
                    print("    ⚠️ RSI缺少必要列")
        else:
            print(f"    ❌ RSI返回类型错误: {type(rsi_result)}")
        
        return True
        
    except Exception as e:
        print(f"    ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_insufficient_data_handling():
    """测试数据不足情况的处理"""
    print("\n🔍 测试数据不足情况的处理...")
    
    try:
        from WMZC import calculate_macd, calculate_kdj, calculate_rsi
        
        # 创建不足的测试数据
        insufficient_data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [95, 96, 97],
            'close': [103, 104, 105],
            'volume': [1000, 1100, 1200]
        })
        
        print(f"  测试数据: {len(insufficient_data)}行 (不足)")
        
        # 测试MACD
        macd_result = calculate_macd(insufficient_data)
        if isinstance(macd_result, pd.DataFrame) and macd_result.empty:
            print("    ✅ MACD正确处理数据不足情况")
        else:
            print("    ⚠️ MACD数据不足处理可能有问题")
        
        # 测试KDJ
        kdj_result = calculate_kdj(insufficient_data)
        if isinstance(kdj_result, pd.DataFrame) and kdj_result.empty:
            print("    ✅ KDJ正确处理数据不足情况")
        else:
            print("    ⚠️ KDJ数据不足处理可能有问题")
        
        # 测试RSI
        rsi_result = calculate_rsi(insufficient_data)
        if isinstance(rsi_result, pd.DataFrame) and rsi_result.empty:
            print("    ✅ RSI正确处理数据不足情况")
        else:
            print("    ⚠️ RSI数据不足处理可能有问题")
        
        return True
        
    except Exception as e:
        print(f"    ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_improvements():
    """测试性能改进"""
    print("\n🔍 测试性能改进...")
    
    try:
        from WMZC import calculate_macd, calculate_kdj, calculate_rsi
        
        # 创建较大的测试数据
        large_data = pd.DataFrame({
            'open': np.random.uniform(100, 110, 200),
            'high': np.random.uniform(110, 120, 200),
            'low': np.random.uniform(90, 100, 200),
            'close': np.random.uniform(100, 110, 200),
            'volume': np.random.uniform(1000, 2000, 200)
        })
        
        print(f"  测试数据: {len(large_data)}行")
        
        # 测试MACD性能
        start_time = time.time()
        for i in range(3):  # 多次调用测试缓存效果
            macd_result = calculate_macd(large_data)
        macd_avg_time = (time.time() - start_time) / 3
        
        print(f"    MACD平均耗时: {macd_avg_time*1000:.1f}ms")
        
        # 测试KDJ性能
        start_time = time.time()
        for i in range(3):
            kdj_result = calculate_kdj(large_data)
        kdj_avg_time = (time.time() - start_time) / 3
        
        print(f"    KDJ平均耗时: {kdj_avg_time*1000:.1f}ms")
        
        # 测试RSI性能
        start_time = time.time()
        for i in range(3):
            rsi_result = calculate_rsi(large_data)
        rsi_avg_time = (time.time() - start_time) / 3
        
        print(f"    RSI平均耗时: {rsi_avg_time*1000:.1f}ms")
        
        # 性能评估
        if macd_avg_time < 0.5 and kdj_avg_time < 0.5 and rsi_avg_time < 0.5:
            print("    ✅ 指标计算性能良好 (<500ms)")
        elif macd_avg_time < 1.0 and kdj_avg_time < 1.0 and rsi_avg_time < 1.0:
            print("    👍 指标计算性能可接受 (<1000ms)")
        else:
            print("    ⚠️ 指标计算性能需要进一步优化")
        
        return True
        
    except Exception as e:
        print(f"    ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 WMZC DataFrame Bug修复验证")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("DataFrame布尔值判断修复", test_dataframe_boolean_fixes()))
    test_results.append(("数据不足情况处理", test_insufficient_data_handling()))
    test_results.append(("性能改进", test_performance_improvements()))
    
    # 统计结果
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    print("\n" + "=" * 50)
    print("🎯 修复验证结果")
    print("=" * 50)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n📊 总体成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    
    if success_rate >= 90:
        status = "🎉 优秀"
        recommendation = "Bug修复效果很好，DataFrame布尔值判断问题已解决"
    elif success_rate >= 70:
        status = "👍 良好"
        recommendation = "Bug修复基本成功，还有小部分问题需要关注"
    else:
        status = "⚠️ 需要改进"
        recommendation = "Bug修复需要进一步完善"
    
    print(f"修复状态: {status}")
    print(f"建议: {recommendation}")
    
    return test_results

if __name__ == "__main__":
    try:
        results = main()
        print("\n🎉 验证完成!")
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
