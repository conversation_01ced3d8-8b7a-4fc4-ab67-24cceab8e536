#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 WMZC性能监控脚本
监控修复后的内存使用和响应时间改善
"""

import os
import sys
import time
import psutil
import gc
import threading
from datetime import datetime
from collections import defaultdict, deque

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.metrics = defaultdict(list)
        self.baseline_memory = psutil.virtual_memory().used / 1024 / 1024  # MB
        self.monitoring = False
        self.monitor_thread = None
        
    def start_monitoring(self, duration=60):
        """开始性能监控"""
        print(f"🚀 开始性能监控 (持续{duration}秒)")
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 50)
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(duration,),
            daemon=True
        )
        self.monitor_thread.start()
        
    def _monitor_loop(self, duration):
        """监控循环"""
        end_time = time.time() + duration
        
        while time.time() < end_time and self.monitoring:
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 收集Python进程指标
                self._collect_process_metrics()
                
                # 收集垃圾回收指标
                self._collect_gc_metrics()
                
                time.sleep(1)  # 每秒采样一次
                
            except Exception as e:
                print(f"❌ 监控异常: {e}")
                
        self.monitoring = False
        
    def _collect_system_metrics(self):
        """收集系统指标"""
        # 内存使用
        memory = psutil.virtual_memory()
        self.metrics['system_memory_percent'].append(memory.percent)
        self.metrics['system_memory_used_mb'].append(memory.used / 1024 / 1024)
        
        # CPU使用
        cpu_percent = psutil.cpu_percent(interval=None)
        self.metrics['system_cpu_percent'].append(cpu_percent)
        
        # 磁盘IO
        disk_io = psutil.disk_io_counters()
        if disk_io:
            self.metrics['disk_read_mb'].append(disk_io.read_bytes / 1024 / 1024)
            self.metrics['disk_write_mb'].append(disk_io.write_bytes / 1024 / 1024)
    
    def _collect_process_metrics(self):
        """收集当前进程指标"""
        process = psutil.Process()
        
        # 进程内存
        memory_info = process.memory_info()
        self.metrics['process_memory_rss_mb'].append(memory_info.rss / 1024 / 1024)
        self.metrics['process_memory_vms_mb'].append(memory_info.vms / 1024 / 1024)
        
        # 进程CPU
        cpu_percent = process.cpu_percent()
        self.metrics['process_cpu_percent'].append(cpu_percent)
        
        # 线程数
        num_threads = process.num_threads()
        self.metrics['process_threads'].append(num_threads)
        
        # 文件描述符
        try:
            num_fds = process.num_fds() if hasattr(process, 'num_fds') else 0
            self.metrics['process_file_descriptors'].append(num_fds)
        except:
            pass
    
    def _collect_gc_metrics(self):
        """收集垃圾回收指标"""
        # 垃圾回收统计
        gc_stats = gc.get_stats()
        if gc_stats:
            for i, stat in enumerate(gc_stats):
                self.metrics[f'gc_gen{i}_collections'].append(stat['collections'])
                self.metrics[f'gc_gen{i}_collected'].append(stat['collected'])
                self.metrics[f'gc_gen{i}_uncollectable'].append(stat['uncollectable'])
        
        # 当前对象数量
        obj_count = len(gc.get_objects())
        self.metrics['gc_objects_count'].append(obj_count)
    
    def test_memory_performance(self):
        """测试内存性能"""
        print("🧪 测试内存性能...")
        
        # 测试大对象创建和释放
        start_memory = psutil.virtual_memory().used / 1024 / 1024
        
        # 创建大对象
        large_objects = []
        for i in range(100):
            large_objects.append([0] * 10000)  # 每个约80KB
        
        peak_memory = psutil.virtual_memory().used / 1024 / 1024
        
        # 释放对象
        del large_objects
        gc.collect()
        
        end_memory = psutil.virtual_memory().used / 1024 / 1024
        
        print(f"  开始内存: {start_memory:.1f}MB")
        print(f"  峰值内存: {peak_memory:.1f}MB")
        print(f"  结束内存: {end_memory:.1f}MB")
        print(f"  内存增长: {peak_memory - start_memory:.1f}MB")
        print(f"  内存回收: {peak_memory - end_memory:.1f}MB")
        
        # 评估内存回收效率
        recovery_rate = (peak_memory - end_memory) / (peak_memory - start_memory) * 100
        print(f"  回收效率: {recovery_rate:.1f}%")
        
        return recovery_rate > 80  # 80%以上回收率为良好
    
    def test_response_time(self):
        """测试响应时间"""
        print("\n🧪 测试响应时间...")
        
        response_times = []
        
        # 测试简单计算的响应时间
        for i in range(100):
            start = time.perf_counter()
            
            # 模拟计算任务
            result = sum(range(1000))
            
            end = time.perf_counter()
            response_times.append((end - start) * 1000)  # 转换为毫秒
        
        avg_response = sum(response_times) / len(response_times)
        max_response = max(response_times)
        min_response = min(response_times)
        
        print(f"  平均响应时间: {avg_response:.3f}ms")
        print(f"  最大响应时间: {max_response:.3f}ms")
        print(f"  最小响应时间: {min_response:.3f}ms")
        
        # 评估响应时间
        return avg_response < 1.0  # 平均响应时间小于1ms为良好
    
    def test_concurrent_performance(self):
        """测试并发性能"""
        print("\n🧪 测试并发性能...")
        
        import concurrent.futures
        
        def cpu_task(n):
            """CPU密集型任务"""
            return sum(i * i for i in range(n))
        
        start_time = time.perf_counter()
        
        # 并发执行任务
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(cpu_task, 10000) for _ in range(20)]
            results = [future.result() for future in futures]
        
        end_time = time.perf_counter()
        
        concurrent_time = end_time - start_time
        print(f"  并发执行时间: {concurrent_time:.3f}s")
        
        # 串行执行对比
        start_time = time.perf_counter()
        serial_results = [cpu_task(10000) for _ in range(20)]
        end_time = time.perf_counter()
        
        serial_time = end_time - start_time
        print(f"  串行执行时间: {serial_time:.3f}s")
        
        speedup = serial_time / concurrent_time
        print(f"  并发加速比: {speedup:.2f}x")
        
        return speedup > 1.5  # 1.5倍以上加速为良好
    
    def generate_report(self):
        """生成性能报告"""
        print("\n" + "=" * 50)
        print("📊 性能监控报告")
        print("=" * 50)
        
        if not self.metrics:
            print("❌ 没有收集到性能数据")
            return False
        
        # 内存分析
        if 'process_memory_rss_mb' in self.metrics:
            memory_data = self.metrics['process_memory_rss_mb']
            print(f"📈 内存使用分析:")
            print(f"  初始内存: {memory_data[0]:.1f}MB")
            print(f"  峰值内存: {max(memory_data):.1f}MB")
            print(f"  平均内存: {sum(memory_data)/len(memory_data):.1f}MB")
            print(f"  内存增长: {max(memory_data) - memory_data[0]:.1f}MB")
        
        # CPU分析
        if 'process_cpu_percent' in self.metrics:
            cpu_data = self.metrics['process_cpu_percent']
            avg_cpu = sum(cpu_data) / len(cpu_data)
            max_cpu = max(cpu_data)
            print(f"\n🔥 CPU使用分析:")
            print(f"  平均CPU: {avg_cpu:.1f}%")
            print(f"  峰值CPU: {max_cpu:.1f}%")
        
        # 垃圾回收分析
        if 'gc_objects_count' in self.metrics:
            gc_data = self.metrics['gc_objects_count']
            print(f"\n🗑️ 垃圾回收分析:")
            print(f"  初始对象数: {gc_data[0]}")
            print(f"  峰值对象数: {max(gc_data)}")
            print(f"  平均对象数: {sum(gc_data)//len(gc_data)}")
        
        # 线程分析
        if 'process_threads' in self.metrics:
            thread_data = self.metrics['process_threads']
            print(f"\n🧵 线程分析:")
            print(f"  平均线程数: {sum(thread_data)//len(thread_data)}")
            print(f"  峰值线程数: {max(thread_data)}")
        
        return True
    
    def run_performance_tests(self):
        """运行性能测试"""
        print("🚀 开始性能测试")
        print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        results = {}
        
        # 内存性能测试
        results['memory'] = self.test_memory_performance()
        
        # 响应时间测试
        results['response_time'] = self.test_response_time()
        
        # 并发性能测试
        results['concurrent'] = self.test_concurrent_performance()
        
        # 开始监控
        self.start_monitoring(30)  # 监控30秒
        
        # 等待监控完成
        if self.monitor_thread:
            self.monitor_thread.join()
        
        # 生成报告
        self.generate_report()
        
        # 总体评估
        print(f"\n🎯 性能测试总结:")
        passed_tests = sum(results.values())
        total_tests = len(results)
        
        for test_name, result in results.items():
            status = "✅" if result else "❌"
            print(f"{status} {test_name}: {'通过' if result else '需要优化'}")
        
        success_rate = passed_tests / total_tests * 100
        print(f"\n📊 性能评分: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("✅ 优秀！性能表现良好")
            return True
        elif success_rate >= 60:
            print("⚠️ 良好，但有优化空间")
            return False
        else:
            print("❌ 需要性能优化")
            return False

def main():
    """主函数"""
    monitor = PerformanceMonitor()
    success = monitor.run_performance_tests()
    
    print("\n🎉 性能监控完成！")
    return success

if __name__ == "__main__":
    main()
