#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
技术指标计算测试脚本
基于真实Gate.io市场数据验证技术指标计算准确性
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.append('.')

# 导入WMZC模块
try:
    import WMZC
    print("✅ WMZC模块导入成功")
except ImportError as e:
    print(f"❌ WMZC模块导入失败: {e}")
    sys.exit(1)

# Gate.io API 凭证
API_KEY = "d5ea5faa068d66204bb68b75201c56d5"
SECRET_KEY = "5b516e55788fba27e61f9bd06b22ab3661b3115797076d5e73199bea3a8afb1c"

async def main():
    """主测试函数"""
    print("=" * 80)
    print("📊 技术指标计算测试开始")
    print("=" * 80)
    
    # 创建API测试器
    api_tester = WMZC.GateIOAPITester(API_KEY, SECRET_KEY)
    
    # 创建技术指标计算器
    indicator_calculator = WMZC.TechnicalIndicatorCalculator(api_tester)
    
    try:
        # 运行完整的技术指标测试
        results = await indicator_calculator.run_comprehensive_indicator_test("BTC_USDT")
        
        # 显示详细结果
        print("\n" + "=" * 80)
        print("📈 技术指标计算结果")
        print("=" * 80)
        
        if results.get("success"):
            print("🎉 所有技术指标计算成功！")
            
            # 显示最新指标值
            if results.get("sma_20"):
                print(f"📊 SMA(20): {results['sma_20'][-1]:.2f}")
            
            if results.get("ema_20"):
                print(f"📊 EMA(20): {results['ema_20'][-1]:.2f}")
            
            if results.get("macd") and results["macd"].get("macd"):
                macd_data = results["macd"]
                print(f"📊 MACD: {macd_data['macd'][-1]:.4f}")
                if macd_data.get("signal"):
                    print(f"📊 MACD信号线: {macd_data['signal'][-1]:.4f}")
                if macd_data.get("histogram"):
                    print(f"📊 MACD柱状图: {macd_data['histogram'][-1]:.4f}")
            
            if results.get("rsi"):
                rsi_value = results["rsi"][-1]
                print(f"📊 RSI(14): {rsi_value:.2f}")
                if rsi_value > 70:
                    print("   ⚠️ RSI超买信号")
                elif rsi_value < 30:
                    print("   ⚠️ RSI超卖信号")
                else:
                    print("   ✅ RSI正常区间")
            
            if results.get("bollinger"):
                boll_data = results["bollinger"]
                if boll_data.get("upper") and boll_data.get("middle") and boll_data.get("lower"):
                    print(f"📊 布林带上轨: {boll_data['upper'][-1]:.2f}")
                    print(f"📊 布林带中轨: {boll_data['middle'][-1]:.2f}")
                    print(f"📊 布林带下轨: {boll_data['lower'][-1]:.2f}")
            
            if results.get("kdj"):
                kdj_data = results["kdj"]
                if kdj_data.get("k") and kdj_data.get("d") and kdj_data.get("j"):
                    print(f"📊 KDJ K值: {kdj_data['k'][-1]:.2f}")
                    print(f"📊 KDJ D值: {kdj_data['d'][-1]:.2f}")
                    print(f"📊 KDJ J值: {kdj_data['j'][-1]:.2f}")
                    
                    # KDJ信号分析
                    k_val = kdj_data['k'][-1]
                    d_val = kdj_data['d'][-1]
                    if k_val > d_val and k_val < 80:
                        print("   📈 KDJ金叉信号（买入）")
                    elif k_val < d_val and k_val > 20:
                        print("   📉 KDJ死叉信号（卖出）")
                    else:
                        print("   ➡️ KDJ无明确信号")
            
            print("\n✅ 技术指标计算验证完成")
            
        else:
            print("❌ 技术指标计算失败")
            if results.get("error"):
                print(f"错误信息: {results['error']}")
            if results.get("success_rate"):
                print(f"成功率: {results['success_rate']}")
        
        print("=" * 80)
        
        return results
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_trading_signals(results):
    """分析交易信号"""
    if not results or not results.get("success"):
        return
    
    print("\n" + "=" * 80)
    print("🎯 交易信号分析")
    print("=" * 80)
    
    signals = []
    
    # RSI信号
    if results.get("rsi"):
        rsi_value = results["rsi"][-1]
        if rsi_value > 70:
            signals.append("RSI超买，建议卖出")
        elif rsi_value < 30:
            signals.append("RSI超卖，建议买入")
    
    # MACD信号
    if results.get("macd"):
        macd_data = results["macd"]
        if (macd_data.get("macd") and macd_data.get("signal") and 
            len(macd_data["macd"]) >= 2 and len(macd_data["signal"]) >= 2):
            
            current_macd = macd_data["macd"][-1]
            prev_macd = macd_data["macd"][-2]
            current_signal = macd_data["signal"][-1]
            prev_signal = macd_data["signal"][-2]
            
            if prev_macd <= prev_signal and current_macd > current_signal:
                signals.append("MACD金叉，建议买入")
            elif prev_macd >= prev_signal and current_macd < current_signal:
                signals.append("MACD死叉，建议卖出")
    
    # KDJ信号
    if results.get("kdj"):
        kdj_data = results["kdj"]
        if (kdj_data.get("k") and kdj_data.get("d") and 
            len(kdj_data["k"]) >= 2 and len(kdj_data["d"]) >= 2):
            
            current_k = kdj_data["k"][-1]
            prev_k = kdj_data["k"][-2]
            current_d = kdj_data["d"][-1]
            prev_d = kdj_data["d"][-2]
            
            if prev_k <= prev_d and current_k > current_d and current_k < 80:
                signals.append("KDJ金叉，建议买入")
            elif prev_k >= prev_d and current_k < current_d and current_k > 20:
                signals.append("KDJ死叉，建议卖出")
    
    # 显示信号
    if signals:
        print("📈 检测到的交易信号:")
        for i, signal in enumerate(signals, 1):
            print(f"   {i}. {signal}")
        
        # 综合判断
        buy_signals = sum(1 for s in signals if "买入" in s)
        sell_signals = sum(1 for s in signals if "卖出" in s)
        
        if buy_signals > sell_signals:
            print("\n🟢 综合判断: 偏向买入")
        elif sell_signals > buy_signals:
            print("\n🔴 综合判断: 偏向卖出")
        else:
            print("\n🟡 综合判断: 观望")
    else:
        print("📊 当前无明确交易信号，建议观望")
    
    print("=" * 80)

if __name__ == "__main__":
    # 运行异步测试
    try:
        results = asyncio.run(main())
        
        if results and results.get("success"):
            # 分析交易信号
            analyze_trading_signals(results)
            
            print("\n🚀 准备进行下一步：策略集成测试")
            sys.exit(0)
        else:
            print("\n❌ 技术指标测试失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试脚本异常: {e}")
        sys.exit(1)
