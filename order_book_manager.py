#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能订单簿管理器 - 为WMZC量化交易系统优化WebSocket实时数据处理
支持增量更新、数据完整性验证和多交易所统一接口
"""

import asyncio
import time
import json
import logging
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Callable, Any
from decimal import Decimal, ROUND_HALF_UP
from enum import Enum
# 🛠️ BUG修复 #3: 移除threading导入，严格使用异步编程

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ExchangeType(Enum):
    """交易所类型"""
    OKX = "okx"
    GATE = "gate"


@dataclass
class OrderBookLevel:
    """订单簿价格档位"""
    price: Decimal
    size: Decimal
    timestamp: float = field(default_factory=time.time)
    
    def __post_init__(self):
        # 确保使用Decimal类型避免浮点精度问题
        if not isinstance(self.price, Decimal):
            self.price = Decimal(str(self.price))
        if not isinstance(self.size, Decimal):
            self.size = Decimal(str(self.size))


@dataclass
class OrderBookSnapshot:
    """订单簿快照"""
    symbol: str
    bids: List[OrderBookLevel] = field(default_factory=list)
    asks: List[OrderBookLevel] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)
    sequence: int = 0
    checksum: Optional[str] = None
    
    def get_best_bid(self) -> Optional[OrderBookLevel]:
        """获取最佳买价"""
        return self.bids[0] if self.bids else None
    
    def get_best_ask(self) -> Optional[OrderBookLevel]:
        """获取最佳卖价"""
        return self.asks[0] if self.asks else None
    
    def get_spread(self) -> Optional[Decimal]:
        """获取买卖价差"""
        best_bid = self.get_best_bid()
        best_ask = self.get_best_ask()
        if best_bid and best_ask:
            return best_ask.price - best_bid.price
        return None
    
    def get_mid_price(self) -> Optional[Decimal]:
        """获取中间价"""
        best_bid = self.get_best_bid()
        best_ask = self.get_best_ask()
        if best_bid and best_ask:
            return (best_bid.price + best_ask.price) / 2
        return None


@dataclass
class OrderBookUpdate:
    """订单簿增量更新"""
    symbol: str
    bids: List[Tuple[Decimal, Decimal]] = field(default_factory=list)  # (price, size)
    asks: List[Tuple[Decimal, Decimal]] = field(default_factory=list)  # (price, size)
    timestamp: float = field(default_factory=time.time)
    sequence: int = 0
    is_snapshot: bool = False


class OrderBookManager:
    """
    高性能订单簿管理器
    
    功能特性：
    1. 支持多交易所订单簿维护
    2. 增量更新和数据完整性验证
    3. 自动重连和数据同步
    4. 高精度价格计算（Decimal）
    5. 实时统计和监控
    6. 线程安全操作
    """
    
    def __init__(self, exchange_type: ExchangeType):
        self.exchange_type = exchange_type
        
        # 订单簿数据存储
        self.order_books: Dict[str, OrderBookSnapshot] = {}
        self.update_queues: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # 序列号管理
        self.last_sequences: Dict[str, int] = defaultdict(int)
        self.expected_sequences: Dict[str, int] = defaultdict(int)
        
        # 数据完整性检查
        self.checksum_validators: Dict[str, Callable] = {}
        self.integrity_errors: Dict[str, int] = defaultdict(int)
        
        # 统计信息
        self.stats = {
            'total_updates': 0,
            'successful_updates': 0,
            'integrity_errors': 0,
            'sequence_gaps': 0,
            'reconnections': 0,
            'avg_update_latency': 0.0,
            'symbols_count': 0
        }
        
        # 🛠️ BUG修复 #4: 使用异步锁替代线程锁
        # 异步安全锁
        self._locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
        self._global_lock = asyncio.Lock()
        
        # 回调函数
        self.update_callbacks: List[Callable] = []
        self.error_callbacks: List[Callable] = []
        
        # 配置参数
        self.config = self._get_exchange_config()
        
        logger.info(f"✅ {exchange_type.value.upper()} 订单簿管理器初始化完成")

    def _get_exchange_config(self) -> Dict:
        """获取交易所特定配置"""
        configs = {
            ExchangeType.OKX: {
                'max_depth': 400,
                'checksum_enabled': True,
                'sequence_validation': True,
                'update_interval': 100,  # ms
                'reconnect_delay': 5.0
            },
            ExchangeType.GATE: {
                'max_depth': 100,
                'checksum_enabled': False,
                'sequence_validation': True,
                'update_interval': 100,  # ms
                'reconnect_delay': 3.0
            }
        }
        return configs[self.exchange_type]

    async def initialize_symbol(self, symbol: str, snapshot_data: Dict = None) -> bool:
        """
        初始化交易对订单簿
        
        Args:
            symbol: 交易对符号
            snapshot_data: 初始快照数据
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 🛠️ BUG修复 #4: 使用异步锁
            async with self._locks[symbol]:
                if snapshot_data:
                    # 使用提供的快照数据
                    snapshot = self._parse_snapshot(symbol, snapshot_data)
                else:
                    # 获取初始快照
                    snapshot = await self._fetch_initial_snapshot(symbol)
                
                if snapshot:
                    self.order_books[symbol] = snapshot
                    self.last_sequences[symbol] = snapshot.sequence
                    self.expected_sequences[symbol] = snapshot.sequence + 1
                    
                    # 设置校验和验证器
                    if self.config['checksum_enabled']:
                        self.checksum_validators[symbol] = self._create_checksum_validator(symbol)
                    
                    self.stats['symbols_count'] = len(self.order_books)
                    logger.info(f"✅ {symbol} 订单簿初始化成功 (深度: {len(snapshot.bids)}x{len(snapshot.asks)})")
                    return True
                else:
                    logger.error(f"❌ {symbol} 订单簿初始化失败：无法获取快照")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ {symbol} 订单簿初始化异常: {e}")
            return False

    async def process_update(self, symbol: str, update_data: Dict) -> bool:
        """
        处理订单簿增量更新
        
        Args:
            symbol: 交易对符号
            update_data: 更新数据
            
        Returns:
            bool: 处理是否成功
        """
        start_time = time.time()
        
        try:
            # 🛠️ BUG修复 #4: 使用异步锁
            async with self._locks[symbol]:
                # 解析更新数据
                update = self._parse_update(symbol, update_data)
                if not update:
                    return False
                
                # 检查序列号连续性
                if self.config['sequence_validation']:
                    if not self._validate_sequence(symbol, update.sequence):
                        logger.warning(f"⚠️ {symbol} 序列号不连续: 期望{self.expected_sequences[symbol]}, 收到{update.sequence}")
                        self.stats['sequence_gaps'] += 1
                        # 触发重新同步
                        await self._resync_orderbook(symbol)
                        return False
                
                # 应用更新
                success = self._apply_update(symbol, update)
                
                if success:
                    # 数据完整性检查
                    if self.config['checksum_enabled'] and update.sequence % 10 == 0:  # 每10次更新检查一次
                        if not self._validate_checksum(symbol):
                            logger.error(f"❌ {symbol} 校验和验证失败")
                            self.stats['integrity_errors'] += 1
                            self.integrity_errors[symbol] += 1
                            await self._resync_orderbook(symbol)
                            return False
                    
                    # 更新统计
                    self.stats['total_updates'] += 1
                    self.stats['successful_updates'] += 1
                    
                    # 计算延迟
                    latency = (time.time() - start_time) * 1000  # ms
                    self._update_latency_stats(latency)
                    
                    # 触发回调
                    await self._trigger_update_callbacks(symbol, self.order_books[symbol])
                    
                    logger.debug(f"✅ {symbol} 订单簿更新成功 (序列: {update.sequence}, 延迟: {latency:.2f}ms)")
                    return True
                else:
                    logger.error(f"❌ {symbol} 订单簿更新失败")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ {symbol} 订单簿更新异常: {e}")
            await self._trigger_error_callbacks(symbol, str(e))
            return False

    def _parse_snapshot(self, symbol: str, data: Dict) -> Optional[OrderBookSnapshot]:
        """解析订单簿快照数据"""
        try:
            if self.exchange_type == ExchangeType.OKX:
                return self._parse_okx_snapshot(symbol, data)
            elif self.exchange_type == ExchangeType.GATE:
                return self._parse_gate_snapshot(symbol, data)
            else:
                logger.error(f"❌ 不支持的交易所类型: {self.exchange_type}")
                return None
        except Exception as e:
            logger.error(f"❌ {symbol} 快照解析失败: {e}")
            return None

    def _parse_okx_snapshot(self, symbol: str, data: Dict) -> Optional[OrderBookSnapshot]:
        """解析OKX订单簿快照"""
        try:
            snapshot_data = data.get('data', [{}])[0]
            
            bids = []
            for bid_data in snapshot_data.get('bids', []):
                price, size, _, _ = bid_data  # OKX格式: [price, size, liquidated_orders, order_count]
                bids.append(OrderBookLevel(Decimal(price), Decimal(size)))
            
            asks = []
            for ask_data in snapshot_data.get('asks', []):
                price, size, _, _ = ask_data
                asks.append(OrderBookLevel(Decimal(price), Decimal(size)))
            
            # 排序：买单价格从高到低，卖单价格从低到高
            bids.sort(key=lambda x: x.price, reverse=True)
            asks.sort(key=lambda x: x.price)
            
            return OrderBookSnapshot(
                symbol=symbol,
                bids=bids,
                asks=asks,
                timestamp=float(snapshot_data.get('ts', time.time() * 1000)) / 1000,
                sequence=int(snapshot_data.get('seqId', 0)),
                checksum=snapshot_data.get('checksum')
            )
            
        except Exception as e:
            logger.error(f"❌ OKX快照解析失败: {e}")
            return None

    def _parse_gate_snapshot(self, symbol: str, data: Dict) -> Optional[OrderBookSnapshot]:
        """解析Gate.io订单簿快照"""
        try:
            bids = []
            for bid_data in data.get('bids', []):
                price, size = bid_data  # Gate.io格式: [price, size]
                bids.append(OrderBookLevel(Decimal(price), Decimal(size)))
            
            asks = []
            for ask_data in data.get('asks', []):
                price, size = ask_data
                asks.append(OrderBookLevel(Decimal(price), Decimal(size)))
            
            # 排序
            bids.sort(key=lambda x: x.price, reverse=True)
            asks.sort(key=lambda x: x.price)
            
            return OrderBookSnapshot(
                symbol=symbol,
                bids=bids,
                asks=asks,
                timestamp=float(data.get('t', time.time() * 1000)) / 1000,
                sequence=int(data.get('id', 0))
            )
            
        except Exception as e:
            logger.error(f"❌ Gate.io快照解析失败: {e}")
            return None

    def _parse_update(self, symbol: str, data: Dict) -> Optional[OrderBookUpdate]:
        """解析订单簿更新数据"""
        try:
            if self.exchange_type == ExchangeType.OKX:
                return self._parse_okx_update(symbol, data)
            elif self.exchange_type == ExchangeType.GATE:
                return self._parse_gate_update(symbol, data)
            else:
                return None
        except Exception as e:
            logger.error(f"❌ {symbol} 更新解析失败: {e}")
            return None

    def _parse_okx_update(self, symbol: str, data: Dict) -> Optional[OrderBookUpdate]:
        """解析OKX订单簿更新"""
        try:
            update_data = data.get('data', [{}])[0]
            
            bids = []
            for bid_data in update_data.get('bids', []):
                price, size, _, _ = bid_data
                bids.append((Decimal(price), Decimal(size)))
            
            asks = []
            for ask_data in update_data.get('asks', []):
                price, size, _, _ = ask_data
                asks.append((Decimal(price), Decimal(size)))
            
            return OrderBookUpdate(
                symbol=symbol,
                bids=bids,
                asks=asks,
                timestamp=float(update_data.get('ts', time.time() * 1000)) / 1000,
                sequence=int(update_data.get('seqId', 0)),
                is_snapshot=data.get('action') == 'snapshot'
            )
            
        except Exception as e:
            logger.error(f"❌ OKX更新解析失败: {e}")
            return None

    def _parse_gate_update(self, symbol: str, data: Dict) -> Optional[OrderBookUpdate]:
        """解析Gate.io订单簿更新"""
        try:
            bids = []
            for bid_data in data.get('b', []):  # Gate.io使用'b'表示bids
                price, size = bid_data
                bids.append((Decimal(price), Decimal(size)))
            
            asks = []
            for ask_data in data.get('a', []):  # Gate.io使用'a'表示asks
                price, size = ask_data
                asks.append((Decimal(price), Decimal(size)))
            
            return OrderBookUpdate(
                symbol=symbol,
                bids=bids,
                asks=asks,
                timestamp=float(data.get('t', time.time() * 1000)) / 1000,
                sequence=int(data.get('u', 0)),  # Gate.io使用'u'表示update id
                is_snapshot=data.get('s', 0) == 1  # Gate.io使用's'表示是否为快照
            )
            
        except Exception as e:
            logger.error(f"❌ Gate.io更新解析失败: {e}")
            return None

    def _apply_update(self, symbol: str, update: OrderBookUpdate) -> bool:
        """应用订单簿更新"""
        try:
            if symbol not in self.order_books:
                logger.error(f"❌ {symbol} 订单簿未初始化")
                return False

            order_book = self.order_books[symbol]

            # 应用买单更新
            for price, size in update.bids:
                self._update_price_level(order_book.bids, price, size, reverse=True)

            # 应用卖单更新
            for price, size in update.asks:
                self._update_price_level(order_book.asks, price, size, reverse=False)

            # 更新时间戳和序列号
            order_book.timestamp = update.timestamp
            order_book.sequence = update.sequence
            self.last_sequences[symbol] = update.sequence
            self.expected_sequences[symbol] = update.sequence + 1

            return True

        except Exception as e:
            logger.error(f"❌ {symbol} 更新应用失败: {e}")
            return False

    def _update_price_level(self, levels: List[OrderBookLevel], price: Decimal, size: Decimal, reverse: bool):
        """更新价格档位"""
        # 查找现有价格档位
        for i, level in enumerate(levels):
            if level.price == price:
                if size == 0:
                    # 删除价格档位
                    levels.pop(i)
                else:
                    # 更新价格档位
                    level.size = size
                    level.timestamp = time.time()
                return

        # 如果是新价格档位且数量不为0，插入到正确位置
        if size > 0:
            new_level = OrderBookLevel(price, size)

            # 找到插入位置
            insert_pos = 0
            for i, level in enumerate(levels):
                if (reverse and price > level.price) or (not reverse and price < level.price):
                    insert_pos = i
                    break
                insert_pos = i + 1

            levels.insert(insert_pos, new_level)

            # 限制深度
            max_depth = self.config['max_depth']
            if len(levels) > max_depth:
                levels = levels[:max_depth]

    def _validate_sequence(self, symbol: str, sequence: int) -> bool:
        """验证序列号连续性"""
        expected = self.expected_sequences[symbol]
        if sequence == expected:
            return True
        elif sequence > expected:
            # 可能有遗漏的更新，需要重新同步
            logger.warning(f"⚠️ {symbol} 序列号跳跃: 期望{expected}, 收到{sequence}")
            return False
        else:
            # 收到过期的更新，忽略
            logger.debug(f"🔄 {symbol} 收到过期更新: {sequence} < {expected}")
            return False

    def _validate_checksum(self, symbol: str) -> bool:
        """验证订单簿校验和"""
        if symbol not in self.checksum_validators:
            return True

        try:
            validator = self.checksum_validators[symbol]
            return validator(self.order_books[symbol])
        except Exception as e:
            logger.error(f"❌ {symbol} 校验和验证异常: {e}")
            return False

    def _create_checksum_validator(self, symbol: str) -> Callable:
        """创建校验和验证器"""
        if self.exchange_type == ExchangeType.OKX:
            return self._create_okx_checksum_validator()
        else:
            return lambda x: True  # 其他交易所暂不支持校验和

    def _create_okx_checksum_validator(self) -> Callable:
        """创建OKX校验和验证器"""
        def validate(order_book: OrderBookSnapshot) -> bool:
            try:
                # OKX校验和算法：取前25档买卖单，按价格:数量格式拼接后计算CRC32
                import zlib

                data_str = ""

                # 买单（价格从高到低）
                for i, bid in enumerate(order_book.bids[:25]):
                    data_str += f"{bid.price}:{bid.size}:"

                # 卖单（价格从低到高）
                for i, ask in enumerate(order_book.asks[:25]):
                    data_str += f"{ask.price}:{ask.size}:"

                # 计算CRC32
                calculated_checksum = str(zlib.crc32(data_str.encode()) & 0xffffffff)

                # 与接收到的校验和比较
                return calculated_checksum == order_book.checksum

            except Exception as e:
                logger.error(f"❌ OKX校验和计算失败: {e}")
                return False

        return validate

    async def _fetch_initial_snapshot(self, symbol: str) -> Optional[OrderBookSnapshot]:
        """获取初始订单簿快照"""
        # 这里应该调用REST API获取快照
        # 由于需要与WMZC系统集成，这里返回None，由外部提供快照
        logger.warning(f"⚠️ {symbol} 需要外部提供初始快照")
        return None

    async def _resync_orderbook(self, symbol: str):
        """重新同步订单簿"""
        try:
            logger.info(f"🔄 开始重新同步 {symbol} 订单簿")

            # 获取新的快照
            snapshot = await self._fetch_initial_snapshot(symbol)
            if snapshot:
                # 🛠️ BUG修复 #4: 使用异步锁
                async with self._locks[symbol]:
                    self.order_books[symbol] = snapshot
                    self.last_sequences[symbol] = snapshot.sequence
                    self.expected_sequences[symbol] = snapshot.sequence + 1

                self.stats['reconnections'] += 1
                logger.info(f"✅ {symbol} 订单簿重新同步完成")
            else:
                logger.error(f"❌ {symbol} 订单簿重新同步失败")

        except Exception as e:
            logger.error(f"❌ {symbol} 订单簿重新同步异常: {e}")

    def _update_latency_stats(self, latency: float):
        """更新延迟统计"""
        total_updates = self.stats['successful_updates']
        if total_updates == 1:
            self.stats['avg_update_latency'] = latency
        else:
            # 计算移动平均
            self.stats['avg_update_latency'] = (
                (self.stats['avg_update_latency'] * (total_updates - 1) + latency) / total_updates
            )

    async def _trigger_update_callbacks(self, symbol: str, order_book: OrderBookSnapshot):
        """触发更新回调"""
        for callback in self.update_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(symbol, order_book)
                else:
                    callback(symbol, order_book)
            except Exception as e:
                logger.error(f"❌ 更新回调执行失败: {e}")

    async def _trigger_error_callbacks(self, symbol: str, error: str):
        """触发错误回调"""
        for callback in self.error_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(symbol, error)
                else:
                    callback(symbol, error)
            except Exception as e:
                logger.error(f"❌ 错误回调执行失败: {e}")

    async def get_order_book(self, symbol: str) -> Optional[OrderBookSnapshot]:
        """获取订单簿快照"""
        # 🛠️ BUG修复 #4: 使用异步锁
        async with self._locks[symbol]:
            return self.order_books.get(symbol)

    async def get_best_prices(self, symbol: str) -> Tuple[Optional[Decimal], Optional[Decimal]]:
        """获取最佳买卖价"""
        # 🛠️ BUG修复 #10: 正确调用异步方法
        order_book = await self.get_order_book(symbol)
        if order_book:
            best_bid = order_book.get_best_bid()
            best_ask = order_book.get_best_ask()
            return (best_bid.price if best_bid else None,
                   best_ask.price if best_ask else None)
        return None, None

    async def get_stats(self) -> Dict:
        """获取统计信息"""
        # 🛠️ BUG修复 #4: 使用异步锁
        async with self._global_lock:
            return {
                **self.stats,
                'symbols': list(self.order_books.keys()),
                'integrity_error_details': dict(self.integrity_errors)
            }

    def add_update_callback(self, callback: Callable):
        """添加更新回调"""
        self.update_callbacks.append(callback)

    def add_error_callback(self, callback: Callable):
        """添加错误回调"""
        self.error_callbacks.append(callback)

    async def remove_symbol(self, symbol: str):
        """移除交易对"""
        # 🛠️ BUG修复 #4: 使用异步锁
        async with self._locks[symbol]:
            if symbol in self.order_books:
                del self.order_books[symbol]
            if symbol in self.last_sequences:
                del self.last_sequences[symbol]
            if symbol in self.expected_sequences:
                del self.expected_sequences[symbol]
            if symbol in self.checksum_validators:
                del self.checksum_validators[symbol]
            if symbol in self.integrity_errors:
                del self.integrity_errors[symbol]

        self.stats['symbols_count'] = len(self.order_books)
        logger.info(f"🗑️ {symbol} 订单簿已移除")


# 全局管理器实例
order_book_managers: Dict[ExchangeType, OrderBookManager] = {}


def get_order_book_manager(exchange_type: ExchangeType) -> OrderBookManager:
    """获取订单簿管理器实例"""
    if exchange_type not in order_book_managers:
        order_book_managers[exchange_type] = OrderBookManager(exchange_type)
    return order_book_managers[exchange_type]


# 便捷函数
def get_okx_order_book_manager() -> OrderBookManager:
    """获取OKX订单簿管理器"""
    return get_order_book_manager(ExchangeType.OKX)


def get_gate_order_book_manager() -> OrderBookManager:
    """获取Gate.io订单簿管理器"""
    return get_order_book_manager(ExchangeType.GATE)


if __name__ == "__main__":
    # 测试代码
    async def test_order_book_manager():
        """测试订单簿管理器"""
        print("🧪 开始测试订单簿管理器...")

        # 创建OKX订单簿管理器
        manager = OrderBookManager(ExchangeType.OKX)

        # 模拟快照数据
        snapshot_data = {
            'data': [{
                'bids': [['50000', '1.5', '0', '1'], ['49999', '2.0', '0', '1']],
                'asks': [['50001', '1.2', '0', '1'], ['50002', '1.8', '0', '1']],
                'ts': str(int(time.time() * 1000)),
                'seqId': '1000'
            }]
        }

        # 初始化订单簿
        success = await manager.initialize_symbol('BTC-USDT', snapshot_data)
        print(f"初始化结果: {success}")

        if success:
            # 🛠️ BUG修复 #11: 正确调用异步方法
            # 获取订单簿
            order_book = await manager.get_order_book('BTC-USDT')
            if order_book:
                print(f"最佳买价: {order_book.get_best_bid().price}")
                print(f"最佳卖价: {order_book.get_best_ask().price}")
                print(f"价差: {order_book.get_spread()}")

            # 模拟更新
            update_data = {
                'data': [{
                    'bids': [['50000', '2.0', '0', '1']],  # 更新买单数量
                    'asks': [['50001', '0', '0', '0']],    # 删除卖单
                    'ts': str(int(time.time() * 1000)),
                    'seqId': '1001'
                }]
            }

            update_success = await manager.process_update('BTC-USDT', update_data)
            print(f"更新结果: {update_success}")

            # 🛠️ BUG修复 #11: 正确调用异步方法
            # 获取统计信息
            stats = await manager.get_stats()
            print(f"统计信息: {stats}")

    # 运行测试
    asyncio.run(test_order_book_manager())
