#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 关键修复验证测试
验证DataFrame布尔值判断和订单金额计算的修复效果
"""

import sys
import os
import traceback

def test_import_wmzc():
    """测试WMZC模块导入"""
    print("🔍 测试WMZC模块导入...")
    
    try:
        import WMZC
        print("✅ WMZC模块导入成功")
        return True
    except Exception as e:
        print(f"❌ WMZC模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_safe_dataframe_check():
    """测试_safe_dataframe_check函数修复"""
    print("\n🔍 测试_safe_dataframe_check函数修复...")
    
    try:
        import WMZC
        import pandas as pd
        
        # 测试空DataFrame
        empty_df = pd.DataFrame()
        result = WMZC._safe_dataframe_check(empty_df)
        print(f"✅ 空DataFrame检查: {result} (应该为False)")
        assert result == False, "空DataFrame应该返回False"
        
        # 测试有数据的DataFrame
        data_df = pd.DataFrame({'close': [100, 101, 102]})
        result = WMZC._safe_dataframe_check(data_df)
        print(f"✅ 有数据DataFrame检查: {result} (应该为True)")
        assert result == True, "有数据DataFrame应该返回True"
        
        # 测试None
        result = WMZC._safe_dataframe_check(None)
        print(f"✅ None检查: {result} (应该为False)")
        assert result == False, "None应该返回False"
        
        print("✅ _safe_dataframe_check函数测试通过")
        return True
        
    except Exception as e:
        print(f"❌ _safe_dataframe_check函数测试失败: {e}")
        traceback.print_exc()
        return False

def test_technical_indicators():
    """测试技术指标计算不会产生DataFrame布尔值错误"""
    print("\n🔍 测试技术指标计算...")
    
    try:
        import WMZC
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'close': [100 + i + np.sin(i/10) * 5 for i in range(50)],
            'high': [105 + i + np.sin(i/10) * 5 for i in range(50)],
            'low': [95 + i + np.sin(i/10) * 5 for i in range(50)],
            'open': [98 + i + np.sin(i/10) * 5 for i in range(50)],
            'volume': [1000 + i * 10 for i in range(50)]
        })
        
        # 测试MACD计算
        try:
            macd_result = WMZC.calculate_macd(test_data)
            if WMZC._safe_dataframe_check(macd_result):
                print(f"✅ MACD计算成功，数据行数: {len(macd_result)}")
            else:
                print("⚠️ MACD计算返回空结果")
        except Exception as e:
            print(f"❌ MACD计算失败: {e}")
            return False
        
        # 测试RSI计算
        try:
            rsi_result = WMZC.calculate_rsi(test_data)
            if WMZC._safe_dataframe_check(rsi_result):
                print(f"✅ RSI计算成功，数据行数: {len(rsi_result)}")
            else:
                print("⚠️ RSI计算返回空结果")
        except Exception as e:
            print(f"❌ RSI计算失败: {e}")
            return False
        
        # 测试KDJ计算
        try:
            kdj_result = WMZC.calculate_kdj(test_data)
            if WMZC._safe_dataframe_check(kdj_result):
                print(f"✅ KDJ计算成功，数据行数: {len(kdj_result)}")
            else:
                print("⚠️ KDJ计算返回空结果")
        except Exception as e:
            print(f"❌ KDJ计算失败: {e}")
            return False
        
        print("✅ 技术指标计算测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 技术指标计算测试失败: {e}")
        traceback.print_exc()
        return False

def test_order_amount_logic():
    """测试订单金额调整逻辑"""
    print("\n🔍 测试订单金额调整逻辑...")
    
    try:
        # 模拟订单金额调整逻辑
        def smart_margin_check_and_adjust(amount, available_balance):
            """模拟修复后的金额调整逻辑"""
            
            # 区分API错误和真实余额不足
            if available_balance <= 0:
                return 0.0  # 余额不足时返回0
            
            # 计算最大可用金额（保留2%作为手续费缓冲）
            max_usable = available_balance * 0.98
            
            # 如果请求金额超过可用余额，调整为最大可用金额
            if amount > max_usable:
                # 确保调整后的金额有意义
                if max_usable < 1.0:  # 提高最小金额要求到1 USDT
                    return 0.0  # 金额过低时返回0
                return max_usable
            
            # 检查最小下单金额（提高到1.0 USDT）
            min_order_amount = 1.0
            if amount < min_order_amount:
                # 确保有足够余额支持最小金额
                if available_balance >= min_order_amount * 1.02:  # 包含手续费缓冲
                    return min_order_amount
                else:
                    return 0.0
            
            return amount
        
        # 测试用例
        test_cases = [
            (10.0, 100.0, 10.0, "正常金额"),
            (150.0, 100.0, 98.0, "超额金额"),
            (0.5, 100.0, 1.0, "过小金额"),
            (10.0, 0.0, 0.0, "零余额"),
            (10.0, 0.5, 0.0, "余额不足最小金额"),
        ]
        
        for amount, balance, expected, description in test_cases:
            result = smart_margin_check_and_adjust(amount, balance)
            print(f"✅ {description}: 请求{amount}, 余额{balance} -> 结果{result} (期望{expected})")
            
            # 允许一定的浮点数误差
            if abs(result - expected) > 0.1:
                print(f"⚠️ 结果不符合预期: {result} != {expected}")
        
        print("✅ 订单金额调整逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 订单金额调整逻辑测试失败: {e}")
        traceback.print_exc()
        return False

def test_syntax_errors():
    """测试语法错误修复"""
    print("\n🔍 测试语法错误修复...")
    
    try:
        # 尝试编译WMZC.py文件
        with open('WMZC.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 尝试编译代码
        compile(code, 'WMZC.py', 'exec')
        print("✅ WMZC.py语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        print(f"   位置: {e.offset}")
        print(f"   文本: {e.text}")
        return False
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 WMZC关键修复验证测试")
    print("=" * 60)
    
    tests = [
        ("语法错误修复", test_syntax_errors),
        ("模块导入", test_import_wmzc),
        ("DataFrame安全检查", test_safe_dataframe_check),
        ("技术指标计算", test_technical_indicators),
        ("订单金额逻辑", test_order_amount_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！关键修复成功")
        print("💡 现在可以尝试运行: python run_trading_system.py")
    elif passed >= total - 1:
        print("⚠️ 大部分测试通过，修复基本成功")
        print("💡 可以尝试运行系统，但可能仍有小问题")
    else:
        print("❌ 多项测试失败，需要进一步检查")
        print("💡 建议先解决测试失败的问题")
    
    print("=" * 60)
    return passed >= total - 1

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
