#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 WMZC现代化GUI启动脚本
用于启动和测试现代化GUI界面
"""

import sys
import os
import traceback

def check_dependencies():
    """检查依赖库"""
    required_packages = [
        ('customtkinter', 'CustomTkinter'),
        ('tkinter', 'tkinter'),
        ('threading', 'threading'),
        ('json', 'json'),
        ('time', 'time'),
        ('datetime', 'datetime')
    ]
    
    missing_packages = []
    
    for package, display_name in required_packages:
        try:
            __import__(package)
            print(f"✅ {display_name} - 已安装")
        except ImportError:
            missing_packages.append(display_name)
            print(f"❌ {display_name} - 未安装")
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        if 'CustomTkinter' in missing_packages:
            print("pip install customtkinter")
        return False
    
    print("\n✅ 所有依赖库检查通过")
    return True

def run_modern_gui():
    """运行现代化GUI"""
    try:
        print("🚀 启动WMZC现代化GUI...")
        
        # 导入现代GUI模块
        from modern_wmzc_gui import ModernWMZCGUI, WMZCModernGUIAdapter
        
        # 检查是否有原WMZC实例
        original_wmzc = None
        try:
            # 尝试导入原WMZC系统
            import WMZC
            print("📦 检测到原WMZC系统")
            # 这里可以创建原WMZC实例的连接
            # original_wmzc = WMZC.TradingApp()
        except ImportError:
            print("📦 未检测到原WMZC系统，使用独立模式")
        
        # 创建GUI适配器
        if original_wmzc:
            app = WMZCModernGUIAdapter(original_wmzc)
        else:
            # 独立运行现代GUI
            app = ModernWMZCGUI()
        
        print("🎨 现代化GUI界面已启动")
        print("💡 功能说明:")
        print("   - 左侧导航栏：点击切换不同功能页面")
        print("   - 右上角主题按钮：切换暗色/亮色主题")
        print("   - 主配置页面：配置API密钥和交易参数")
        print("   - 策略赶集页面：选择和管理交易策略")
        print("   - 交易记录页面：查看交易历史和统计")
        print("   - 新闻资讯页面：获取市场新闻和情绪分析")
        print("   - 日志控制台：查看系统运行日志")
        print("\n🎉 享受现代化的交易体验！")
        
        # 运行GUI
        app.run()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n📋 错误详情:")
        traceback.print_exc()
        return False
    
    return True

def show_help():
    """显示帮助信息"""
    help_text = """
🚀 WMZC现代化GUI启动脚本

用法:
    python run_modern_gui.py [选项]

选项:
    --help, -h      显示此帮助信息
    --check, -c     仅检查依赖库
    --version, -v   显示版本信息

功能特性:
    ✨ 现代化界面设计
    🌙 暗色/亮色主题切换
    📱 响应式布局
    🎯 20个功能页面
    📊 实时数据显示
    🔧 完整配置管理

系统要求:
    - Python 3.7+
    - CustomTkinter
    - tkinter (通常随Python安装)

安装依赖:
    pip install customtkinter

示例:
    python run_modern_gui.py          # 启动GUI
    python run_modern_gui.py --check  # 检查依赖
    """
    print(help_text)

def show_version():
    """显示版本信息"""
    version_info = """
🚀 WMZC现代化GUI
版本: 2.0.0
构建日期: 2025-01-22
作者: WMZC开发团队

更新内容:
    ✨ 全新现代化界面设计
    🌙 支持暗色/亮色主题
    📱 响应式布局适配
    🎯 20个功能页面重新设计
    📊 实时数据可视化
    🔧 完整的配置管理系统
    🚀 性能优化和用户体验提升

技术栈:
    - CustomTkinter (现代UI框架)
    - Python 3.7+
    - tkinter (基础GUI库)
    """
    print(version_info)

def main():
    """主函数"""
    # 解析命令行参数
    args = sys.argv[1:]
    
    if '--help' in args or '-h' in args:
        show_help()
        return
    
    if '--version' in args or '-v' in args:
        show_version()
        return
    
    if '--check' in args or '-c' in args:
        print("🔍 检查依赖库...")
        if check_dependencies():
            print("\n✅ 依赖检查完成，可以启动GUI")
        else:
            print("\n❌ 依赖检查失败，请安装缺少的包")
        return
    
    # 默认启动GUI
    print("=" * 60)
    print("🚀 WMZC量化交易系统 - 现代化GUI")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，无法启动GUI")
        print("💡 请运行 'python run_modern_gui.py --help' 查看帮助")
        return
    
    # 启动GUI
    success = run_modern_gui()
    
    if success:
        print("\n👋 感谢使用WMZC现代化GUI！")
    else:
        print("\n❌ GUI启动失败")
        print("💡 请检查错误信息或运行 'python run_modern_gui.py --help' 查看帮助")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        traceback.print_exc()
