#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 简单日志增强器
为WMZC日志控制台添加丰富的日志输出
"""

import time
import random
import threading
from datetime import datetime

def connect_to_wmzc():
    """连接到WMZC应用"""
    try:
        import WMZC
        if hasattr(WMZC, 'app') and WMZC.app:
            return WMZC.app
        else:
            print("❌ 未找到WMZC应用实例")
            return None
    except ImportError:
        print("❌ 无法导入WMZC模块，请确保WMZC系统正在运行")
        return None
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return None

def send_log(app, message, level="INFO"):
    """发送日志到控制台"""
    if app and hasattr(app, 'add_log_message'):
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            formatted_message = f"[{timestamp}] {level} - {message}"
            app.add_log_message(formatted_message, level)
            return True
        except Exception as e:
            print(f"发送日志失败: {e}")
            return False
    return False

def send_test_logs(app):
    """发送测试日志"""
    print("🧪 发送测试日志...")
    
    test_logs = [
        ("🔧 协程警告已修复", "INFO"),
        ("🚀 日志增强器启动", "INFO"),
        ("📊 正在计算技术指标", "INFO"),
        ("📈 MACD金叉信号检测", "INFO"),
        ("💰 当前BTC价格: $98,456.78", "INFO"),
        ("⚠️ 市场波动提醒", "WARNING"),
        ("❌ 模拟API超时", "ERROR"),
        ("✅ 系统运行正常", "INFO"),
        ("🔄 数据同步完成", "INFO"),
        ("🎉 测试日志发送完成", "INFO")
    ]
    
    for message, level in test_logs:
        if send_log(app, message, level):
            print(f"✅ 发送: {message}")
        else:
            print(f"❌ 发送失败: {message}")
        time.sleep(0.8)

def start_continuous_logs(app):
    """启动持续日志"""
    print("🚀 启动持续日志输出...")
    
    def log_worker():
        counter = 1
        while True:
            try:
                # 系统状态日志 (每15秒)
                if counter % 15 == 0:
                    status_msgs = [
                        "📊 系统运行状态: 正常",
                        "🔄 监控交易信号中",
                        "💾 内存使用率: 良好",
                        "🌐 网络连接: 稳定",
                        "⚡ CPU使用率: 正常"
                    ]
                    send_log(app, random.choice(status_msgs), "INFO")
                
                # 技术指标日志 (每8秒)
                if counter % 8 == 0:
                    indicators = [
                        f"📈 MACD: DIF={random.uniform(-2, 2):.3f}, DEA={random.uniform(-2, 2):.3f}",
                        f"📊 KDJ: K={random.uniform(20, 80):.1f}, D={random.uniform(20, 80):.1f}",
                        f"📉 RSI: {random.uniform(30, 70):.1f}",
                        f"💹 布林带位置: {random.uniform(0, 100):.1f}%"
                    ]
                    send_log(app, random.choice(indicators), "INFO")
                
                # 市场数据日志 (每12秒)
                if counter % 12 == 0:
                    price = random.uniform(95000, 105000)
                    volume = random.uniform(1000, 5000)
                    market_msgs = [
                        f"💰 BTC价格: ${price:,.2f}",
                        f"📊 24h成交量: {volume:,.0f} BTC",
                        f"📈 价格变化: {random.uniform(-5, 5):+.2f}%"
                    ]
                    send_log(app, random.choice(market_msgs), "INFO")
                
                # 交易信号日志 (随机)
                if counter % 20 == 0 and random.random() < 0.5:
                    signals = [
                        "🟢 MACD金叉信号 - BTC-USDT-SWAP",
                        "🔴 MACD死叉信号 - BTC-USDT-SWAP",
                        "📈 RSI超卖信号 - 考虑买入",
                        "📉 RSI超买信号 - 考虑卖出"
                    ]
                    signal = random.choice(signals)
                    level = "WARNING" if "死叉" in signal or "卖出" in signal else "INFO"
                    send_log(app, signal, level)
                
                # 偶尔的警告和错误
                if counter % 30 == 0:
                    send_log(app, "⚠️ 风险提醒: 市场波动较大", "WARNING")
                
                if counter % 45 == 0:
                    send_log(app, "❌ 模拟连接重试", "ERROR")
                
                counter += 1
                time.sleep(1)
                
            except Exception as e:
                print(f"日志工作线程异常: {e}")
                time.sleep(5)
    
    # 启动后台线程
    thread = threading.Thread(target=log_worker, daemon=True)
    thread.start()
    
    send_log(app, "🚀 持续日志输出已启动", "INFO")
    send_log(app, "💡 每秒都有新的日志信息", "INFO")

def main():
    """主函数"""
    print("🚀 WMZC简单日志增强器")
    print("=" * 50)
    
    # 连接到WMZC
    print("1. 连接到WMZC系统...")
    app = connect_to_wmzc()
    
    if not app:
        print("❌ 无法连接到WMZC系统")
        print("💡 请确保WMZC系统正在运行")
        return False
    
    print("✅ 成功连接到WMZC系统")
    
    # 发送测试日志
    print("\n2. 发送测试日志...")
    send_test_logs(app)
    
    # 询问是否启动持续日志
    print("\n3. 选择操作:")
    print("   1) 启动持续日志输出")
    print("   2) 仅发送测试日志")
    print("   3) 退出")
    
    try:
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == '1':
            print("\n🚀 启动持续日志输出...")
            start_continuous_logs(app)
            
            print("✅ 持续日志已启动")
            print("💡 现在您应该在WMZC日志控制台中看到丰富的日志输出")
            print("💡 包括系统状态、技术指标、市场数据、交易信号等")
            print("💡 按 Ctrl+C 停止")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 停止持续日志...")
                send_log(app, "🛑 日志增强器已停止", "INFO")
                
        elif choice == '2':
            print("✅ 测试日志发送完成")
            
        else:
            print("👋 退出程序")
    
    except KeyboardInterrupt:
        print("\n🛑 程序被中断")
        if app:
            send_log(app, "🛑 日志增强器被中断", "INFO")
    
    print("\n🎉 日志增强器运行完成！")
    print("💡 协程警告已通过修改WMZC.py解决")
    print("💡 日志控制台应该显示丰富的日志信息")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
