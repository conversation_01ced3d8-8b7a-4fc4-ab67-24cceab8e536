#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC系统全方位BUG检测器
基于专业检测提示词进行深度扫描
"""

import ast
import re
import os
import json
import time
from typing import List, Dict, Any

class WMZCBugDetector:
    """WMZC系统BUG检测器"""
    
    def __init__(self):
        self.bugs_found = []
        self.files_to_check = [
            'WMZC.py',
            'Global_Position_Controller.py', 
            'batch_order_manager.py',
            'exchange_rate_limiter.py',
            'smart_retry_handler.py',
            'order_book_manager.py',
            'optimization_config_parameters.py'
        ]
        self.config_files = [
            'wmzc_config.json',
            'trading_config.json',
            'user_settings.json'
        ]
    
    def add_bug(self, bug_type: str, severity: str, description: str, file_name: str, line_num: int = None):
        """添加发现的BUG"""
        self.bugs_found.append({
            'type': bug_type,
            'severity': severity,
            'description': description,
            'file': file_name,
            'line': line_num,
            'timestamp': time.time()
        })
    
    def check_syntax_and_structure(self):
        """检测语法与结构完整性"""
        print("📋 阶段1: 语法与结构完整性检测")
        
        for file_name in self.files_to_check:
            if not os.path.exists(file_name):
                continue
                
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 语法检查
                try:
                    ast.parse(content)
                    print(f"✅ {file_name} 语法检查通过")
                except SyntaxError as e:
                    self.add_bug(
                        "语法错误", "🔴 严重",
                        f"语法错误: {e.msg}",
                        file_name, e.lineno
                    )
                
                # 检查异步编程违规
                self._check_async_violations(content, file_name)
                
            except Exception as e:
                self.add_bug(
                    "文件读取错误", "🟠 重要",
                    f"无法读取文件: {e}",
                    file_name
                )
    
    def _check_async_violations(self, content: str, file_name: str):
        """检查异步编程违规"""
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # 检查time.sleep使用
            if 'time.sleep(' in line and 'await asyncio.sleep(' not in line:
                self.add_bug(
                    "异步编程违规", "🔴 严重",
                    "使用time.sleep()阻塞操作，应使用await asyncio.sleep()",
                    file_name, i
                )
            
            # 检查缺少await的异步调用
            if re.search(r'(?<!await\s)(?<!await\s\s)(?<!await\s\s\s)\w+\.\w+\s*\([^)]*\)\s*(?=\s|$)', line):
                if 'async def' in line or 'await' in line:
                    continue
                # 进一步检查是否是异步方法调用
                if any(keyword in line for keyword in ['_async', 'async_', 'fetch_', 'get_', 'post_']):
                    self.add_bug(
                        "异步调用错误", "🟡 中等",
                        "可能缺少await的异步方法调用",
                        file_name, i
                    )
    
    def check_security_issues(self):
        """检测安全问题"""
        print("🛡️ 阶段2: 安全性检测")
        
        # 检查配置文件中的敏感信息
        for config_file in self.config_files:
            if not os.path.exists(config_file):
                continue
                
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
                
                # 检查API密钥
                api_patterns = [
                    r'"[a-zA-Z_]*[aA][pP][iI][_]*[kK][eE][yY]":\s*"[^"]{10,}"',
                    r'"[a-zA-Z_]*[sS][eE][cC][rR][eE][tT]":\s*"[^"]{10,}"',
                    r'"[a-zA-Z_]*[pP][aA][sS][sS][wW][oO][rR][dD]":\s*"[^"]{5,}"'
                ]
                
                for pattern in api_patterns:
                    matches = re.findall(pattern, config_content)
                    if matches:
                        self.add_bug(
                            "安全漏洞", "🔴 严重",
                            f"配置文件中发现明文敏感信息: {len(matches)}处",
                            config_file
                        )
                
            except Exception as e:
                print(f"⚠️ 无法检查配置文件 {config_file}: {e}")
    
    def check_business_logic(self):
        """检测业务逻辑正确性"""
        print("📊 阶段3: 业务逻辑检测")
        
        if os.path.exists('WMZC.py'):
            with open('WMZC.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查除零错误防护
            division_patterns = [
                r'\/\s*[a-zA-Z_]\w*(?!\s*[!=<>])',
                r'\/\s*\([^)]+\)(?!\s*[!=<>])'
            ]
            
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                for pattern in division_patterns:
                    if re.search(pattern, line) and 'if' not in line and '!= 0' not in line:
                        self.add_bug(
                            "除零风险", "🟡 中等",
                            "可能存在除零错误风险",
                            'WMZC.py', i
                        )
    
    def check_resource_management(self):
        """检测资源管理"""
        print("🔄 阶段4: 资源管理检测")
        
        for file_name in self.files_to_check:
            if not os.path.exists(file_name):
                continue
                
            with open(file_name, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                # 检查未关闭的文件操作
                if 'open(' in line and 'with' not in line and '.close()' not in content[content.find(line):content.find(line)+200]:
                    self.add_bug(
                        "资源泄漏", "🟠 重要",
                        "文件打开后可能未正确关闭",
                        file_name, i
                    )
    
    def run_comprehensive_detection(self):
        """运行全面检测"""
        print("🚀 开始WMZC系统全方位BUG检测...")
        print(f"检测文件数量: {len(self.files_to_check)}")
        print(f"配置文件数量: {len(self.config_files)}")
        
        start_time = time.time()
        
        # 执行各阶段检测
        self.check_syntax_and_structure()
        self.check_security_issues()
        self.check_business_logic()
        self.check_resource_management()
        
        end_time = time.time()
        
        # 生成检测报告
        self.generate_report(end_time - start_time)
    
    def generate_report(self, detection_time: float):
        """生成检测报告"""
        print(f"\n📊 检测完成，耗时: {detection_time:.2f}秒")
        print(f"发现BUG总数: {len(self.bugs_found)}")
        
        if not self.bugs_found:
            print("🎉 恭喜！未发现任何BUG，系统状态良好！")
            return
        
        # 按严重程度分类
        severity_counts = {}
        for bug in self.bugs_found:
            severity = bug['severity']
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        print("\n📋 BUG分类统计:")
        for severity, count in severity_counts.items():
            print(f"  {severity}: {count}个")
        
        print("\n🔍 详细BUG列表:")
        for i, bug in enumerate(self.bugs_found, 1):
            line_info = f" (行{bug['line']})" if bug['line'] else ""
            print(f"{i}. {bug['severity']} {bug['type']}")
            print(f"   文件: {bug['file']}{line_info}")
            print(f"   描述: {bug['description']}")
            print()
        
        # 保存报告到文件
        report_data = {
            'detection_time': detection_time,
            'total_bugs': len(self.bugs_found),
            'severity_counts': severity_counts,
            'bugs': self.bugs_found,
            'timestamp': time.time()
        }
        
        with open('bug_detection_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print("📄 详细报告已保存到: bug_detection_report.json")

if __name__ == "__main__":
    detector = WMZCBugDetector()
    detector.run_comprehensive_detection()
