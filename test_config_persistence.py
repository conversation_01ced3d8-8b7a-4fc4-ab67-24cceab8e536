#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 WMZC配置持久化功能测试脚本
测试API密钥配置保存和加载功能
"""

import os
import json
import time

def test_config_save_and_load():
    """测试配置保存和加载功能"""
    print("🧪 测试配置持久化功能")
    print("=" * 50)
    
    # 测试数据
    test_api_config = {
        "API_KEY": "test_api_key_12345",
        "API_SECRET": "test_api_secret_67890", 
        "PASSPHRASE": "test_passphrase_abc"
    }
    
    config_files = ['trading_config.json', 'wmzc_config.json']
    
    print("📝 步骤1: 保存测试API配置...")
    
    for config_file in config_files:
        try:
            # 读取现有配置
            existing_config = {}
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
            
            # 更新API配置
            if config_file == 'wmzc_config.json':
                existing_config['okx_api_key'] = test_api_config['API_KEY']
                existing_config['okx_secret_key'] = test_api_config['API_SECRET']
                existing_config['okx_passphrase'] = test_api_config['PASSPHRASE']
            else:
                existing_config.update(test_api_config)
                existing_config['OKX_API_KEY'] = test_api_config['API_KEY']
                existing_config['OKX_SECRET_KEY'] = test_api_config['API_SECRET']
                existing_config['OKX_PASSPHRASE'] = test_api_config['PASSPHRASE']
            
            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, indent=2, ensure_ascii=False)
            
            print(f"  ✅ {config_file}: 测试配置已保存")
            
        except Exception as e:
            print(f"  ❌ {config_file}: 保存失败 - {e}")
            return False
    
    print("\n⏳ 步骤2: 等待1秒模拟系统重启...")
    time.sleep(1)
    
    print("\n📥 步骤3: 重新加载配置验证持久化...")
    
    success = True
    
    for config_file in config_files:
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
            
            # 验证API配置
            if config_file == 'wmzc_config.json':
                api_key = loaded_config.get('okx_api_key', '')
                api_secret = loaded_config.get('okx_secret_key', '')
                passphrase = loaded_config.get('okx_passphrase', '')
            else:
                api_key = loaded_config.get('API_KEY', '')
                api_secret = loaded_config.get('API_SECRET', '')
                passphrase = loaded_config.get('PASSPHRASE', '')
            
            # 检查是否正确保存
            if (api_key == test_api_config['API_KEY'] and 
                api_secret == test_api_config['API_SECRET'] and 
                passphrase == test_api_config['PASSPHRASE']):
                print(f"  ✅ {config_file}: 配置持久化成功")
            else:
                print(f"  ❌ {config_file}: 配置持久化失败")
                print(f"    期望: {test_api_config}")
                print(f"    实际: API_KEY={api_key}, API_SECRET={api_secret}, PASSPHRASE={passphrase}")
                success = False
                
        except Exception as e:
            print(f"  ❌ {config_file}: 加载失败 - {e}")
            success = False
    
    print("\n🧹 步骤4: 清理测试数据...")
    
    # 清理测试数据，恢复空配置
    for config_file in config_files:
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 清空API配置
            if config_file == 'wmzc_config.json':
                config['okx_api_key'] = ''
                config['okx_secret_key'] = ''
                config['okx_passphrase'] = ''
            else:
                config['API_KEY'] = ''
                config['API_SECRET'] = ''
                config['PASSPHRASE'] = ''
                config['OKX_API_KEY'] = ''
                config['OKX_SECRET_KEY'] = ''
                config['OKX_PASSPHRASE'] = ''
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"  ✅ {config_file}: 测试数据已清理")
            
        except Exception as e:
            print(f"  ⚠️ {config_file}: 清理失败 - {e}")
    
    return success

def test_wmzc_config_loading():
    """测试WMZC系统配置加载功能"""
    print("\n🔧 测试WMZC系统配置加载...")
    
    try:
        # 尝试导入WMZC模块
        import sys
        sys.path.insert(0, '.')
        
        # 检查配置加载函数是否存在
        print("  📦 检查WMZC模块...")
        
        # 这里我们不实际导入WMZC，因为它可能有很多依赖
        # 只检查配置文件是否符合预期格式
        
        config_files = ['trading_config.json', 'wmzc_config.json']
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 检查必要的字段是否存在
                    required_fields = []
                    if config_file == 'trading_config.json':
                        required_fields = ['API_KEY', 'API_SECRET', 'PASSPHRASE', 'SYMBOL', 'TIMEFRAME']
                    elif config_file == 'wmzc_config.json':
                        required_fields = ['okx_api_key', 'okx_secret_key', 'okx_passphrase']
                    
                    missing_fields = [field for field in required_fields if field not in config]
                    
                    if missing_fields:
                        print(f"  ⚠️ {config_file}: 缺少字段 {missing_fields}")
                    else:
                        print(f"  ✅ {config_file}: 结构完整")
                        
                except json.JSONDecodeError as e:
                    print(f"  ❌ {config_file}: JSON格式错误 - {e}")
                except Exception as e:
                    print(f"  ❌ {config_file}: 检查失败 - {e}")
            else:
                print(f"  ❌ {config_file}: 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 WMZC配置持久化功能测试")
    print("=" * 50)
    
    # 测试1: 配置保存和加载
    test1_success = test_config_save_and_load()
    
    # 测试2: WMZC系统配置加载
    test2_success = test_wmzc_config_loading()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  配置持久化测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"  系统配置加载测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过！配置持久化功能正常")
        print("\n💡 使用建议:")
        print("  1. 在WMZC主配置页面填写真实的API密钥")
        print("  2. 点击保存配置按钮")
        print("  3. 重启WMZC系统验证配置是否保留")
        print("  4. 如果仍有问题，请检查文件权限")
    else:
        print("\n⚠️ 部分测试失败，配置持久化可能仍有问题")
        print("💡 建议:")
        print("  1. 检查文件写入权限")
        print("  2. 确保配置文件格式正确")
        print("  3. 重新运行修复脚本")
    
    return test1_success and test2_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
