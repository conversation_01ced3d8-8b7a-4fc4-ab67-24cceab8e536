#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易系统启动脚本
简化的启动入口，用于运行WMZC.py中的交易系统
"""

import sys
import os
import traceback

def main():
    """主启动函数"""
    print("=" * 60)
    print("🚀 启动量化交易系统...")
    print("=" * 60)

    try:
        # 检查WMZC.py文件是否存在
        if not os.path.exists('WMZC.py'):
            print("❌ 错误：找不到WMZC.py文件")
            print("请确保WMZC.py文件在当前目录中")
            return False

        print("📁 找到WMZC.py文件")

        # 设置环境变量，避免一些常见问题
        os.environ['PYTHONPATH'] = os.getcwd()

        # 导入并启动交易系统
        print("📦 导入交易系统模块...")
        try:
            # 清理可能的缓存
            if 'WMZC' in sys.modules:
                del sys.modules['WMZC']

            import WMZC
            print("✅ 模块导入成功")
        except ImportError as e:
            print(f"❌ 模块导入失败: {e}")
            print("请检查依赖包是否已安装")
            print("💡 可以运行: python install_dependencies.py")
            return False
        except Exception as e:
            print(f"❌ 模块导入异常: {e}")
            print("详细错误信息:")
            traceback.print_exc()

            # 尝试提供解决建议
            error_str = str(e).lower()
            if 'tkinter' in error_str:
                print("\n💡 解决建议: tkinter问题")
                print("   - Windows: 重新安装Python，确保勾选tkinter")
                print("   - Linux: sudo apt-get install python3-tk")
                print("   - macOS: brew install python-tk")
            elif 'pandas' in error_str or 'numpy' in error_str:
                print("\n💡 解决建议: 缺少数据处理库")
                print("   运行: pip install pandas numpy")

            return False

        # 启动系统
        print("🚀 启动交易系统...")
        try:
            # 优先尝试使用main函数
            if hasattr(WMZC, 'main') and callable(getattr(WMZC, 'main')):
                print("📋 调用main函数启动系统...")
                WMZC.main()
            elif hasattr(WMZC, 'start_system') and callable(getattr(WMZC, 'start_system')):
                print("📋 调用start_system函数启动系统...")
                WMZC.start_system()
            elif hasattr(WMZC, 'TradingApp'):
                print("📋 直接创建GUI应用...")
                app = WMZC.TradingApp()
                print("✅ GUI创建成功，开始运行...")
                app.mainloop()
            else:
                print("❌ 未找到合适的启动方法")
                print("可用的属性:")
                attrs = [attr for attr in dir(WMZC) if not attr.startswith('_')]
                for attr in attrs[:10]:  # 只显示前10个
                    print(f"   - {attr}")
                if len(attrs) > 10:
                    print(f"   ... 还有 {len(attrs) - 10} 个属性")
                return False

        except KeyboardInterrupt:
            print("\n👋 用户中断，系统退出")
            return True
        except Exception as e:
            print(f"❌ 系统启动失败: {e}")
            print("详细错误信息:")
            traceback.print_exc()

            # 尝试提供解决建议
            error_str = str(e).lower()
            if 'config' in error_str:
                print("\n💡 解决建议: 配置问题")
                print("   - 检查配置文件是否存在")
                print("   - 确保API密钥格式正确")
            elif 'permission' in error_str:
                print("\n💡 解决建议: 权限问题")
                print("   - 以管理员身份运行")
                print("   - 检查文件权限")

            return False

        print("✅ 系统正常退出")
        return True

    except Exception as e:
        print(f"💥 启动脚本异常: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

def check_dependencies():
    """检查必要的依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'tkinter',
        'pandas', 
        'numpy',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'pandas':
                import pandas
            elif package == 'numpy':
                import numpy
            elif package == 'requests':
                import requests
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有必要依赖包已安装")
    return True

def show_help():
    """显示帮助信息"""
    print("=" * 60)
    print("📖 量化交易系统使用说明")
    print("=" * 60)
    print()
    print("🚀 启动方式:")
    print("  python run_trading_system.py")
    print()
    print("🔧 参数选项:")
    print("  --help, -h     显示此帮助信息")
    print("  --check-deps   检查依赖包")
    print()
    print("📋 系统要求:")
    print("  - Python 3.7+")
    print("  - tkinter (GUI界面)")
    print("  - pandas (数据处理)")
    print("  - numpy (数值计算)")
    print("  - requests (网络请求)")
    print()
    print("📁 文件要求:")
    print("  - WMZC.py (主程序文件)")
    print()
    print("💡 使用提示:")
    print("  1. 确保所有依赖包已安装")
    print("  2. 确保WMZC.py文件在当前目录")
    print("  3. 运行此脚本启动系统")
    print("  4. 在GUI界面中配置API密钥")
    print("  5. 开始量化交易")
    print()

if __name__ == "__main__":
    # 处理命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['--help', '-h']:
            show_help()
            sys.exit(0)
        elif arg == '--check-deps':
            success = check_dependencies()
            sys.exit(0 if success else 1)
        else:
            print(f"❌ 未知参数: {arg}")
            print("使用 --help 查看帮助信息")
            sys.exit(1)
    
    # 检查依赖包
    if not check_dependencies():
        print("\n❌ 依赖包检查失败，请先安装缺少的包")
        sys.exit(1)
    
    # 启动系统
    success = main()
    sys.exit(0 if success else 1)
