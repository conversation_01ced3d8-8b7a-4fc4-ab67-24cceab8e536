#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
用于验证WMZC交易系统是否可以正常导入和基本功能
"""

import sys
import os
import traceback
import time

def test_python_version():
    """测试Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    
    print("✅ Python版本符合要求")
    return True

def test_basic_imports():
    """测试基础库导入"""
    print("\n🔍 测试基础库导入...")
    
    basic_libs = [
        ('os', 'os'),
        ('sys', 'sys'),
        ('json', 'json'),
        ('time', 'time'),
        ('datetime', 'datetime'),
        ('threading', 'threading'),
        ('tkinter', 'tkinter')
    ]
    
    success_count = 0
    for lib_name, import_name in basic_libs:
        try:
            __import__(import_name)
            print(f"✅ {lib_name} - 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {lib_name} - 导入失败: {e}")
    
    print(f"📊 基础库测试结果: {success_count}/{len(basic_libs)}")
    return success_count == len(basic_libs)

def test_data_libs():
    """测试数据处理库"""
    print("\n🔍 测试数据处理库...")
    
    data_libs = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('requests', 'requests')
    ]
    
    success_count = 0
    for lib_name, import_name in data_libs:
        try:
            lib = __import__(import_name)
            if hasattr(lib, '__version__'):
                version = lib.__version__
                print(f"✅ {lib_name} v{version} - 导入成功")
            else:
                print(f"✅ {lib_name} - 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {lib_name} - 导入失败: {e}")
    
    print(f"📊 数据库测试结果: {success_count}/{len(data_libs)}")
    return success_count >= 2  # 至少需要2个库

def test_wmzc_import():
    """测试WMZC模块导入"""
    print("\n🔍 测试WMZC模块导入...")
    
    if not os.path.exists('WMZC.py'):
        print("❌ WMZC.py文件不存在")
        return False
    
    print("📁 找到WMZC.py文件")
    
    try:
        # 清理可能的缓存
        if 'WMZC' in sys.modules:
            del sys.modules['WMZC']
        
        print("📦 尝试导入WMZC模块...")
        import WMZC
        print("✅ WMZC模块导入成功")
        
        # 检查主要类和函数
        print("🔍 检查主要组件...")
        components = []
        
        if hasattr(WMZC, 'TradingApp'):
            components.append('TradingApp (GUI类)')
        if hasattr(WMZC, 'main'):
            components.append('main (主函数)')
        if hasattr(WMZC, 'start_system'):
            components.append('start_system (启动函数)')
        
        if components:
            print("✅ 找到以下组件:")
            for comp in components:
                print(f"   - {comp}")
        else:
            print("⚠️ 未找到预期的主要组件")
        
        return True
        
    except ImportError as e:
        print(f"❌ WMZC模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ WMZC模块导入异常: {e}")
        print("详细错误:")
        traceback.print_exc()
        return False

def test_gui_creation():
    """测试GUI创建（不启动）"""
    print("\n🔍 测试GUI创建...")
    
    try:
        import WMZC
        
        if not hasattr(WMZC, 'TradingApp'):
            print("⚠️ 未找到TradingApp类，跳过GUI测试")
            return True
        
        print("🎨 尝试创建GUI对象...")
        app = WMZC.TradingApp()
        print("✅ GUI对象创建成功")
        
        # 不调用mainloop，只是测试创建
        print("📋 GUI组件初始化完成")
        
        # 清理
        try:
            app.destroy()
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")
        print("详细错误:")
        traceback.print_exc()
        return False

def test_config_files():
    """测试配置文件"""
    print("\n🔍 检查配置文件...")
    
    config_files = [
        'trading_config.json',
        'wmzc_config.json',
        'config_template.json'
    ]
    
    found_configs = []
    for config_file in config_files:
        if os.path.exists(config_file):
            found_configs.append(config_file)
            print(f"✅ 找到配置文件: {config_file}")
        else:
            print(f"⚠️ 配置文件不存在: {config_file}")
    
    if found_configs:
        print(f"📊 配置文件检查: 找到 {len(found_configs)} 个配置文件")
        return True
    else:
        print("⚠️ 未找到任何配置文件，系统可能需要初始化")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("=" * 60)
    print("🚀 WMZC交易系统 - 快速测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试Python版本
    test_results.append(("Python版本", test_python_version()))
    
    # 测试基础库
    test_results.append(("基础库导入", test_basic_imports()))
    
    # 测试数据处理库
    test_results.append(("数据处理库", test_data_libs()))
    
    # 测试WMZC导入
    test_results.append(("WMZC模块导入", test_wmzc_import()))
    
    # 测试GUI创建
    test_results.append(("GUI创建测试", test_gui_creation()))
    
    # 测试配置文件
    test_results.append(("配置文件检查", test_config_files()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 项测试通过")
    
    # 给出建议
    if passed == total:
        print("🎉 所有测试通过！系统应该可以正常运行")
        print("💡 建议: 运行 python run_trading_system.py 启动系统")
    elif passed >= total - 2:
        print("⚠️ 大部分测试通过，系统基本可用")
        print("💡 建议: 检查失败的项目，然后尝试启动系统")
    else:
        print("❌ 多项测试失败，系统可能无法正常运行")
        print("💡 建议: 先解决依赖问题，运行 python install_dependencies.py")
    
    return passed >= total - 1

def main():
    """主函数"""
    try:
        success = run_comprehensive_test()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ 快速测试完成，系统状态良好")
        else:
            print("⚠️ 快速测试发现问题，请检查上述错误")
        print("=" * 60)
        
        return success
        
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        return False
    except Exception as e:
        print(f"\n💥 测试脚本异常: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
